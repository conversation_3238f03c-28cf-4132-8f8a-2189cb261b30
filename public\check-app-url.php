<?php
// 检查APP_URL配置
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>APP_URL配置检查</h1>";

echo "<h2>当前配置</h2>";
echo "<p>.env文件中的APP_URL: " . env('APP_URL') . "</p>";
echo "<p>config('app.url'): " . config('app.url') . "</p>";

echo "<h2>URL生成测试</h2>";
echo "<p>asset('test.css'): " . asset('test.css') . "</p>";
echo "<p>url('/'): " . url('/') . "</p>";
echo "<p>Storage::url('test.jpg'): " . \Storage::url('test.jpg') . "</p>";

echo "<h2>图片URL测试</h2>";
$testImagePath = 'uploads/editor/1753962532_cnEQXuR2.png';
echo "<p>测试图片路径: {$testImagePath}</p>";
echo "<p>Storage::url(): " . \Storage::url($testImagePath) . "</p>";
echo "<p>手动构建: " . config('app.url') . "/storage/{$testImagePath}</p>";

echo "<h2>实际图片测试</h2>";
$storageUrl = \Storage::url($testImagePath);
$manualUrl = config('app.url') . "/storage/{$testImagePath}";

echo "<p>Storage URL方式:</p>";
echo "<img src='{$storageUrl}' style='max-width: 200px; border: 1px solid blue;' onerror='this.style.border=\"2px solid red\"; this.alt=\"失败: {$storageUrl}\"'>";

echo "<p>手动URL方式:</p>";
echo "<img src='{$manualUrl}' style='max-width: 200px; border: 1px solid green;' onerror='this.style.border=\"2px solid red\"; this.alt=\"失败: {$manualUrl}\"'>";

echo "<h2>配置文件内容</h2>";
echo "<h3>.env文件相关行</h3>";
$envContent = file_get_contents('../.env');
$envLines = explode("\n", $envContent);
foreach ($envLines as $line) {
    if (strpos($line, 'APP_URL') !== false) {
        echo "<p><code>" . htmlspecialchars($line) . "</code></p>";
    }
}
?>
