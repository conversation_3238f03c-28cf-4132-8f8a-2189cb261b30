<?php
// 修复存储链接脚本

echo "<h1>存储链接修复工具</h1>";

$publicStoragePath = __DIR__ . '/storage';
$storageAppPublicPath = realpath(__DIR__ . '/../storage/app/public');

echo "<h2>当前状态检查</h2>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>项目</th><th>路径</th><th>状态</th><th>详情</th></tr>";

// 检查storage/app/public目录
$storageExists = is_dir($storageAppPublicPath);
echo "<tr><td>storage/app/public目录</td><td>$storageAppPublicPath</td><td>" . ($storageExists ? '✅存在' : '❌不存在') . "</td><td>" . ($storageExists ? '可写: ' . (is_writable($storageAppPublicPath) ? '是' : '否') : '') . "</td></tr>";

// 检查public/storage链接
$linkExists = file_exists($publicStoragePath);
$isLink = is_link($publicStoragePath);
$linkTarget = $isLink ? readlink($publicStoragePath) : '';
$linkCorrect = $isLink && realpath($linkTarget) === $storageAppPublicPath;

echo "<tr><td>public/storage链接</td><td>$publicStoragePath</td><td>" . ($linkExists ? '✅存在' : '❌不存在') . "</td><td>" . ($isLink ? "链接到: $linkTarget" : ($linkExists ? '是目录而非链接' : '')) . "</td></tr>";
echo "<tr><td>链接正确性</td><td>-</td><td>" . ($linkCorrect ? '✅正确' : '❌错误') . "</td><td>目标应为: $storageAppPublicPath</td></tr>";

echo "</table>";

// 检查最新上传的文件
echo "<h2>最新上传文件检查</h2>";
$recentFiles = [
    '1754151535_xWpUtXc4.png',
    '1754151542_PCXKceoz.png'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>文件名</th><th>storage/app/public中</th><th>public/storage中</th><th>可访问</th></tr>";

foreach ($recentFiles as $file) {
    $storagePath = $storageAppPublicPath . '/uploads/editor/' . $file;
    $publicPath = $publicStoragePath . '/uploads/editor/' . $file;
    $url = 'http://' . $_SERVER['HTTP_HOST'] . '/storage/uploads/editor/' . $file;
    
    $inStorage = file_exists($storagePath);
    $inPublic = file_exists($publicPath);
    
    // 测试URL可访问性
    $accessible = false;
    if ($inPublic) {
        $context = stream_context_create([
            'http' => [
                'method' => 'HEAD',
                'timeout' => 5
            ]
        ]);
        $headers = @get_headers($url, 1, $context);
        $accessible = $headers && strpos($headers[0], '200') !== false;
    }
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>" . ($inStorage ? '✅' : '❌') . "</td>";
    echo "<td>" . ($inPublic ? '✅' : '❌') . "</td>";
    echo "<td>" . ($accessible ? '✅' : '❌') . " <a href='$url' target='_blank'>测试</a></td>";
    echo "</tr>";
}

echo "</table>";

// 修复操作
if (isset($_POST['action'])) {
    echo "<h2>修复操作结果</h2>";
    
    switch ($_POST['action']) {
        case 'remove_link':
            if (file_exists($publicStoragePath)) {
                if (is_link($publicStoragePath)) {
                    $result = unlink($publicStoragePath);
                    echo "<p>" . ($result ? "✅ 成功删除旧链接" : "❌ 删除旧链接失败") . "</p>";
                } else {
                    // 如果是目录，需要递归删除
                    $result = removeDirectory($publicStoragePath);
                    echo "<p>" . ($result ? "✅ 成功删除旧目录" : "❌ 删除旧目录失败") . "</p>";
                }
            } else {
                echo "<p>ℹ️ public/storage 不存在，无需删除</p>";
            }
            break;
            
        case 'create_link':
            if (!file_exists($publicStoragePath)) {
                if (function_exists('symlink')) {
                    $result = symlink($storageAppPublicPath, $publicStoragePath);
                    echo "<p>" . ($result ? "✅ 成功创建符号链接" : "❌ 创建符号链接失败") . "</p>";
                } else {
                    echo "<p>❌ 系统不支持符号链接功能</p>";
                }
            } else {
                echo "<p>⚠️ public/storage 已存在，请先删除</p>";
            }
            break;
            
        case 'fix_permissions':
            $result = true;
            if (is_dir($storageAppPublicPath)) {
                $result &= chmod($storageAppPublicPath, 0755);
                $result &= chmodRecursive($storageAppPublicPath, 0755, 0644);
            }
            echo "<p>" . ($result ? "✅ 权限修复完成" : "❌ 权限修复失败") . "</p>";
            break;
    }
    
    echo "<p><a href='" . $_SERVER['PHP_SELF'] . "'>刷新页面查看状态</a></p>";
}

// 修复建议
echo "<h2>修复建议</h2>";
if (!$linkCorrect) {
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>需要修复存储链接</h3>";
    echo "<p>建议按以下步骤操作：</p>";
    echo "<ol>";
    echo "<li>删除现有的 public/storage（如果存在）</li>";
    echo "<li>创建新的符号链接</li>";
    echo "<li>修复权限（如果需要）</li>";
    echo "</ol>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='remove_link' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-right: 10px;'>1. 删除现有链接/目录</button>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='create_link' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-right: 10px;'>2. 创建符号链接</button>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='fix_permissions' style='background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>3. 修复权限</button>";
    echo "</form>";
    
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<p>✅ 存储链接配置正确！</p>";
    echo "</div>";
}

// 手动命令
echo "<h2>手动修复命令</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; font-family: monospace;'>";
echo "<p>如果上述自动修复不工作，可以手动执行以下命令：</p>";
echo "<pre>";
echo "# 删除现有链接（Windows）\n";
echo "rmdir /s public\\storage\n\n";
echo "# 删除现有链接（Linux/Mac）\n";
echo "rm -rf public/storage\n\n";
echo "# 创建新链接\n";
echo "php artisan storage:link\n\n";
echo "# 或者手动创建（Windows）\n";
echo "mklink /D public\\storage ..\\storage\\app\\public\n\n";
echo "# 或者手动创建（Linux/Mac）\n";
echo "ln -s ../storage/app/public public/storage\n";
echo "</pre>";
echo "</div>";

// 辅助函数
function removeDirectory($dir) {
    if (!is_dir($dir)) return false;
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        is_dir($path) ? removeDirectory($path) : unlink($path);
    }
    return rmdir($dir);
}

function chmodRecursive($path, $dirMode, $fileMode) {
    $result = true;
    if (is_dir($path)) {
        $result &= chmod($path, $dirMode);
        $files = array_diff(scandir($path), array('.', '..'));
        foreach ($files as $file) {
            $result &= chmodRecursive($path . DIRECTORY_SEPARATOR . $file, $dirMode, $fileMode);
        }
    } else {
        $result &= chmod($path, $fileMode);
    }
    return $result;
}
?>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h2 { color: #333; margin-top: 30px; }
</style>
