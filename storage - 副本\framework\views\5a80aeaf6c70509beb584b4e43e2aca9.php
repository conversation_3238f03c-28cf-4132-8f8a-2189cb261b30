<?php $__env->startSection('title', $product->meta_title ?: $product->name . ' - 睿测微智能科技'); ?>
<?php $__env->startSection('description', $product->meta_description ?: Str::limit(strip_tags($product->description), 160)); ?>
<?php $__env->startSection('keywords', $product->meta_keywords ?: '智能测试设备,' . $product->name . ',睿测微'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* 产品详情页Banner样式 - 左右分栏布局 */
.product-hero-banner {
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
}

.product-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
}

.product-text-section {
    flex: 1;
    padding: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 2;
}

.product-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    background: url('<?php echo e(asset('images/banner/product.png')); ?>') right center/contain no-repeat;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.product-name {
    font-size: 2.8rem;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.product-tagline {
    font-size: 1.4rem;
    color: #3b82f6;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.product-description {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid #3b82f6;
    color: #3b82f6;
    padding: 10px 28px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
}

/* 装饰元素 */
.product-hero-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .product-hero-banner {
        height: auto;
        min-height: 600px;
    }

    .product-banner-content {
        flex-direction: column;
    }

    .product-text-section {
        flex: none;
        padding: 40px 30px;
        text-align: center;
    }

    .product-image-section {
        flex: none;
        height: 300px;
        background-size: contain;
        background-position: center;
    }

    .product-name {
        font-size: 2.2rem;
    }

    .product-tagline {
        font-size: 1.2rem;
    }

    .cta-buttons {
        justify-content: center;
    }
}

.product-text-section {
    flex: 1;
    padding: 3rem;
    color: white;
    position: relative;
    z-index: 3;
}

.product-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.product-name {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.product-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.product-main-image {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    transition: transform 0.3s ease;
}

.product-main-image:hover {
    transform: scale(1.05);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* 产品信息卡片样式 */
.product-info-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-top: -50px;
    position: relative;
    z-index: 5;
}

.spec-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.spec-item:hover {
    background: #e3f2fd;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.spec-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.action-buttons .btn {
    border-radius: 50px;
    font-weight: 600;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border: none;
    color: white;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.btn-outline-custom {
    border: 2px solid #3b82f6;
    color: #3b82f6;
    background: transparent;
}

.btn-outline-custom:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .product-banner-content {
        flex-direction: column;
        text-align: center;
    }

    .product-text-section,
    .product-image-section {
        flex: none;
        padding: 1.5rem;
    }

    .product-name {
        font-size: 2rem;
    }

    .product-description {
        font-size: 1rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- 产品详情Banner -->
<section class="py-4">
    <div class="container">
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="product-hero-banner">
                        <div class="product-banner-content">
                            <!-- 文字内容区域 -->
                            <div class="product-text-section">
                                <!-- 面包屑导航 -->
                                <nav aria-label="breadcrumb" class="mb-3">
                                    <ol class="breadcrumb">
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo e(route('home')); ?>" class="text-decoration-none" style="color: #64748b;">
                                                <i class="fas fa-home me-1"></i>首页
                                            </a>
                                        </li>
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo e(route('products.index')); ?>" class="text-decoration-none" style="color: #64748b;">
                                                产品中心
                                            </a>
                                        </li>
                                        <li class="breadcrumb-item active" style="color: #3b82f6;" aria-current="page">
                                            <?php echo e($product->name); ?>

                                        </li>
                                    </ol>
                                </nav>

                                <h1 class="product-name">
                                    <?php echo e($product->name); ?>

                                </h1>
                                <p class="product-tagline">
                                    <?php echo e($product->category ? $product->category->name : '智能测试设备'); ?>

                                </p>
                                <p class="product-description">
                                    <?php echo e($product->description ?: '专业的智能测试设备，为您提供高精度、高可靠性的测试解决方案。'); ?>

                                </p>

                                <!-- 产品基本信息 -->
                                <div class="product-meta mb-4">
                                    <?php if($product->model): ?>
                                        <span class="badge bg-primary me-2 mb-2">
                                            <i class="fas fa-tag me-1"></i>型号：<?php echo e($product->model); ?>

                                        </span>
                                    <?php endif; ?>
                                    <?php if($product->brand): ?>
                                        <span class="badge bg-info me-2 mb-2">
                                            <i class="fas fa-industry me-1"></i>品牌：<?php echo e($product->brand); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="cta-buttons">
                                    <a href="#product-details" class="btn-primary-custom">
                                        <i class="fas fa-info-circle me-2"></i>查看详情
                                    </a>
                                    <a href="<?php echo e(route('contact')); ?>" class="btn-outline-custom">
                                        <i class="fas fa-phone me-2"></i>立即咨询
                                    </a>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="product-image-section">
                                <!-- 背景图片通过CSS设置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- 产品信息卡片 -->
<section class="py-4" id="product-details">
    <div class="container">
        <div class="product-info-card">
            <div class="row g-0">
                <!-- 产品图片展示 -->
                <div class="col-lg-6">
                    <div class="p-4">
                        <div class="main-image-wrapper mb-3">
                            <?php if($product->image): ?>
                                <img src="<?php echo e(Storage::url($product->image)); ?>"
                                     alt="<?php echo e($product->name); ?>"
                                     class="img-fluid rounded-3 shadow-lg"
                                     style="width: 100%; height: 400px; object-fit: cover;">
                            <?php else: ?>
                                <img src="<?php echo e(asset('images/default-product.png')); ?>"
                                     alt="<?php echo e($product->name); ?>"
                                     class="img-fluid rounded-3 shadow-lg"
                                     style="width: 100%; height: 400px; object-fit: cover;">
                            <?php endif; ?>
                        </div>

                        <!-- 产品图片集 -->
                        <?php if($product->images && count($product->images) > 0): ?>
                            <div class="row g-2">
                                <?php $__currentLoopData = array_slice($product->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-3">
                                        <img src="<?php echo e(Storage::url($image)); ?>"
                                             alt="<?php echo e($product->name); ?>"
                                             class="img-fluid rounded-2 shadow-sm"
                                             style="width: 100%; height: 80px; object-fit: cover; cursor: pointer;"
                                             onclick="changeMainImage(this.src)">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="col-lg-6">
                    <div class="p-4">
                        <h2 class="h3 fw-bold text-dark mb-3"><?php echo e($product->name); ?></h2>

                        <?php if($product->description): ?>
                            <p class="text-muted mb-4"><?php echo e($product->description); ?></p>
                        <?php endif; ?>

                        <!-- 核心特性 -->
                        <div class="row g-3 mb-4">
                            <div class="col-6">
                                <div class="spec-item text-center">
                                    <div class="spec-icon mx-auto">
                                        <i class="fas fa-microchip"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">智能化</h6>
                                    <small class="text-muted">AI驱动测试</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="spec-item text-center">
                                    <div class="spec-icon mx-auto">
                                        <i class="fas fa-bullseye"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">高精度</h6>
                                    <small class="text-muted">±0.1°C精度</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="spec-item text-center">
                                    <div class="spec-icon mx-auto">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">可定制</h6>
                                    <small class="text-muted">灵活配置</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="spec-item text-center">
                                    <div class="spec-icon mx-auto">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">可靠性</h6>
                                    <small class="text-muted">稳定运行</small>
                                </div>
                            </div>
                        </div>

                        <!-- 产品规格 -->
                        <?php if($product->specifications && count($product->specifications) > 0): ?>
                            <div class="mb-4">
                                <h5 class="fw-bold mb-3">技术规格</h5>
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <?php $__currentLoopData = $product->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="fw-semibold text-muted" style="width: 40%;"><?php echo e($spec['name']); ?></td>
                                                <td><?php echo e($spec['value']); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- 操作按钮 -->
                        <div class="action-buttons d-grid gap-2 d-md-flex">
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary-custom flex-fill">
                                <i class="fas fa-phone me-2"></i>立即咨询
                            </a>
                            <a href="#" class="btn btn-outline-custom flex-fill" onclick="downloadBrochure()">
                                <i class="fas fa-download me-2"></i>下载资料
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 产品详细内容 -->
<?php if($product->content): ?>
<section class="py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-5">
                        <h3 class="h4 fw-bold text-center mb-4">产品详情</h3>
                        <div class="product-content">
                            <?php echo $product->content; ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 联系咨询 -->
<section class="py-4" id="contact" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h3 class="h4 fw-bold mb-3">对此产品感兴趣？</h3>
                <p class="text-muted mb-4">我们的专业团队将为您提供详细的产品咨询和定制化解决方案</p>

                <div class="row g-3 justify-content-center">
                    <div class="col-md-4">
                        <div class="contact-item p-3 rounded-3 bg-white shadow-sm">
                            <i class="fas fa-phone text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6 class="fw-bold">电话咨询</h6>
                            <p class="text-muted small mb-0">************</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="contact-item p-3 rounded-3 bg-white shadow-sm">
                            <i class="fas fa-envelope text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6 class="fw-bold">邮件咨询</h6>
                            <p class="text-muted small mb-0"><EMAIL></p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="contact-item p-3 rounded-3 bg-white shadow-sm">
                            <i class="fas fa-comments text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6 class="fw-bold">在线咨询</h6>
                            <p class="text-muted small mb-0">7x24小时服务</p>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary-custom btn-lg px-5">
                        <i class="fas fa-paper-plane me-2"></i>立即联系我们
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
// 切换主图片
function changeMainImage(src) {
    const mainImg = document.querySelector('.product-main-image');
    if (mainImg) {
        mainImg.src = src;
    }
}

// 下载产品资料
function downloadBrochure() {
    // 这里可以添加下载逻辑
    alert('产品资料下载功能开发中...');
}

// 平滑滚动到指定区域
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// 联系方式点击事件
document.addEventListener('DOMContentLoaded', function() {
    // 电话点击
    const phoneElements = document.querySelectorAll('[href*="tel:"]');
    phoneElements.forEach(el => {
        el.addEventListener('click', function() {
            // 可以添加统计代码
            console.log('用户点击了电话联系');
        });
    });

    // 邮件点击
    const emailElements = document.querySelectorAll('[href*="mailto:"]');
    emailElements.forEach(el => {
        el.addEventListener('click', function() {
            // 可以添加统计代码
            console.log('用户点击了邮件联系');
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/frontend/products/show.blade.php ENDPATH**/ ?>