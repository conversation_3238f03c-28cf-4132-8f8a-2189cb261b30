@echo off
echo ========================================
echo 修复产品中心Banner图片访问问题
echo ========================================
echo.

set "SOURCE_DIR=c:\Users\<USER>\Desktop\hanwang-test-equipment"
set "TARGET_DIR=D:\phpstudy_pro\WWW\hanwang-test-equipment"

echo 1. 检查源文件是否存在...
if exist "%SOURCE_DIR%\public\images\banner\product.png" (
    echo ✅ 源文件存在: %SOURCE_DIR%\public\images\banner\product.png
) else (
    echo ❌ 源文件不存在: %SOURCE_DIR%\public\images\banner\product.png
    pause
    exit /b 1
)

echo.
echo 2. 检查目标目录是否存在...
if exist "%TARGET_DIR%" (
    echo ✅ 目标目录存在: %TARGET_DIR%
) else (
    echo ❌ 目标目录不存在: %TARGET_DIR%
    echo 请检查PHPStudy配置或手动创建目录
    pause
    exit /b 1
)

echo.
echo 3. 创建必要的目录结构...
if not exist "%TARGET_DIR%\public\images\banner" (
    mkdir "%TARGET_DIR%\public\images\banner"
    echo ✅ 创建目录: %TARGET_DIR%\public\images\banner
) else (
    echo ✅ 目录已存在: %TARGET_DIR%\public\images\banner
)

echo.
echo 4. 复制图片文件...
copy "%SOURCE_DIR%\public\images\banner\product.png" "%TARGET_DIR%\public\images\banner\product.png"
if %errorlevel% == 0 (
    echo ✅ 文件复制成功
) else (
    echo ❌ 文件复制失败
    pause
    exit /b 1
)

echo.
echo 5. 验证文件是否存在...
if exist "%TARGET_DIR%\public\images\banner\product.png" (
    echo ✅ 目标文件存在: %TARGET_DIR%\public\images\banner\product.png
    
    echo.
    echo 6. 显示文件信息...
    dir "%TARGET_DIR%\public\images\banner\product.png"
) else (
    echo ❌ 目标文件不存在
    pause
    exit /b 1
)

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 现在您可以通过以下URL访问图片：
echo http://localhost/hanwang-test-equipment/images/banner/product.png
echo 或者根据您的PHPStudy配置：
echo http://localhost:端口号/hanwang-test-equipment/images/banner/product.png
echo.
echo 如果还是访问不到，请检查：
echo 1. PHPStudy是否正在运行
echo 2. 虚拟主机配置是否正确
echo 3. 项目的.htaccess文件配置
echo.
pause
