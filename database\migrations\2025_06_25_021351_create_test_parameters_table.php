<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_parameters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('test_record_id')->constrained('test_records')->onDelete('cascade')->comment('测试记录ID');
            $table->string('parameter_name')->comment('参数名称');
            $table->string('parameter_value')->comment('参数值');
            $table->string('unit')->nullable()->comment('单位');
            $table->decimal('min_value', 10, 4)->nullable()->comment('最小值');
            $table->decimal('max_value', 10, 4)->nullable()->comment('最大值');
            $table->decimal('measured_value', 10, 4)->nullable()->comment('测量值');
            $table->boolean('is_pass')->nullable()->comment('是否通过');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_parameters');
    }
};
