@extends('admin.layouts.app')

@section('title', '控制台')

@section('breadcrumb')
    <li class="breadcrumb-item active">控制台</li>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-tachometer-alt me-2"></i>控制台
    </h1>
    <p class="text-muted">欢迎回来，{{ Auth::guard('admin')->user()->name }}！</p>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                <i class="fas fa-cube"></i>
            </div>
            <h3 class="stat-number">{{ $stats['products'] }}</h3>
            <p class="stat-label">产品总数</p>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                <i class="fas fa-tags"></i>
            </div>
            <h3 class="stat-number">{{ $stats['categories'] }}</h3>
            <p class="stat-label">产品分类</p>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                <i class="fas fa-newspaper"></i>
            </div>
            <h3 class="stat-number">{{ $stats['news'] }}</h3>
            <p class="stat-label">新闻资讯</p>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                <i class="fas fa-users"></i>
            </div>
            <h3 class="stat-number">{{ $stats['admins'] }}</h3>
            <p class="stat-label">管理员</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最新产品 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-cube me-2"></i>最新产品
                </h5>
            </div>
            <div class="card-body">
                @if($latestProducts->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($latestProducts as $product)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                    <small class="text-muted">
                                        {{ $product->category->name ?? '未分类' }} •
                                        {{ $product->created_at->format('Y-m-d') }}
                                    </small>
                                </div>
                                <span class="badge bg-{{ $product->status === 'published' ? 'success' : 'warning' }} rounded-pill">
                                    {{ $product->status === 'published' ? '已发布' : '草稿' }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-primary">
                            查看全部产品 <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-cube fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无产品数据</p>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加产品
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- 最新新闻 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-newspaper me-2"></i>最新新闻
                </h5>
            </div>
            <div class="card-body">
                @if($latestNews->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($latestNews as $news)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $news->title }}</h6>
                                    <small class="text-muted">
                                        {{ $news->created_at->format('Y-m-d H:i') }}
                                    </small>
                                </div>
                                <span class="badge bg-{{ $news->status === 'published' ? 'success' : 'warning' }} rounded-pill">
                                    {{ $news->status === 'published' ? '已发布' : '草稿' }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.news.index') }}" class="btn btn-outline-primary">
                            查看全部新闻 <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无新闻数据</p>
                        <a href="{{ route('admin.news.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加新闻
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-bolt me-2"></i>快捷操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus fa-2x mb-2 d-block"></i>
                            添加产品
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-tags fa-2x mb-2 d-block"></i>
                            管理分类
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ route('admin.news.index') }}" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-newspaper fa-2x mb-2 d-block"></i>
                            发布新闻
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ route('admin.settings') }}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>系统版本：</strong></td>
                                <td>睿测微管理系统 v1.0</td>
                            </tr>
                            <tr>
                                <td><strong>Laravel版本：</strong></td>
                                <td>{{ app()->version() }}</td>
                            </tr>
                            <tr>
                                <td><strong>PHP版本：</strong></td>
                                <td>{{ PHP_VERSION }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>当前用户：</strong></td>
                                <td>{{ Auth::guard('admin')->user()->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>用户角色：</strong></td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{ Auth::guard('admin')->user()->role }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>登录时间：</strong></td>
                                <td>{{ now()->format('Y-m-d H:i:s') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
