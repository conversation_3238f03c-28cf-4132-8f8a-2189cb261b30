<?php $__env->startSection('title', '新闻详情'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.news.index')); ?>">新闻管理</a></li>
    <li class="breadcrumb-item active"><?php echo e($news->title); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-newspaper me-2"></i><?php echo e($news->title); ?>

        </h1>
        <p class="text-muted">新闻详情信息</p>
    </div>
    <div class="d-flex gap-2">
        <a href="<?php echo e(route('admin.news.edit', $news)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>编辑新闻
        </a>
        <a href="<?php echo e(route('admin.news.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-list me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- 基本信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>新闻标题：</strong></td>
                                <td><?php echo e($news->title); ?></td>
                            </tr>
                            <tr>
                                <td><strong>新闻别名：</strong></td>
                                <td><code><?php echo e($news->slug); ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>新闻分类：</strong></td>
                                <td>
                                    <?php if($news->category): ?>
                                        <span class="badge me-1" style="background-color: <?php echo e($news->category->color); ?>;">
                                            <?php echo e($news->category->name); ?>

                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>新闻类型：</strong></td>
                                <td>
                                    <?php switch($news->type):
                                        case ('company'): ?>
                                            <span class="badge bg-primary">公司新闻</span>
                                            <?php break; ?>
                                        <?php case ('industry'): ?>
                                            <span class="badge bg-success">行业资讯</span>
                                            <?php break; ?>
                                        <?php case ('technology'): ?>
                                            <span class="badge bg-info">技术动态</span>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>作者：</strong></td>
                                <td><?php echo e($news->author ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>来源：</strong></td>
                                <td><?php echo e($news->source ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>浏览量：</strong></td>
                                <td><span class="badge bg-secondary"><?php echo e($news->views); ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>状态：</strong></td>
                                <td>
                                    <?php if($news->is_published): ?>
                                        <span class="badge bg-success">已发布</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">草稿</span>
                                    <?php endif; ?>
                                    <?php if($news->is_featured): ?>
                                        <span class="badge bg-warning ms-1">推荐</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>发布时间：</strong></td>
                                <td>
                                    <?php if($news->published_at): ?>
                                        <?php echo e($news->published_at->format('Y-m-d H:i:s')); ?>

                                    <?php else: ?>
                                        <span class="text-muted">未发布</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td><?php echo e($news->created_at->format('Y-m-d H:i:s')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>更新时间：</strong></td>
                                <td><?php echo e($news->updated_at->format('Y-m-d H:i:s')); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <?php if($news->image): ?>
                            <div class="text-center">
                                <img src="<?php echo e(Storage::url($news->image)); ?>" 
                                     alt="<?php echo e($news->title); ?>" 
                                     class="img-fluid rounded shadow"
                                     style="max-width: 250px;">
                                <div class="mt-2 text-muted">新闻图片</div>
                            </div>
                        <?php else: ?>
                            <div class="text-center">
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 250px; height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                                <div class="mt-2 text-muted">暂无图片</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if($news->summary): ?>
                    <div class="mt-4">
                        <h6><strong>新闻摘要：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo e($news->summary); ?>

                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if($news->content): ?>
                    <div class="mt-4">
                        <h6><strong>新闻内容：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo $news->content; ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- SEO信息 -->
        <?php if($news->meta_title || $news->meta_description || $news->meta_keywords): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>SEO信息
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <?php if($news->meta_title): ?>
                            <tr>
                                <td width="120"><strong>SEO标题：</strong></td>
                                <td><?php echo e($news->meta_title); ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if($news->meta_description): ?>
                            <tr>
                                <td><strong>SEO描述：</strong></td>
                                <td><?php echo e($news->meta_description); ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if($news->meta_keywords): ?>
                            <tr>
                                <td><strong>SEO关键词：</strong></td>
                                <td><?php echo e($news->meta_keywords); ?></td>
                            </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="col-lg-4">
        <!-- 统计信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>新闻统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 class="text-primary mb-1"><?php echo e($news->id); ?></h3>
                            <small class="text-muted">新闻ID</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success mb-1"><?php echo e($news->views); ?></h3>
                        <small class="text-muted">浏览量</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1">
                                <?php echo e($news->published_at ? $news->published_at->diffForHumans() : '-'); ?>

                            </h4>
                            <small class="text-muted">发布时间</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1">
                            <?php echo e($news->content ? str_word_count(strip_tags($news->content)) : 0); ?>

                        </h4>
                        <small class="text-muted">字数统计</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.news.edit', $news)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>编辑新闻
                    </a>
                    <button type="button" class="btn btn-success" onclick="toggleStatus(<?php echo e($news->id); ?>)">
                        <?php if($news->is_published): ?>
                            <i class="fas fa-eye-slash me-2"></i>设为草稿
                        <?php else: ?>
                            <i class="fas fa-eye me-2"></i>发布新闻
                        <?php endif; ?>
                    </button>
                    <button type="button" class="btn btn-warning" onclick="toggleFeatured(<?php echo e($news->id); ?>)">
                        <?php if($news->is_featured): ?>
                            <i class="fas fa-star-half-alt me-2"></i>取消推荐
                        <?php else: ?>
                            <i class="fas fa-star me-2"></i>设为推荐
                        <?php endif; ?>
                    </button>
                    <form action="<?php echo e(route('admin.news.destroy', $news)); ?>" 
                          method="POST" 
                          onsubmit="return confirm('确定要删除这条新闻吗？此操作不可恢复！')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-2"></i>删除新闻
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 相关信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>相关信息
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if($news->category): ?>
                        <a href="<?php echo e(route('admin.news-categories.show', $news->category)); ?>" class="btn btn-outline-info">
                            <i class="fas fa-folder me-2"></i>查看分类
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('admin.news.index')); ?>?category=<?php echo e($news->category_id); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>同分类新闻
                    </a>
                    <a href="<?php echo e(route('admin.news.index')); ?>?type=<?php echo e($news->type); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-filter me-2"></i>同类型新闻
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function toggleStatus(newsId) {
        // 这里可以添加AJAX请求来切换新闻状态
        alert('状态切换功能需要后续实现');
    }
    
    function toggleFeatured(newsId) {
        // 这里可以添加AJAX请求来切换推荐状态
        alert('推荐状态切换功能需要后续实现');
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/news/show.blade.php ENDPATH**/ ?>