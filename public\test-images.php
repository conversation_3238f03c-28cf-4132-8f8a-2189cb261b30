<?php
echo "<h1>产品图片测试</h1>";

// 测试存储路径
$storagePath = 'products/a9NmiDOnEif2n8FgCWWkTYUfnvPqeXzEgvSO7Gok.png';
$imageUrl = '/storage/' . $storagePath;
$defaultImageUrl = '/images/default-product.png';

echo "<h2>图片路径测试</h2>";
echo "<p>存储图片路径: " . $storagePath . "</p>";
echo "<p>图片URL: " . $imageUrl . "</p>";
echo "<p>默认图片URL: " . $defaultImageUrl . "</p>";

echo "<h2>图片显示测试</h2>";
echo "<p>存储图片:</p>";
echo "<img src='" . $imageUrl . "' alt='产品图片' style='max-width: 200px; border: 1px solid #ccc;'>";

echo "<p>默认图片:</p>";
echo "<img src='" . $defaultImageUrl . "' alt='默认图片' style='max-width: 200px; border: 1px solid #ccc;'>";

echo "<h2>文件存在检查</h2>";
echo "<p>存储图片文件存在: " . (file_exists(__DIR__ . '/storage/' . $storagePath) ? '是' : '否') . "</p>";
echo "<p>默认图片文件存在: " . (file_exists(__DIR__ . '/images/default-product.png') ? '是' : '否') . "</p>";

echo "<h2>直接链接测试</h2>";
echo "<p><a href='" . $imageUrl . "' target='_blank'>点击查看存储图片</a></p>";
echo "<p><a href='" . $defaultImageUrl . "' target='_blank'>点击查看默认图片</a></p>";
?>
