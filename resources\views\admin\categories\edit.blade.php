@extends('admin.layouts.app')

@section('title', '编辑产品分类')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.categories.index') }}">产品分类</a></li>
    <li class="breadcrumb-item active">编辑分类</li>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-edit me-2"></i>编辑产品分类
    </h1>
    <p class="text-muted">编辑分类：{{ $category->name }}</p>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.categories.update', $category) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $category->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">别名</label>
                                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                       id="slug" name="slug" value="{{ old('slug', $category->slug) }}" 
                                       placeholder="留空自动生成">
                                @error('slug')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">用于URL，只能包含字母、数字和连字符</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">分类描述</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description', $category->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">父级分类</label>
                                <select class="form-select @error('parent_id') is-invalid @enderror" 
                                        id="parent_id" name="parent_id">
                                    <option value="">选择父级分类（可选）</option>
                                    @foreach($parentCategories as $parent)
                                        <option value="{{ $parent->id }}" 
                                                {{ old('parent_id', $category->parent_id) == $parent->id ? 'selected' : '' }}>
                                            {{ $parent->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('parent_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">排序</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $category->sort_order) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">数字越小排序越靠前</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">分类图片</label>
                        @if($category->image)
                            <div class="mb-2">
                                <img src="{{ Storage::url($category->image) }}" alt="{{ $category->name }}" 
                                     class="img-thumbnail" style="max-width: 200px;">
                                <div class="form-text">当前图片</div>
                            </div>
                        @endif
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">支持 JPEG、PNG、JPG、GIF 格式，最大 2MB。选择新图片将替换当前图片。</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                启用分类
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>更新分类
                        </button>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        <a href="{{ route('admin.categories.show', $category) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>查看详情
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>分类信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>ID：</strong></td>
                        <td>{{ $category->id }}</td>
                    </tr>
                    <tr>
                        <td><strong>创建时间：</strong></td>
                        <td>{{ $category->created_at->format('Y-m-d H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>更新时间：</strong></td>
                        <td>{{ $category->updated_at->format('Y-m-d H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>产品数量：</strong></td>
                        <td>
                            <span class="badge bg-primary">{{ $category->products()->count() }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>子分类：</strong></td>
                        <td>
                            <span class="badge bg-info">{{ $category->children()->count() }}</span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>提示
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>编辑说明</h6>
                    <ul class="mb-0">
                        <li>修改分类名称会影响前台显示</li>
                        <li>修改别名会影响URL地址</li>
                        <li>禁用分类会隐藏其下所有产品</li>
                    </ul>
                </div>
                
                @if($category->products()->count() > 0)
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>注意</h6>
                        <p class="mb-0">该分类下有 {{ $category->products()->count() }} 个产品，删除前请先处理这些产品。</p>
                    </div>
                @endif
                
                @if($category->children()->count() > 0)
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>注意</h6>
                        <p class="mb-0">该分类下有 {{ $category->children()->count() }} 个子分类，删除前请先处理这些子分类。</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // 自动生成别名
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '') // 移除特殊字符
            .replace(/\s+/g, '-') // 空格替换为连字符
            .replace(/-+/g, '-') // 多个连字符替换为单个
            .trim('-'); // 移除首尾连字符
        
        if (document.getElementById('slug').value === '' || document.getElementById('slug').value === '{{ $category->slug }}') {
            document.getElementById('slug').value = slug;
        }
    });
</script>
@endpush
