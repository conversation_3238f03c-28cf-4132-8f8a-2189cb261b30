@extends('admin.layouts.app')

@section('title', '用户详情')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">用户详情</h1>
                    <p class="text-muted mb-0">查看用户 <strong>{{ $user->name }}</strong> 的详细信息</p>
                </div>
                <div class="btn-group">
                    <a href="{{ route('admin.admin-users.edit', $user) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>编辑用户
                    </a>
                    <a href="{{ route('admin.admin-users.change-password', $user) }}" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>修改密码
                    </a>
                    <a href="{{ route('admin.admin-users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回列表
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- 用户基本信息 -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="avatar-circle mx-auto mb-3">
                                {{ substr($user->name, 0, 1) }}
                            </div>
                            <h4 class="card-title">{{ $user->name }}</h4>
                            <p class="text-muted">{{ $user->email }}</p>
                            
                            @php
                                $roleColors = [
                                    'super_admin' => 'danger',
                                    'admin' => 'primary',
                                    'operator' => 'info',
                                    'user' => 'secondary'
                                ];
                            @endphp
                            <span class="badge bg-{{ $roleColors[$user->role] ?? 'secondary' }} fs-6 px-3 py-2">
                                {{ $user->getRoleDisplayName() }}
                            </span>
                            
                            <div class="mt-3">
                                @if($user->status === 'active')
                                    <span class="badge bg-success fs-6 px-3 py-2">
                                        <i class="fas fa-check-circle me-1"></i>账号启用
                                    </span>
                                @else
                                    <span class="badge bg-danger fs-6 px-3 py-2">
                                        <i class="fas fa-times-circle me-1"></i>账号禁用
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tools me-2"></i>快速操作
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.admin-users.edit', $user) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>编辑信息
                                </a>
                                <a href="{{ route('admin.admin-users.change-password', $user) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-key me-2"></i>修改密码
                                </a>
                                <button type="button" class="btn btn-outline-info" onclick="toggleUserStatus()">
                                    <i class="fas fa-power-off me-2"></i>
                                    {{ $user->status === 'active' ? '禁用账号' : '启用账号' }}
                                </button>
                                @if($user->id !== Auth::guard('admin')->id())
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteUser()">
                                        <i class="fas fa-trash me-2"></i>删除用户
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细信息 -->
                <div class="col-lg-8">
                    <!-- 基本信息 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>基本信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">姓名</label>
                                        <div class="fw-bold">{{ $user->name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">邮箱</label>
                                        <div class="fw-bold">{{ $user->email }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">角色</label>
                                        <div>
                                            <span class="badge bg-{{ $roleColors[$user->role] ?? 'secondary' }}">
                                                {{ $user->getRoleDisplayName() }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">部门</label>
                                        <div class="fw-bold">{{ $user->department ?? '-' }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">手机号</label>
                                        <div class="fw-bold">{{ $user->phone ?? '-' }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">账号状态</label>
                                        <div>
                                            @if($user->status === 'active')
                                                <span class="badge bg-success">启用</span>
                                            @else
                                                <span class="badge bg-danger">禁用</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账号统计 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>账号统计
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 text-primary mb-1">{{ $user->created_at->diffInDays() }}</div>
                                        <small class="text-muted">注册天数</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 text-success mb-1">
                                            @if($user->email_verified_at)
                                                <i class="fas fa-check-circle"></i>
                                            @else
                                                <i class="fas fa-times-circle text-danger"></i>
                                            @endif
                                        </div>
                                        <small class="text-muted">邮箱验证</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 text-info mb-1">
                                            @if($user->last_login_at)
                                                {{ $user->last_login_at->diffInDays() }}
                                            @else
                                                -
                                            @endif
                                        </div>
                                        <small class="text-muted">最后登录</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 text-warning mb-1">0</div>
                                        <small class="text-muted">操作记录</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间信息 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>时间信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">创建时间</label>
                                        <div class="fw-bold">{{ $user->created_at->format('Y-m-d H:i:s') }}</div>
                                        <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label text-muted">最后更新</label>
                                        <div class="fw-bold">{{ $user->updated_at->format('Y-m-d H:i:s') }}</div>
                                        <small class="text-muted">{{ $user->updated_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                                @if($user->email_verified_at)
                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">邮箱验证时间</label>
                                            <div class="fw-bold">{{ $user->email_verified_at->format('Y-m-d H:i:s') }}</div>
                                            <small class="text-muted">{{ $user->email_verified_at->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                @endif
                                @if($user->last_login_at)
                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">最后登录时间</label>
                                            <div class="fw-bold">{{ $user->last_login_at->format('Y-m-d H:i:s') }}</div>
                                            <small class="text-muted">{{ $user->last_login_at->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 2rem;
}

.info-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.info-item:last-child {
    border-bottom: none;
}
</style>
@endpush

@push('scripts')
<script>
function toggleUserStatus() {
    const userId = {{ $user->id }};
    const currentStatus = {{ $user->status === 'active' ? 'true' : 'false' }};
    const action = currentStatus ? '禁用' : '启用';

    if (confirm(`确定要${action}此用户吗？`)) {
        fetch(`{{ route('admin.admin-users.index') }}/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请重试');
        });
    }
}

function deleteUser() {
    const userName = '{{ $user->name }}';
    
    if (confirm(`确定要删除用户 ${userName} 吗？\n\n此操作不可撤销！`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route('admin.admin-users.destroy', $user) }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
