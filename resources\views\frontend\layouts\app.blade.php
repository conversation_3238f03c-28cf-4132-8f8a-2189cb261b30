<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>@yield('title', '上海睿测微智能科技有限公司 - 智能测试设备领导者')</title>
    <meta name="description" content="@yield('description', '上海睿测微智能科技有限公司专注智能测试设备研发，提供专业的半导体测试、芯片检测、智能制造解决方案')">
    <meta name="keywords" content="@yield('keywords', '智能测试设备,半导体测试,芯片检测,智能制造,上海睿测微')">

    <!-- Bootstrap CSS - 使用国内CDN -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome - 使用国内CDN -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- AOS动画库 - 使用国内CDN -->
    <link href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.1/aos.css" rel="stylesheet">

    <style>
        /* 品牌色彩定义 - 基于睿测微logo的蓝色 */
        :root {
            --primary-blue: #1e4a8c;
            --secondary-blue: #2563eb;
            --accent-blue: #3b82f6;
            --light-blue: #60a5fa;
            --dark-blue: #1e3a8a;
            --gradient-primary: linear-gradient(135deg, #1e4a8c 0%, #2563eb 50%, #3b82f6 100%);
            --gradient-secondary: linear-gradient(45deg, #1e3a8a 0%, #1e4a8c 100%);
        }

        /* 大气企业级导航栏 */
        .navbar {
            padding: 1.5rem 0;
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .navbar-brand {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-blue) !important;
            display: flex;
            align-items: center;
            text-decoration: none;
            margin-right: 4rem;
        }
        .navbar-brand svg {
            height: 80px;
            margin-right: 20px;
            filter: drop-shadow(0 3px 8px rgba(74, 144, 226, 0.15));
        }
        .navbar-brand:hover {
            color: var(--secondary-blue) !important;
            transform: scale(1.02);
            transition: all 0.3s ease;
        }

        /* 精致导航按钮 - 参考汉王风格 */
        .navbar-nav {
            gap: 2.8rem;
            align-items: center;
        }
        .navbar-nav .nav-link {
            color: #333333 !important;
            font-weight: 400;
            font-size: 1.05rem;
            padding: 1rem 0 !important;
            margin: 0;
            transition: all 0.3s ease;
            position: relative;
            background: transparent;
            border: none;
            letter-spacing: 0.3px;
        }

        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary-blue);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .navbar-nav .nav-link:hover::after,
        .navbar-nav .nav-link.active::after {
            width: 100%;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: var(--primary-blue) !important;
        }

        /* 下拉箭头样式 */
        .navbar-nav .dropdown-toggle::after {
            margin-left: 0.4rem;
            transition: transform 0.3s ease;
            font-size: 0.75rem;
        }
        .navbar-nav .dropdown:hover .dropdown-toggle::after {
            transform: rotate(180deg);
        }

        /* 精致下拉菜单 */
        .dropdown-menu {
            border: none;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            padding: 0.8rem 0;
            background: white;
            margin-top: 0.8rem;
            min-width: 180px;
        }
        .dropdown-item {
            padding: 0.7rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 400;
            font-size: 0.95rem;
            color: #333333;
            border: none;
            background: transparent;
        }
        .dropdown-item:hover {
            color: var(--primary-blue);
            background: #f8fafc;
            padding-left: 1.5rem;
        }
        .dropdown-item:active {
            background: #e2e8f0;
        }

        /* 大气英雄轮播区域 */
        .hero-carousel {
            height: 600px;
            position: relative;
            overflow: hidden;
        }
        .carousel-item {
            height: 600px;
            background-size: cover;
            background-position: center;
            position: relative;
        }
        .carousel-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }
        .carousel-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
                        linear-gradient(-45deg, rgba(255,255,255,0.02) 25%, transparent 25%);
            background-size: 30px 30px;
            z-index: 1;
        }
        .carousel-content {
            position: relative;
            z-index: 3;
            height: 100%;
            display: flex;
            align-items: center;
            color: white;
        }
        .carousel-caption h2 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            line-height: 1.1;
        }
        .carousel-caption p {
            font-size: 1.4rem;
            line-height: 1.7;
            margin-bottom: 2.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            max-width: 600px;
        }
        .carousel-visual {
            position: relative;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .carousel-visual .main-icon {
            font-size: 15rem;
            opacity: 0.2;
            position: absolute;
            z-index: 1;
        }
        .carousel-visual .center-element {
            position: relative;
            z-index: 3;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        .carousel-visual .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 2;
        }
        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: float 6s ease-in-out infinite;
        }
        .carousel-control-prev,
        .carousel-control-next {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.8;
            transition: all 0.3s ease;
        }
        .carousel-control-prev {
            left: 30px;
        }
        .carousel-control-next {
            right: 30px;
        }
        .carousel-control-prev:hover,
        .carousel-control-next:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-50%) scale(1.1);
        }
        .carousel-indicators {
            bottom: 30px;
            gap: 10px;
        }
        .carousel-indicators [data-bs-target] {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin: 0;
            background: rgba(255, 255, 255, 0.5);
            border: 2px solid rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        .carousel-indicators [data-bs-target].active {
            background: white;
            transform: scale(1.2);
        }

        /* 芯片测试设备视觉效果 */
        .chip-testing-visual {
            position: relative;
            animation: float 8s ease-in-out infinite;
        }

        .testing-equipment {
            position: relative;
            animation: pulse 4s ease-in-out infinite;
        }

        .equipment-body {
            transition: all 0.3s ease;
        }

        .equipment-body:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
        }

        .chip-under-test i {
            animation: chipGlow 3s ease-in-out infinite alternate;
        }

        .chip-under-test i:nth-child(1) {
            animation-delay: 0s;
        }

        .chip-under-test i:nth-child(2) {
            animation-delay: 0.5s;
        }

        .chip-under-test i:nth-child(3) {
            animation-delay: 1s;
        }

        .temperature-controls .temp-control {
            transition: all 0.3s ease;
        }

        .temperature-controls .temp-control:hover {
            transform: translateY(-5px);
        }

        .floating-features .feature-badge {
            animation: badgeFloat 6s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .floating-features .feature-badge:nth-child(1) {
            animation-delay: 0s;
        }

        .floating-features .feature-badge:nth-child(2) {
            animation-delay: 1.5s;
        }

        .floating-features .feature-badge:nth-child(3) {
            animation-delay: 3s;
        }

        .floating-features .feature-badge:nth-child(4) {
            animation-delay: 4.5s;
        }

        .floating-features .feature-badge:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        @keyframes chipGlow {
            0% {
                opacity: 0.7;
                transform: scale(1);
            }
            100% {
                opacity: 1;
                transform: scale(1.1);
                filter: brightness(1.2);
            }
        }

        @keyframes badgeFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
        }

        /* 新的芯片动画效果 */
        @keyframes chipFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.6;
            }
        }

        @keyframes orbitChip {
            0% {
                transform: rotate(0deg) translateX(80px) rotate(0deg);
            }
            100% {
                transform: rotate(360deg) translateX(80px) rotate(-360deg);
            }
        }

        /* 主芯片容器效果 */
        .main-chip-container {
            animation: mainChipFloat 6s ease-in-out infinite;
        }

        .main-chip {
            transition: all 0.3s ease;
            animation: chipPulse 4s ease-in-out infinite;
        }

        .main-chip:hover {
            transform: scale(1.05);
            box-shadow: 0 25px 80px rgba(59, 130, 246, 0.6);
        }

        .chip-core i {
            animation: coreGlow 3s ease-in-out infinite alternate;
        }

        .pin {
            animation: pinGlow 2s ease-in-out infinite alternate;
        }

        .pin:nth-child(odd) {
            animation-delay: 0.5s;
        }

        .temp-indicator {
            transition: all 0.3s ease;
        }

        .temp-indicator:hover {
            transform: translateY(-5px) scale(1.1);
        }

        .temp-icon {
            transition: all 0.3s ease;
        }

        .temp-indicator:hover .temp-icon {
            transform: scale(1.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        @keyframes mainChipFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes chipPulse {
            0%, 100% {
                box-shadow: 0 20px 60px rgba(59, 130, 246, 0.4);
            }
            50% {
                box-shadow: 0 25px 80px rgba(59, 130, 246, 0.6);
            }
        }

        @keyframes coreGlow {
            0% {
                filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
            }
            100% {
                filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.8));
            }
        }

        @keyframes pinGlow {
            0% {
                background: #fbbf24;
                box-shadow: 0 0 5px rgba(251, 191, 36, 0.5);
            }
            100% {
                background: #f59e0b;
                box-shadow: 0 0 15px rgba(245, 158, 11, 0.8);
            }
        }

        /* 新的实验室风格动画 */
        @keyframes lightFlicker {
            0%, 100% {
                opacity: 0.3;
                transform: scaleY(1);
            }
            50% {
                opacity: 0.6;
                transform: scaleY(1.1);
            }
        }

        @keyframes techFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-15px) rotate(180deg);
                opacity: 0.6;
            }
        }

        @keyframes statusBlink {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.2);
            }
        }

        @keyframes envFloat {
            0%, 100% {
                transform: translateY(0px);
                opacity: 0.9;
            }
            50% {
                transform: translateY(-8px);
                opacity: 1;
            }
        }

        /* 实验室设备样式 */
        .lab-equipment-showcase {
            animation: labFloat 8s ease-in-out infinite;
        }

        .equipment-container {
            transition: all 0.3s ease;
        }

        .equipment-container:hover {
            transform: scale(1.02);
        }

        .main-equipment {
            transition: all 0.3s ease;
            animation: equipmentPulse 6s ease-in-out infinite;
        }

        .main-equipment:hover {
            transform: rotateY(-8deg) rotateX(8deg) scale(1.05);
            box-shadow: 0 30px 100px rgba(59, 130, 246, 0.6);
        }

        .chip-holder {
            animation: chipProcess 4s ease-in-out infinite;
        }

        .control-button {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .control-button:hover {
            transform: scale(1.2);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }

        .env-indicator {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .env-indicator:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        @keyframes labFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        @keyframes equipmentPulse {
            0%, 100% {
                box-shadow: 0 25px 80px rgba(59, 130, 246, 0.4);
            }
            50% {
                box-shadow: 0 30px 100px rgba(59, 130, 246, 0.6);
            }
        }

        @keyframes chipProcess {
            0%, 100% {
                box-shadow: 0 5px 15px rgba(251, 191, 36, 0.4);
            }
            50% {
                box-shadow: 0 8px 25px rgba(251, 191, 36, 0.7);
                transform: scale(1.05);
            }
        }

        /* 特点卡片动画 */
        .feature-item {
            transition: all 0.3s ease;
            animation: featureSlideIn 0.8s ease-out forwards;
            opacity: 0;
            transform: translateX(-30px);
        }

        .feature-item:nth-child(1) { animation-delay: 0.2s; }
        .feature-item:nth-child(2) { animation-delay: 0.4s; }
        .feature-item:nth-child(3) { animation-delay: 0.6s; }
        .feature-item:nth-child(4) { animation-delay: 0.8s; }

        .feature-item:hover {
            transform: translateX(5px) scale(1.02);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            transition: all 0.3s ease;
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(5deg);
        }

        @keyframes featureSlideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 公司标识动画 */
        .company-badge {
            animation: badgeGlow 3s ease-in-out infinite;
        }

        @keyframes badgeGlow {
            0%, 100% {
                box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
            }
            50% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
            }
        }

        /* 标题动画 */
        .carousel-caption h1 {
            animation: titleSlideUp 1s ease-out;
        }

        .carousel-caption h2 {
            animation: titleSlideUp 1s ease-out 0.3s both;
        }

        @keyframes titleSlideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 新的实验室和芯片动画 */
        @keyframes labPulse {
            0%, 100% {
                opacity: 0.2;
                transform: scale(1);
            }
            50% {
                opacity: 0.4;
                transform: scale(1.1);
            }
        }

        @keyframes chipFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.4;
            }
        }

        @keyframes chipPulse {
            0%, 100% {
                box-shadow: 0 10px 30px rgba(251, 191, 36, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 15px 40px rgba(251, 191, 36, 0.7);
                transform: scale(1.05);
            }
        }

        /* 产品图片浮动动画 */
        @keyframes productFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* 轮播图背景模式 */
        .carousel-bg-pattern-1 {
            background-image: linear-gradient(45deg, rgba(255,255,255,0.03) 25%, transparent 25%),
                              linear-gradient(-45deg, rgba(255,255,255,0.03) 25%, transparent 25%);
            background-size: 40px 40px;
        }

        .carousel-bg-pattern-2 {
            background-image: linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                              linear-gradient(-45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                              linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.05) 75%),
                              linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.05) 75%);
            background-size: 25px 25px;
            background-position: 0 0, 0 12.5px, 12.5px -12.5px, -12.5px 0px;
        }

        .carousel-bg-pattern-3 {
            background-image: linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px),
                              linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .carousel-bg-pattern-4 {
            background-image: linear-gradient(45deg, rgba(0,0,0,0.02) 25%, transparent 25%),
                              linear-gradient(-45deg, rgba(0,0,0,0.02) 25%, transparent 25%);
            background-size: 30px 30px;
        }

        /* 特性标签浮动动画 */
        @keyframes tagFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.9;
            }
            50% {
                transform: translateY(-5px) scale(1.05);
                opacity: 1;
            }
        }

        /* 特点高亮卡片样式 */
        .feature-highlight {
            transition: all 0.3s ease;
            border: 1px solid rgba(30,74,140,0.1);
        }

        .feature-highlight:hover {
            transform: translateY(-8px) scale(1.05);
            background: rgba(255,255,255,1) !important;
            box-shadow: 0 15px 40px rgba(30,74,140,0.2);
            border-color: rgba(30,74,140,0.3);
        }

        .feature-highlight .feature-icon {
            transition: all 0.3s ease;
        }

        .feature-highlight:hover .feature-icon {
            transform: scale(1.2) rotate(5deg);
        }

        /* 产品图片容器样式 */
        .main-product-image {
            transition: all 0.3s ease;
            border: 3px solid rgba(30,74,140,0.2);
        }

        .main-product-image:hover {
            transform: scale(1.05) rotateY(-5deg);
            box-shadow: 0 20px 60px rgba(30,74,140,0.3);
            border-color: rgba(30,74,140,0.5);
        }

        /* 特性标签样式 */
        .feature-tag {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .feature-tag:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* 新的轮播图动画 */
        @keyframes chipTest {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(251,191,36,0.3);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(251,191,36,0.5);
            }
        }

        @keyframes chipPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 10px 30px rgba(5,150,105,0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 15px 40px rgba(5,150,105,0.5);
            }
        }

        @keyframes nodeFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes servicePulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 10px 30px rgba(245,158,11,0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 15px 40px rgba(245,158,11,0.5);
            }
        }

        @keyframes serviceFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-8px);
            }
        }

        /* 特性卡片样式 */
        .feature-card {
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 30px rgba(30,74,140,0.15) !important;
        }

        .feature-card .feature-icon {
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.2) rotate(5deg);
        }

        /* 应用项目和服务项目样式 */
        .application-item, .service-item {
            transition: all 0.3s ease;
        }

        .application-item:hover, .service-item:hover {
            transform: translateX(10px);
            background: rgba(255,255,255,0.2) !important;
        }

        /* 技术特点样式 */
        .tech-feature {
            transition: all 0.3s ease;
        }

        .tech-feature:hover {
            transform: translateX(10px);
            background: rgba(255,255,255,0.15) !important;
        }

        /* 特点卡片悬停效果 */
        .feature-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .feature-card:hover .feature-icon-large {
            transform: scale(1.1) rotate(5deg);
        }

        /* 芯片展示容器 */
        .chip-base:hover {
            transform: rotateY(-15deg) rotateX(10deg) scale(1.05);
            box-shadow: 0 40px 120px rgba(59, 130, 246, 0.5);
        }

        .main-chip-display {
            animation: chipShowcase 8s ease-in-out infinite;
        }

        @keyframes chipShowcase {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* 卡片样式 */
        .feature-card {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            height: 100%;
            border-radius: 20px;
            overflow: hidden;
            background: white;
            box-shadow: 0 10px 30px rgba(30, 74, 140, 0.08);
            border: 1px solid rgba(30, 74, 140, 0.05);
        }
        .feature-card:hover {
            transform: translateY(-20px) scale(1.03);
            box-shadow: 0 30px 60px rgba(30, 74, 140, 0.2);
            border-color: var(--accent-blue);
        }
        .feature-card .card-body {
            padding: 3rem 2.5rem;
        }
        .feature-card .fa-3x {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 产品卡片 */
        .product-card {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            box-shadow: 0 10px 30px rgba(30, 74, 140, 0.08);
            border-radius: 20px;
            overflow: hidden;
            background: white;
        }
        .product-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 50px rgba(30, 74, 140, 0.2);
        }

        /* 新闻卡片 */
        .news-card {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 20px;
            overflow: hidden;
            background: white;
            box-shadow: 0 8px 25px rgba(30, 74, 140, 0.08);
        }
        .news-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 20px 40px rgba(30, 74, 140, 0.15);
        }

        /* 标题样式 */
        .section-title {
            position: relative;
            margin-bottom: 4rem;
            font-weight: 800;
            color: var(--primary-blue);
            font-size: 2.5rem;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 5px;
            background: var(--gradient-primary);
            border-radius: 3px;
        }

        /* 按钮样式 */
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(30, 74, 140, 0.2);
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 74, 140, 0.3);
            background: var(--gradient-secondary);
            color: white;
        }
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
            text-decoration: none;
            display: inline-block;
        }
        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
            color: white;
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        }
        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: white;
            background: transparent;
            border-radius: 12px;
            padding: 10px 28px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-outline-light:hover {
            background: white;
            color: var(--primary-blue);
            border-color: white;
            transform: translateY(-2px);
        }

        /* 页脚 */
        footer {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: white;
            border-top: none;
        }
        .footer-link {
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .footer-link:hover {
            color: var(--light-blue);
            transform: translateX(3px);
        }

        /* 动画效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
        }
        .floating {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(30, 74, 140, 0.3); }
            50% { box-shadow: 0 0 40px rgba(30, 74, 140, 0.6); }
        }
        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.5rem;
            }
            .section-title {
                font-size: 2rem;
            }
            .hero-section {
                padding: 100px 0;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <svg width="280" height="80" viewBox="0 0 280 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- 圆形背景 -->
                    <circle cx="40" cy="40" r="35" fill="#1e4a8c"/>
                    <!-- S形状 -->
                    <path d="M22 22 C30 22 40 30 40 40 C40 50 30 58 22 58" stroke="white" stroke-width="4" fill="none"/>
                    <path d="M58 22 C50 22 40 30 40 40 C40 50 50 58 58 58" stroke="white" stroke-width="4" fill="none"/>
                    <!-- 中文文字 -->
                    <text x="90" y="35" font-family="Microsoft YaHei, Arial, sans-serif" font-size="28" font-weight="bold" fill="#1e4a8c">睿测微</text>
                    <!-- 英文文字 -->
                    <text x="90" y="55" font-family="Arial, sans-serif" font-size="14" fill="#666666">RUI CE WEI</text>
                </svg>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('products.*') ? 'active' : '' }}" href="{{ route('products.index') }}">产品中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('solutions') ? 'active' : '' }}" href="{{ route('solutions') }}">解决方案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}" href="{{ route('about') }}">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('services') ? 'active' : '' }}" href="{{ route('services') }}">服务支持</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('news.*') ? 'active' : '' }}" href="{{ route('news.index') }}">新闻资讯</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">联系我们</a>
                    </li>
                    @auth
                        <li class="nav-item ms-4">
                            <a class="btn btn-outline-primary px-4 py-2 rounded-pill" href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-cog me-2"></i>管理后台
                            </a>
                        </li>
                    @endauth
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        @yield('content')
    </main>

    <!-- 页脚 -->
    <footer class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3 text-white">
                        <i class="fas fa-atom me-2 text-info"></i>上海睿测微智能科技有限公司
                    </h5>
                    <p class="text-light">
                        上海睿测微智能科技有限公司专注智能测试设备研发，为客户提供专业的半导体测试、芯片检测、智能制造解决方案。
                    </p>
                    <div class="d-flex">
                        <a href="#" class="footer-link me-3"><i class="fab fa-weixin fa-lg"></i></a>
                        <a href="#" class="footer-link me-3"><i class="fab fa-weibo fa-lg"></i></a>
                        <a href="#" class="footer-link me-3"><i class="fab fa-linkedin fa-lg"></i></a>
                        <a href="#" class="footer-link me-3"><i class="fab fa-github fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3 text-white">产品中心</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="footer-link">智能温控系统</a></li>
                        <li><a href="#" class="footer-link">芯片测试设备</a></li>
                        <li><a href="#" class="footer-link">自动化检测</a></li>
                        <li><a href="#" class="footer-link">智能制造系统</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3 text-white">服务支持</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="footer-link">技术支持</a></li>
                        <li><a href="#" class="footer-link">售后服务</a></li>
                        <li><a href="#" class="footer-link">培训服务</a></li>
                        <li><a href="#" class="footer-link">维修服务</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="mb-3 text-white">联系信息</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            上海市杨浦区长白新村街道军工路1076号机械共性研究院401室
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            139-1726-8669 / 158-2179-6586
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <EMAIL>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-globe me-2"></i>
                            www.sh-ruicewei.com
                        </li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">© 2025 上海睿测微智能科技有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="footer-link me-3">隐私政策</a>
                    <a href="#" class="footer-link">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS - 使用国内CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <!-- AOS动画库 - 使用国内CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.1/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>

    @stack('scripts')
    <script>
        // 初始化轮播图 - 分开控制每张图的时间
        document.addEventListener('DOMContentLoaded', function() {
            var heroCarouselElement = document.getElementById('heroCarousel');
            if (heroCarouselElement) {
                var carousel = new bootstrap.Carousel(heroCarouselElement, {
                    interval: false, // 禁用自动切换，手动控制
                    wrap: true,
                    pause: false
                });

                var currentSlide = 0;
                var slideIntervals = [60000, 5000, 5000, 5000]; // 第一张60秒，其他5秒
                var timer;

                function startTimer() {
                    clearTimeout(timer);
                    timer = setTimeout(function() {
                        currentSlide = (currentSlide + 1) % slideIntervals.length;
                        carousel.next();
                        startTimer();
                    }, slideIntervals[currentSlide]);
                }

                // 监听轮播图切换事件
                heroCarouselElement.addEventListener('slid.bs.carousel', function(event) {
                    currentSlide = event.to;
                    startTimer();
                });

                // 启动定时器
                startTimer();
            }
        });
    </script>
</body>
</html>

