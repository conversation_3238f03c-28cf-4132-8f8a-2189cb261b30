<?php
echo "<h1>PHP扩展检查</h1>";

$required_extensions = [
    'mbstring' => 'MB String (多字节字符串)',
    'fileinfo' => 'File Info (文件信息)',
    'openssl' => 'OpenSSL (加密)',
    'curl' => 'cURL (网络请求)',
    'pdo_mysql' => 'PDO MySQL (数据库)',
    'mysqli' => 'MySQLi (数据库)',
    'gd' => 'GD (图像处理)',
    'zip' => 'ZIP (压缩)',
    'json' => 'JSON (数据格式)'
];

echo "<table border='1' cellpadding='10' cellspacing='0'>";
echo "<tr><th>扩展名</th><th>描述</th><th>状态</th></tr>";

foreach ($required_extensions as $ext => $desc) {
    $status = extension_loaded($ext);
    $color = $status ? 'green' : 'red';
    $text = $status ? '✅ 已启用' : '❌ 未启用';
    
    echo "<tr>";
    echo "<td><strong>{$ext}</strong></td>";
    echo "<td>{$desc}</td>";
    echo "<td style='color: {$color};'>{$text}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>mbstring函数测试</h2>";
if (function_exists('mb_strimwidth')) {
    echo "<p style='color: green;'>✅ mb_strimwidth() 函数可用</p>";
    echo "<p>测试: " . mb_strimwidth('这是一个测试字符串', 0, 10, '...') . "</p>";
} else {
    echo "<p style='color: red;'>❌ mb_strimwidth() 函数不可用</p>";
}

echo "<h2>PHP版本信息</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>PHP配置文件: " . php_ini_loaded_file() . "</p>";

echo "<h2>已加载的扩展</h2>";
$loaded_extensions = get_loaded_extensions();
sort($loaded_extensions);
echo "<p>" . implode(', ', $loaded_extensions) . "</p>";
?>
