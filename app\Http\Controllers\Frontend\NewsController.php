<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\NewsCategory;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    /**
     * 新闻列表页面
     */
    public function index(Request $request)
    {
        $query = News::published();

        // 栏目筛选
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // 类型筛选
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('summary', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $news = $query->latest('published_at')->paginate(10);

        // 推荐新闻
        $featuredNews = News::published()->featured()->latest('published_at')->take(5)->get();

        // 最新新闻
        $latestNews = News::published()->latest('published_at')->take(8)->get();

        // 获取栏目列表（包含新闻数量）
        $categories = NewsCategory::active()
            ->withCount(['news' => function($query) {
                $query->published();
            }])
            ->ordered()
            ->get();

        return view('frontend.news.index', compact('news', 'featuredNews', 'latestNews', 'categories'));
    }

    /**
     * 新闻详情页面
     */
    public function show($slug)
    {
        $news = News::where('slug', $slug)->published()->firstOrFail();

        // 增加浏览量
        $news->increment('views');

        // 获取栏目列表（包含新闻数量）
        $categories = NewsCategory::active()
            ->withCount(['news' => function($query) {
                $query->published();
            }])
            ->ordered()
            ->get();

        // 上一篇和下一篇新闻
        $prevNews = News::where('id', '<', $news->id)
            ->published()
            ->orderBy('id', 'desc')
            ->first();

        $nextNews = News::where('id', '>', $news->id)
            ->published()
            ->orderBy('id', 'asc')
            ->first();

        return view('frontend.news.show', compact('news', 'categories', 'prevNews', 'nextNews'));
    }
}
