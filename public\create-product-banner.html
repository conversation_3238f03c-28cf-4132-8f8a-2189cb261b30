<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品Banner图片创建</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .banner-preview {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        .banner-preview::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 100%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="70" r="1.5" fill="rgba(255,255,255,0.12)"/><rect x="60" y="10" width="2" height="20" fill="rgba(255,255,255,0.06)" transform="rotate(45 61 20)"/><rect x="10" y="60" width="3" height="15" fill="rgba(255,255,255,0.09)" transform="rotate(-30 11.5 67.5)"/></svg>') repeat;
            animation: float 20s linear infinite;
        }
        @keyframes float {
            0% { transform: translateX(0) translateY(0) rotate(0deg); }
            100% { transform: translateX(-20px) translateY(-20px) rotate(360deg); }
        }
        .product-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .download-btn {
            background: #2196F3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .download-btn:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>产品中心Banner图片</h1>
        
        <div class="instructions">
            <h3>📋 说明</h3>
            <p>由于无法直接生成PNG图片文件，请按以下步骤操作：</p>
            <ol>
                <li>截图下方的预览图</li>
                <li>或者使用任何图片编辑软件创建类似的图片</li>
                <li>将图片保存为 <code>product.png</code></li>
                <li>放置到 <code>public/images/banner/</code> 目录下</li>
            </ol>
        </div>

        <h3>🎨 Banner预览</h3>
        <div class="banner-preview">
            <div>
                <div class="product-icon">🔬</div>
                <div>智能测试设备</div>
                <div style="font-size: 16px; margin-top: 10px; opacity: 0.8;">Professional Testing Solutions</div>
            </div>
        </div>

        <div class="instructions">
            <h3>🎯 设计要求</h3>
            <ul>
                <li><strong>尺寸：</strong>建议 800x500 像素或更高分辨率</li>
                <li><strong>主题：</strong>智能测试设备、芯片、电路板、科技感</li>
                <li><strong>风格：</strong>现代、专业、科技感</li>
                <li><strong>颜色：</strong>蓝色系渐变，与网站主题一致</li>
                <li><strong>元素：</strong>可包含芯片、电路、测试仪器等图标</li>
            </ul>
        </div>

        <h3>🖼️ 替代方案</h3>
        <p>如果暂时没有合适的图片，可以：</p>
        <ol>
            <li>复制现有的 <code>jjfa.png</code> 并重命名为 <code>product.png</code></li>
            <li>或者从免费图片网站下载相关图片</li>
            <li>推荐网站：Unsplash, Pexels, Pixabay</li>
        </ol>

        <button class="download-btn" onclick="copyExistingImage()">
            📋 复制现有图片的命令
        </button>
        
        <button class="download-btn" onclick="showImageSources()">
            🔗 推荐图片来源
        </button>

        <div id="command-output" style="background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; display: none;">
            <h4>复制命令：</h4>
            <code>copy public\images\banner\jjfa.png public\images\banner\product.png</code>
            <p><small>在项目根目录下运行此命令（Windows）</small></p>
            <p><small>Linux/Mac: cp public/images/banner/jjfa.png public/images/banner/product.png</small></p>
        </div>

        <div id="image-sources" style="background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; display: none;">
            <h4>推荐搜索关键词：</h4>
            <ul>
                <li>semiconductor testing</li>
                <li>chip testing equipment</li>
                <li>electronic testing</li>
                <li>circuit board testing</li>
                <li>laboratory equipment</li>
                <li>technology background</li>
            </ul>
            <h4>免费图片网站：</h4>
            <ul>
                <li><a href="https://unsplash.com" target="_blank">Unsplash</a></li>
                <li><a href="https://www.pexels.com" target="_blank">Pexels</a></li>
                <li><a href="https://pixabay.com" target="_blank">Pixabay</a></li>
            </ul>
        </div>
    </div>

    <script>
        function copyExistingImage() {
            document.getElementById('command-output').style.display = 'block';
            document.getElementById('image-sources').style.display = 'none';
        }

        function showImageSources() {
            document.getElementById('image-sources').style.display = 'block';
            document.getElementById('command-output').style.display = 'none';
        }
    </script>
</body>
</html>
