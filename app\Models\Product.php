<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'description',
        'content',
        'image',
        'images',
        'pdfs',
        'specifications',
        'price',
        'model',
        'brand',
        'sort_order',
        'is_featured',
        'is_published',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'images' => 'array',
        'pdfs' => 'array',
        'specifications' => 'array',
        'price' => 'decimal:2',
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
    ];

    /**
     * 自动生成slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 获取已发布的产品
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * 获取推荐产品
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 按排序获取（推荐产品优先）
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('is_featured', 'desc')
                    ->orderBy('sort_order', 'asc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * 获取产品状态
     */
    public function getStatusAttribute()
    {
        return $this->is_published ? 'published' : 'draft';
    }

    /**
     * 获取产品主图URL
     */
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return config('app.url') . '/storage/' . $this->image;
        }

        return asset('images/default-product.png');
    }

    /**
     * 获取产品图片集URLs
     */
    public function getImagesUrlsAttribute()
    {
        if (!$this->images || !is_array($this->images)) {
            return [];
        }

        return collect($this->images)->map(function ($image) {
            if (\Storage::disk('public')->exists($image)) {
                return \Storage::url($image);
            }
            return null;
        })->filter()->values()->toArray();
    }

    /**
     * 获取产品摘要
     */
    public function getExcerptAttribute()
    {
        if ($this->description) {
            return \Str::limit(strip_tags($this->description), 150);
        }

        return \Str::limit(strip_tags($this->content ?? ''), 150);
    }
}
