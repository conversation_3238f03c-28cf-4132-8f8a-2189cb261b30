<?php
// 图片上传诊断脚本

echo "<h1>图片上传诊断</h1>";

// 1. 检查PHP配置
echo "<h2>1. PHP配置检查</h2>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>配置项</th><th>当前值</th><th>建议值</th><th>状态</th></tr>";

$configs = [
    'file_uploads' => ['on', 'On'],
    'upload_max_filesize' => ['2M', '>=2M'],
    'post_max_size' => ['8M', '>=8M'],
    'max_file_uploads' => ['20', '>=20'],
    'memory_limit' => ['128M', '>=128M'],
    'max_execution_time' => ['30', '>=30'],
];

foreach ($configs as $key => $expected) {
    $value = ini_get($key);
    $status = '✅';
    
    if ($key === 'file_uploads') {
        $status = ($value == '1' || strtolower($value) === 'on') ? '✅' : '❌';
    } elseif (in_array($key, ['upload_max_filesize', 'post_max_size', 'memory_limit'])) {
        $bytes = return_bytes($value);
        $expectedBytes = return_bytes($expected[1]);
        $status = ($bytes >= $expectedBytes) ? '✅' : '❌';
    }
    
    echo "<tr><td>$key</td><td>$value</td><td>{$expected[1]}</td><td>$status</td></tr>";
}
echo "</table>";

// 2. 检查目录权限
echo "<h2>2. 目录权限检查</h2>";
$directories = [
    'storage/app/public' => realpath(__DIR__ . '/../storage/app/public'),
    'storage/app/public/uploads' => realpath(__DIR__ . '/../storage/app/public/uploads'),
    'storage/app/public/uploads/editor' => realpath(__DIR__ . '/../storage/app/public/uploads/editor'),
    'storage/app/public/products' => realpath(__DIR__ . '/../storage/app/public/products'),
    'public/storage' => realpath(__DIR__ . '/storage'),
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>目录</th><th>路径</th><th>存在</th><th>可写</th><th>权限</th></tr>";

foreach ($directories as $name => $path) {
    $exists = $path && file_exists($path) ? '✅' : '❌';
    $writable = $path && is_writable($path) ? '✅' : '❌';
    $perms = $path && file_exists($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A';
    
    echo "<tr><td>$name</td><td>$path</td><td>$exists</td><td>$writable</td><td>$perms</td></tr>";
}
echo "</table>";

// 3. 检查符号链接
echo "<h2>3. 符号链接检查</h2>";
$storageLink = __DIR__ . '/storage';
$storageTarget = realpath(__DIR__ . '/../storage/app/public');

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>项目</th><th>状态</th><th>详情</th></tr>";

if (is_link($storageLink)) {
    $linkTarget = readlink($storageLink);
    $isCorrect = realpath($linkTarget) === $storageTarget;
    echo "<tr><td>符号链接存在</td><td>✅</td><td>链接到: $linkTarget</td></tr>";
    echo "<tr><td>链接正确</td><td>" . ($isCorrect ? '✅' : '❌') . "</td><td>目标应为: $storageTarget</td></tr>";
} else {
    echo "<tr><td>符号链接存在</td><td>❌</td><td>需要运行: php artisan storage:link</td></tr>";
}

// 4. 测试文件上传
echo "<h2>4. 文件上传测试</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])) {
    echo "<h3>上传结果:</h3>";
    
    $file = $_FILES['test_image'];
    echo "<pre>";
    echo "文件信息:\n";
    print_r($file);
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $uploadDir = __DIR__ . '/../storage/app/public/uploads/test/';
        
        // 创建目录
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filename = time() . '_' . $file['name'];
        $destination = $uploadDir . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $destination)) {
            echo "\n✅ 上传成功!\n";
            echo "文件保存到: $destination\n";
            echo "访问URL: " . $_SERVER['HTTP_HOST'] . "/storage/uploads/test/$filename\n";
        } else {
            echo "\n❌ 移动文件失败!\n";
        }
    } else {
        echo "\n❌ 上传错误: " . $file['error'] . "\n";
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'file size exceeds upload_max_filesize',
            UPLOAD_ERR_FORM_SIZE => 'file size exceeds MAX_FILE_SIZE',
            UPLOAD_ERR_PARTIAL => 'file was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'no file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'file upload stopped by extension',
        ];
        echo "错误说明: " . ($errors[$file['error']] ?? 'unknown error') . "\n";
    }
    echo "</pre>";
} else {
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<input type="file" name="test_image" accept="image/*" required>';
    echo '<button type="submit">测试上传</button>';
    echo '</form>';
}

// 5. 环境信息
echo "<h2>5. 环境信息</h2>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>项目</th><th>值</th></tr>";
echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>操作系统</td><td>" . PHP_OS . "</td></tr>";
echo "<tr><td>Web服务器</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>文档根目录</td><td>" . $_SERVER['DOCUMENT_ROOT'] . "</td></tr>";
echo "<tr><td>当前脚本路径</td><td>" . __FILE__ . "</td></tr>";
echo "</table>";

// 辅助函数
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
?>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h2 { color: #333; margin-top: 30px; }
</style>
