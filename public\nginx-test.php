<?php
echo "<h1>Nginx配置诊断</h1>";

echo "<h2>服务器信息</h2>";
echo "<p>服务器软件: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>请求方法: " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p>请求URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>脚本名称: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>查询字符串: " . ($_SERVER['QUERY_STRING'] ?? '无') . "</p>";

echo "<h2>测试链接</h2>";
echo "<p>点击下面的链接测试URL重写是否工作：</p>";
echo "<ul>";
echo "<li><a href='/products' target='_blank'>产品页面 (/products)</a></li>";
echo "<li><a href='/about' target='_blank'>关于我们 (/about)</a></li>";
echo "<li><a href='/solutions' target='_blank'>解决方案 (/solutions)</a></li>";
echo "</ul>";

echo "<h2>绕过URL重写的链接</h2>";
echo "<p>如果上面的链接不工作，试试这些：</p>";
echo "<ul>";
echo "<li><a href='/index.php/products' target='_blank'>产品页面 (/index.php/products)</a></li>";
echo "<li><a href='/index.php/about' target='_blank'>关于我们 (/index.php/about)</a></li>";
echo "<li><a href='/index.php/solutions' target='_blank'>解决方案 (/index.php/solutions)</a></li>";
echo "</ul>";

echo "<h2>诊断结果</h2>";
echo "<p>如果第一组链接工作 = URL重写正常</p>";
echo "<p>如果只有第二组链接工作 = 需要修复URL重写配置</p>";
echo "<p>如果都不工作 = Laravel配置问题</p>";
?>
