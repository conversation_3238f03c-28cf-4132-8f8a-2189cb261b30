<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AdminUserController extends Controller
{
    /**
     * 显示管理员列表
     */
    public function index(Request $request)
    {
        // 检查权限：只有超级管理员可以访问
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        $query = Admin::query();

        // 搜索功能
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // 角色筛选
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.users.index', compact('users'));
    }

    /**
     * 显示创建管理员表单
     */
    public function create()
    {
        // 检查权限：只有超级管理员可以创建用户
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        return view('admin.users.create');
    }

    /**
     * 存储新管理员
     */
    public function store(Request $request)
    {
        // 检查权限：只有超级管理员可以创建用户
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:admins',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:super_admin,admin,operator',
            'status' => 'required|in:active,inactive'
        ]);

        Admin::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'status' => $request->status,
        ]);

        return redirect()->route('admin.admin-users.index')
                        ->with('success', '管理员创建成功！');
    }

    /**
     * 显示管理员详情
     */
    public function show(Admin $adminUser)
    {
        // 检查权限：只有超级管理员可以查看用户详情
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        return view('admin.users.show', ['user' => $adminUser]);
    }

    /**
     * 显示编辑管理员表单
     */
    public function edit(Admin $adminUser)
    {
        // 检查权限：只有超级管理员可以编辑用户
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        return view('admin.users.edit', ['user' => $adminUser]);
    }

    /**
     * 更新管理员信息
     */
    public function update(Request $request, Admin $adminUser)
    {
        // 检查权限：只有超级管理员可以编辑用户
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('admins')->ignore($adminUser->id)],
            'role' => 'required|in:super_admin,admin,operator',
            'status' => 'required|in:active,inactive'
        ]);

        $adminUser->update([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'status' => $request->status,
        ]);

        return redirect()->route('admin.admin-users.index')
                        ->with('success', '管理员信息更新成功！');
    }

    /**
     * 删除管理员
     */
    public function destroy(Admin $adminUser)
    {
        // 检查权限：只有超级管理员可以删除用户
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        // 不能删除自己
        if ($adminUser->id === Auth::guard('admin')->id()) {
            return redirect()->route('admin.admin-users.index')
                            ->with('error', '不能删除自己的账号！');
        }

        $adminUser->delete();

        return redirect()->route('admin.admin-users.index')
                        ->with('success', '管理员删除成功！');
    }

    /**
     * 显示修改密码表单
     */
    public function showChangePasswordForm(Admin $adminUser = null)
    {
        // 如果没有指定用户，则修改自己的密码
        if (!$adminUser) {
            $adminUser = Auth::guard('admin')->user();
        } else {
            // 只有超级管理员可以修改其他用户的密码
            if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
                abort(403, '您没有权限修改其他用户的密码');
            }
        }

        return view('admin.users.change-password', ['user' => $adminUser]);
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request, Admin $adminUser = null)
    {
        // 如果没有指定用户，则修改自己的密码
        if (!$adminUser) {
            $adminUser = Auth::guard('admin')->user();
            $isOwnPassword = true;
        } else {
            // 只有超级管理员可以修改其他用户的密码
            if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
                abort(403, '您没有权限修改其他用户的密码');
            }
            $isOwnPassword = false;
        }

        // 验证规则
        $rules = [
            'new_password' => 'required|string|min:8|confirmed',
        ];

        // 如果是修改自己的密码，需要验证当前密码
        if ($isOwnPassword) {
            $rules['current_password'] = 'required|string';
        }

        $request->validate($rules);

        // 如果是修改自己的密码，验证当前密码
        if ($isOwnPassword && !Hash::check($request->current_password, $adminUser->password)) {
            return back()->withErrors(['current_password' => '当前密码不正确']);
        }

        // 更新密码
        $adminUser->update([
            'password' => Hash::make($request->new_password)
        ]);

        $message = $isOwnPassword ? '密码修改成功！' : "管理员 {$adminUser->name} 的密码修改成功！";

        return redirect()->route('admin.admin-users.index')
                        ->with('success', $message);
    }

    /**
     * 切换用户状态
     */
    public function toggleStatus(Admin $adminUser)
    {
        // 检查权限：只有超级管理员可以切换用户状态
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            return response()->json(['success' => false, 'message' => '您没有权限执行此操作']);
        }

        // 不能禁用自己
        if ($adminUser->id === Auth::guard('admin')->id()) {
            return response()->json(['success' => false, 'message' => '不能禁用自己的账号！']);
        }

        $newStatus = $adminUser->status === 'active' ? 'inactive' : 'active';
        $adminUser->update(['status' => $newStatus]);

        $statusText = $newStatus === 'active' ? '启用' : '禁用';
        return response()->json(['success' => true, 'message' => "管理员已{$statusText}！"]);
    }
}
