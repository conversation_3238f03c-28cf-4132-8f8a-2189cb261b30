@extends('admin.layouts.app')

@section('title', '修改密码')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">修改密码</h1>
                    <p class="text-muted mb-0">
                        @if($user->id === Auth::guard('admin')->id())
                            修改您的登录密码
                        @else
                            修改用户 <strong>{{ $user->name }}</strong> 的密码
                        @endif
                    </p>
                </div>
                <a href="{{ route('admin.admin-users.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回列表
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>
                                @if($user->id === auth()->id())
                                    修改我的密码
                                @else
                                    修改用户密码
                                @endif
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 用户信息 -->
                            @if($user->id !== auth()->id())
                                <div class="alert alert-info">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            {{ substr($user->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-1">{{ $user->name }}</h6>
                                            <small class="text-muted">{{ $user->email }}</small>
                                            <br>
                                            <span class="badge bg-primary">{{ $user->getRoleDisplayName() }}</span>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('admin.admin-users.change-password', $user) }}">
                                @csrf
                                @method('PUT')
                                
                                <!-- 当前密码（仅修改自己密码时需要） -->
                                @if($user->id === Auth::guard('admin')->id())
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">当前密码 <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                                   id="current_password" name="current_password" required>
                                            <button class="btn btn-outline-secondary" type="button" id="toggleCurrentPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        @error('current_password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                @endif

                                <!-- 新密码 -->
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">新密码 <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control @error('new_password') is-invalid @enderror" 
                                               id="new_password" name="new_password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    @error('new_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">密码至少8位字符，建议包含大小写字母、数字和特殊字符</small>
                                </div>

                                <!-- 确认新密码 -->
                                <div class="mb-3">
                                    <label for="new_password_confirmation" class="form-label">确认新密码 <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" 
                                               id="new_password_confirmation" name="new_password_confirmation" required>
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-match-feedback mt-1"></div>
                                </div>

                                <!-- 密码安全提示 -->
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-shield-alt me-2"></i>密码安全建议
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>使用至少8位字符</li>
                                        <li>包含大写和小写字母</li>
                                        <li>包含数字和特殊字符</li>
                                        <li>避免使用常见密码或个人信息</li>
                                        <li>定期更换密码</li>
                                    </ul>
                                </div>

                                @if($user->id !== Auth::guard('admin')->id())
                                    <div class="alert alert-danger">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-exclamation-triangle me-2"></i>重要提醒
                                        </h6>
                                        <p class="mb-0 small">
                                            您正在修改其他用户的密码。修改后，该用户需要使用新密码登录。
                                            请确保将新密码安全地告知用户。
                                        </p>
                                    </div>
                                @endif

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.admin-users.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>取消
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="fas fa-save me-2"></i>修改密码
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.password-strength {
    margin-top: 5px;
}

.password-match-feedback {
    font-size: 0.875rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 密码显示/隐藏切换
    const toggleButtons = [
        { btn: 'toggleCurrentPassword', input: 'current_password' },
        { btn: 'toggleNewPassword', input: 'new_password' },
        { btn: 'toggleConfirmPassword', input: 'new_password_confirmation' }
    ];

    toggleButtons.forEach(item => {
        const btn = document.getElementById(item.btn);
        const input = document.getElementById(item.input);
        
        if (btn && input) {
            btn.addEventListener('click', function() {
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }
    });

    // 新密码强度检查
    const newPasswordInput = document.getElementById('new_password');
    newPasswordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = checkPasswordStrength(password);
        
        // 移除之前的提示
        const existingFeedback = this.parentNode.parentNode.querySelector('.password-strength');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        if (password.length > 0) {
            const strengthDiv = document.createElement('div');
            strengthDiv.className = `password-strength small text-${strength.color}`;
            strengthDiv.innerHTML = `<i class="fas fa-${strength.icon} me-1"></i>${strength.text}`;
            this.parentNode.parentNode.appendChild(strengthDiv);
        }
        
        // 检查密码匹配
        checkPasswordMatch();
    });

    // 确认密码匹配检查
    const confirmPasswordInput = document.getElementById('new_password_confirmation');
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);

    function checkPasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        const feedback = document.querySelector('.password-match-feedback');
        const submitBtn = document.getElementById('submitBtn');
        
        if (confirmPassword.length > 0) {
            if (newPassword === confirmPassword) {
                feedback.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>密码匹配</small>';
                submitBtn.disabled = false;
            } else {
                feedback.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>密码不匹配</small>';
                submitBtn.disabled = true;
            }
        } else {
            feedback.innerHTML = '';
            submitBtn.disabled = false;
        }
    }

    // 表单提交确认
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const isOwnPassword = {{ $user->id === Auth::guard('admin')->id() ? 'true' : 'false' }};
        const userName = '{{ $user->name }}';
        
        if (!isOwnPassword) {
            const confirmed = confirm(`确定要修改用户 ${userName} 的密码吗？\n\n修改后该用户需要使用新密码登录。`);
            if (!confirmed) {
                e.preventDefault();
            }
        }
    });
});

function checkPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (password.match(/[a-z]/)) score++;
    if (password.match(/[A-Z]/)) score++;
    if (password.match(/[0-9]/)) score++;
    if (password.match(/[^a-zA-Z0-9]/)) score++;
    
    if (score < 2) {
        return { color: 'danger', icon: 'times-circle', text: '密码强度：弱' };
    } else if (score < 4) {
        return { color: 'warning', icon: 'exclamation-circle', text: '密码强度：中等' };
    } else {
        return { color: 'success', icon: 'check-circle', text: '密码强度：强' };
    }
}
</script>
@endpush
