<?php
// 简单的产品数据调试页面
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>产品图片调试</h1>";

try {
    // 获取产品数据
    $products = \App\Models\Product::with('category')->published()->take(5)->get();
    
    echo "<h2>产品数据检查</h2>";
    echo "<table border='1' cellpadding='10' cellspacing='0'>";
    echo "<tr><th>ID</th><th>名称</th><th>图片路径</th><th>图片URL</th><th>文件存在</th><th>预览</th></tr>";
    
    foreach ($products as $product) {
        $imagePath = $product->image;
        $imageUrl = $product->image_url;
        $fullPath = storage_path('app/public/' . $imagePath);
        $fileExists = $imagePath && file_exists($fullPath) ? '是' : '否';
        
        echo "<tr>";
        echo "<td>{$product->id}</td>";
        echo "<td>{$product->name}</td>";
        echo "<td>" . ($imagePath ?: '无') . "</td>";
        echo "<td>{$imageUrl}</td>";
        echo "<td>{$fileExists}</td>";
        echo "<td>";
        if ($imageUrl) {
            echo "<img src='{$imageUrl}' style='max-width: 100px; max-height: 100px;' onerror='this.style.display=\"none\"'>";
        }
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h2>存储配置检查</h2>";
    echo "<p>存储磁盘: " . config('filesystems.default') . "</p>";
    echo "<p>Public磁盘根目录: " . config('filesystems.disks.public.root') . "</p>";
    echo "<p>Public磁盘URL: " . config('filesystems.disks.public.url') . "</p>";
    echo "<p>APP_URL: " . config('app.url') . "</p>";
    
    echo "<h2>存储目录检查</h2>";
    $storagePublicPath = storage_path('app/public');
    $publicStoragePath = public_path('storage');
    
    echo "<p>Storage public目录: {$storagePublicPath} - " . (is_dir($storagePublicPath) ? '存在' : '不存在') . "</p>";
    echo "<p>Public storage链接: {$publicStoragePath} - " . (is_link($publicStoragePath) || is_dir($publicStoragePath) ? '存在' : '不存在') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
