@extends('admin.layouts.app')

@section('title', '编辑产品')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.products.index') }}">产品管理</a></li>
    <li class="breadcrumb-item active">编辑产品</li>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-edit me-2"></i>编辑产品
    </h1>
    <p class="text-muted">编辑产品：{{ $product->name }}</p>
</div>

<form action="{{ route('admin.products.update', $product) }}" method="POST" enctype="multipart/form-data" onsubmit="return validateForm()">
    @csrf
    @method('PUT')

    <div class="row">
        <div class="col-lg-8">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name', $product->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">产品别名</label>
                                <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                       id="slug" name="slug" value="{{ old('slug', $product->slug) }}"
                                       placeholder="留空自动生成">
                                @error('slug')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">产品分类 <span class="text-danger">*</span></label>
                                <select class="form-select @error('category_id') is-invalid @enderror"
                                        id="category_id" name="category_id" required>
                                    <option value="">选择分类</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">产品型号</label>
                                <input type="text" class="form-control @error('model') is-invalid @enderror"
                                       id="model" name="model" value="{{ old('model', $product->model) }}">
                                @error('model')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="brand" class="form-label">产品品牌</label>
                        <input type="text" class="form-control @error('brand') is-invalid @enderror"
                               id="brand" name="brand" value="{{ old('brand', $product->brand) }}">
                        @error('brand')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">产品描述</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3">{{ old('description', $product->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">产品详情</label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content" name="content" rows="15">{{ old('content', $product->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">支持富文本编辑，可插入图片</div>
                    </div>
                </div>
            </div>

            <!-- 产品图片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>产品图片
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="image" class="form-label">主图</label>
                        @if($product->image)
                            <div class="mb-2">
                                <img src="{{ Storage::url($product->image) }}" alt="{{ $product->name }}"
                                     class="img-thumbnail" style="max-width: 200px;">
                                <div class="form-text">当前主图</div>
                            </div>
                        @endif
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" accept="image/*" onchange="uploadMainImage(this)">
                        <input type="hidden" name="image" id="image_path" value="{{ $product->image }}">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">支持 JPEG、PNG、JPG、GIF 格式，最大 2MB</div>
                        <div id="image-preview" class="mt-2" style="display: none;">
                            <img id="preview-img" src="" alt="预览图" class="img-thumbnail" style="max-width: 200px;">
                            <div class="mt-1">
                                <small class="text-success">✅ 图片已上传</small>
                            </div>
                        </div>
                        <div id="upload-progress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">上传中...</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="images" class="form-label">产品图片集</label>
                        @if($product->images && count($product->images) > 0)
                            <div class="mb-2">
                                <div class="row">
                                    @foreach($product->images as $image)
                                        <div class="col-md-3 mb-2">
                                            <img src="{{ Storage::url($image) }}" alt="{{ $product->name }}"
                                                 class="img-thumbnail w-100">
                                        </div>
                                    @endforeach
                                </div>
                                <div class="form-text">当前图片集</div>
                            </div>
                        @endif
                        <input type="file" class="form-control @error('images.*') is-invalid @enderror"
                               id="images" accept="image/*" multiple onchange="uploadMultipleImages(this)">
                        <input type="hidden" name="images" id="images_paths" value="{{ $product->images ? json_encode($product->images) : '' }}">
                        @error('images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">可选择多张图片，选择新图片将替换当前图片集</div>
                        <div id="images-preview" class="mt-2" style="display: none;">
                            <div class="row" id="images-container"></div>
                        </div>
                        <div id="images-upload-progress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">上传中...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品规格 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ul me-2"></i>产品规格
                    </h5>
                </div>
                <div class="card-body">
                    <div id="specifications">
                        @if($product->specifications && count($product->specifications) > 0)
                            @foreach($product->specifications as $name => $value)
                                <div class="row mb-2 spec-row">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="spec_names[]"
                                               placeholder="规格名称" value="{{ $name }}">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="spec_values[]"
                                               placeholder="规格值" value="{{ $value }}">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger remove-spec">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="row mb-2 spec-row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="spec_names[]" placeholder="规格名称">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control" name="spec_values[]" placeholder="规格值">
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-outline-danger remove-spec">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        @endif
                    </div>
                    <button type="button" class="btn btn-outline-primary" id="add-spec">
                        <i class="fas fa-plus me-2"></i>添加规格
                    </button>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 发布设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>发布设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">排序</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $product->sort_order) }}" min="0">
                        @error('sort_order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">数字越小排序越靠前</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_published" name="is_published"
                                   value="1" {{ old('is_published', $product->is_published) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                发布产品
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                   value="1" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_featured">
                                推荐产品
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>产品信息
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>ID：</strong></td>
                            <td>{{ $product->id }}</td>
                        </tr>
                        <tr>
                            <td><strong>创建时间：</strong></td>
                            <td>{{ $product->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>更新时间：</strong></td>
                            <td>{{ $product->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>状态：</strong></td>
                            <td>
                                @if($product->is_published)
                                    <span class="badge bg-success">已发布</span>
                                @else
                                    <span class="badge bg-secondary">草稿</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>更新产品
                        </button>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        <a href="{{ route('admin.products.show', $product) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>查看详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<!-- TinyMCE CSS -->
<style>
    .tox-tinymce {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
    }
    .tox-editor-header {
        border-bottom: 1px solid #ced4da !important;
    }
</style>
@endpush

@push('scripts')
<!-- TinyMCE JS -->
<link rel="stylesheet" href="{{ asset('assets/css/simple-editor.css') }}">
<script src="{{ asset('assets/js/simple-editor.js') }}"></script>
<script>
    // 初始化简化富文本编辑器
    let contentEditor;
    document.addEventListener('DOMContentLoaded', function() {
        contentEditor = initSimpleEditor('#content', {
            height: '400px',
            placeholder: '请输入产品详细内容...',
            uploadUrl: '{{ route("admin.upload.image") }}',
            csrfToken: '{{ csrf_token() }}'
        });

        // 显示现有主图预览
        @if($product->image)
            const existingImageUrl = '{{ Storage::url($product->image) }}';
            document.getElementById('preview-img').src = existingImageUrl;
            document.getElementById('image-preview').style.display = 'block';
        @endif

        // 显示现有多图预览
        @if($product->images && count($product->images) > 0)
            const existingImages = @json($product->images);
            const container = document.getElementById('images-container');
            container.innerHTML = '';

            existingImages.forEach((imagePath, index) => {
                const imageUrl = '{{ asset("storage") }}/' + imagePath;
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = `
                    <img src="${imageUrl}" alt="图片 ${index + 1}" class="img-thumbnail w-100">
                    <div class="mt-1">
                        <small class="text-info">✅ 现有图片</small>
                    </div>
                `;
                container.appendChild(col);
            });

            document.getElementById('images-preview').style.display = 'block';
        @endif
    });

    // 表单验证函数
    function validateForm() {
        let isValid = true;
        let errorMessages = [];

        // 验证产品名称
        const name = document.getElementById('name').value.trim();
        if (!name) {
            errorMessages.push('请输入产品名称');
            document.getElementById('name').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('name').classList.remove('is-invalid');
        }

        // 验证产品分类
        const categoryId = document.getElementById('category_id').value;
        if (!categoryId) {
            errorMessages.push('请选择产品分类');
            document.getElementById('category_id').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('category_id').classList.remove('is-invalid');
        }

        // 内容已通过编辑器自动同步到textarea

        // 显示错误信息
        if (!isValid) {
            alert('请完善以下信息：\n' + errorMessages.join('\n'));
            return false;
        }

        return true;
    }

    // TinyMCE已替换为简化富文本编辑器

    // 上传主图
    function uploadMainImage(input) {
        if (!input.files || input.files.length === 0) return;

        const file = input.files[0];
        const formData = new FormData();
        formData.append('image', file);
        formData.append('_token', '{{ csrf_token() }}');

        // 显示上传进度
        document.getElementById('upload-progress').style.display = 'block';
        document.getElementById('image-preview').style.display = 'none';

        fetch('{{ route("admin.upload.image") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            document.getElementById('upload-progress').style.display = 'none';

            if (result.success) {
                // 保存图片路径到隐藏字段
                document.getElementById('image_path').value = result.path;

                // 显示预览
                document.getElementById('preview-img').src = result.url;
                document.getElementById('image-preview').style.display = 'block';
            } else {
                alert('图片上传失败: ' + result.message);
            }
        })
        .catch(error => {
            document.getElementById('upload-progress').style.display = 'none';
            alert('上传错误: ' + error.message);
        });
    }

    // 自动生成别名
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');

        if (document.getElementById('slug').value === '' || document.getElementById('slug').value === '{{ $product->slug }}') {
            document.getElementById('slug').value = slug;
        }
    });

    // 添加规格
    document.getElementById('add-spec').addEventListener('click', function() {
        const specificationsDiv = document.getElementById('specifications');
        const newRow = document.createElement('div');
        newRow.className = 'row mb-2 spec-row';
        newRow.innerHTML = `
            <div class="col-md-4">
                <input type="text" class="form-control" name="spec_names[]" placeholder="规格名称">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" name="spec_values[]" placeholder="规格值">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger remove-spec">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        specificationsDiv.appendChild(newRow);
    });

    // 删除规格
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-spec')) {
            const row = e.target.closest('.spec-row');
            if (document.querySelectorAll('.spec-row').length > 1) {
                row.remove();
            }
        }
    });

    // 上传多图
    function uploadMultipleImages(input) {
        if (!input.files || input.files.length === 0) return;

        const files = Array.from(input.files);
        const uploadedPaths = [];
        let uploadedCount = 0;

        // 显示上传进度
        document.getElementById('images-upload-progress').style.display = 'block';
        document.getElementById('images-preview').style.display = 'none';

        const container = document.getElementById('images-container');
        container.innerHTML = '';

        files.forEach((file, index) => {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('_token', '{{ csrf_token() }}');

            fetch('{{ route("admin.upload.image") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                uploadedCount++;

                if (result.success) {
                    uploadedPaths.push(result.path);

                    // 显示预览
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <img src="${result.url}" alt="图片 ${index + 1}" class="img-thumbnail w-100">
                        <div class="mt-1">
                            <small class="text-success">✅ 已上传</small>
                        </div>
                    `;
                    container.appendChild(col);
                } else {
                    // 显示错误
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <div class="alert alert-danger p-2">
                            <small>图片 ${index + 1} 上传失败</small>
                        </div>
                    `;
                    container.appendChild(col);
                }

                // 更新进度
                const progress = (uploadedCount / files.length) * 100;
                document.querySelector('#images-upload-progress .progress-bar').style.width = progress + '%';

                if (uploadedCount === files.length) {
                    document.getElementById('images-upload-progress').style.display = 'none';
                    document.getElementById('images-preview').style.display = 'block';
                    document.getElementById('images_paths').value = JSON.stringify(uploadedPaths);
                }
            })
            .catch(error => {
                uploadedCount++;
                console.error('图片上传错误:', error);

                // 显示错误
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = `
                    <div class="alert alert-danger p-2">
                        <small>图片 ${index + 1} 上传失败</small>
                    </div>
                `;
                container.appendChild(col);

                // 更新进度
                const progress = (uploadedCount / files.length) * 100;
                document.querySelector('#images-upload-progress .progress-bar').style.width = progress + '%';

                if (uploadedCount === files.length) {
                    document.getElementById('images-upload-progress').style.display = 'none';
                    document.getElementById('images-preview').style.display = 'block';
                    document.getElementById('images_paths').value = JSON.stringify(uploadedPaths);
                }
            });
        });
    }
</script>
@endpush
