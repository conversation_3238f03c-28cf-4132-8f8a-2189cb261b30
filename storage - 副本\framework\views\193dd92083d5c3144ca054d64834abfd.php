<?php $__env->startSection('title', '新闻管理'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item active">新闻管理</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-newspaper me-2"></i>新闻管理
        </h1>
        <p class="text-muted">管理网站新闻资讯</p>
    </div>
    <div class="d-flex gap-2">
        <a href="<?php echo e(route('admin.news-categories.index')); ?>" class="btn btn-outline-primary">
            <i class="fas fa-folder me-2"></i>分类管理
        </a>
        <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>添加新闻
        </a>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.news.index')); ?>">
            <div class="row g-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search"
                           placeholder="搜索标题、摘要、作者..."
                           value="<?php echo e(request('search')); ?>">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="category">
                        <option value="">所有分类</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>"
                                    <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">所有状态</option>
                        <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>已发布</option>
                        <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>草稿</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('admin.news.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>新闻列表
        </h5>
    </div>
    <div class="card-body">
        <?php if($news->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>新闻信息</th>
                            <th>分类</th>
                            <th>作者/来源</th>
                            <th>浏览量</th>
                            <th>状态</th>
                            <th>发布时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($item->id); ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($item->image): ?>
                                            <img src="<?php echo e(Storage::url($item->image)); ?>"
                                                 alt="<?php echo e($item->title); ?>"
                                                 class="rounded me-3"
                                                 style="width: 60px; height: 60px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo e(Str::limit($item->title, 40)); ?></strong>
                                            <?php if($item->summary): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($item->summary, 60)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if($item->category): ?>
                                        <span class="badge" style="background-color: <?php echo e($item->category->color); ?>;">
                                            <?php echo e($item->category->name); ?>

                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">未分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($item->author): ?>
                                        <div><strong><?php echo e($item->author); ?></strong></div>
                                    <?php endif; ?>
                                    <?php if($item->source): ?>
                                        <small class="text-muted"><?php echo e($item->source); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($item->views); ?></span>
                                </td>
                                <td>
                                    <div>
                                        <?php if($item->is_published): ?>
                                            <span class="badge bg-success">已发布</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">草稿</span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if($item->is_featured): ?>
                                        <div class="mt-1">
                                            <span class="badge bg-warning">推荐</span>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($item->published_at): ?>
                                        <?php echo e($item->published_at->format('Y-m-d')); ?>

                                    <?php else: ?>
                                        <span class="text-muted">未发布</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.news.show', $item)); ?>"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.news.edit', $item)); ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.news.destroy', $item)); ?>"
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('确定要删除这条新闻吗？')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center">
                <?php echo e($news->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无新闻</h4>
                <p class="text-muted">点击上方按钮添加第一条新闻</p>
                <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加新闻
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/news/index.blade.php ENDPATH**/ ?>