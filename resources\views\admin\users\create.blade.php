@extends('admin.layouts.app')

@section('title', '添加用户')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">添加用户</h1>
                    <p class="text-muted mb-0">创建新的系统用户账号</p>
                </div>
                <a href="{{ route('admin.admin-users.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回列表
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-plus me-2"></i>用户信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.admin-users.store') }}">
                                @csrf
                                
                                <div class="row">
                                    <!-- 基本信息 -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email') }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                                       id="password" name="password" required>
                                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            @error('password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">密码至少8位字符</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_confirmation" class="form-label">确认密码 <span class="text-danger">*</span></label>
                                            <input type="password" class="form-control" 
                                                   id="password_confirmation" name="password_confirmation" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="role" class="form-label">角色 <span class="text-danger">*</span></label>
                                            <select class="form-select @error('role') is-invalid @enderror"
                                                    id="role" name="role" required>
                                                <option value="">请选择角色</option>
                                                <option value="super_admin" {{ old('role') === 'super_admin' ? 'selected' : '' }}>超级管理员</option>
                                                <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>管理员</option>
                                                <option value="operator" {{ old('role') === 'operator' ? 'selected' : '' }}>操作员</option>
                                            </select>
                                            @error('role')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="department" class="form-label">部门</label>
                                            <input type="text" class="form-control @error('department') is-invalid @enderror" 
                                                   id="department" name="department" value="{{ old('department') }}" 
                                                   placeholder="如：技术部、市场部等">
                                            @error('department')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">手机号</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone') }}" 
                                                   placeholder="手机号码">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">账号状态</label>
                                            <select class="form-select @error('status') is-invalid @enderror"
                                                    id="status" name="status" required>
                                                <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>启用</option>
                                                <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>禁用</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">禁用的账号无法登录系统</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 角色权限说明 -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>角色权限说明
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li><strong>超级管理员</strong>：拥有所有权限，可以管理其他用户</li>
                                        <li><strong>管理员</strong>：可以管理内容，但不能管理用户</li>
                                        <li><strong>操作员</strong>：可以编辑内容，权限有限</li>
                                        <li><strong>普通用户</strong>：只能查看内容</li>
                                    </ul>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.admin-users.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>取消
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>创建管理员
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 密码显示/隐藏切换
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // 密码强度检查
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = checkPasswordStrength(password);
        
        // 移除之前的提示
        const existingFeedback = this.parentNode.parentNode.querySelector('.password-strength');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        if (password.length > 0) {
            const strengthDiv = document.createElement('div');
            strengthDiv.className = `password-strength small mt-1 text-${strength.color}`;
            strengthDiv.innerHTML = `<i class="fas fa-${strength.icon} me-1"></i>${strength.text}`;
            this.parentNode.parentNode.appendChild(strengthDiv);
        }
    });
    
    // 角色选择说明
    const roleSelect = document.getElementById('role');
    roleSelect.addEventListener('change', function() {
        const role = this.value;
        const descriptions = {
            'super_admin': '拥有系统最高权限，可以管理所有用户和内容',
            'admin': '可以管理内容和基本设置，但不能管理用户',
            'operator': '可以编辑和发布内容，权限相对有限',
            'user': '只能查看内容，无编辑权限'
        };
        
        // 移除之前的说明
        const existingDesc = this.parentNode.querySelector('.role-description');
        if (existingDesc) {
            existingDesc.remove();
        }
        
        if (role && descriptions[role]) {
            const descDiv = document.createElement('div');
            descDiv.className = 'role-description small text-muted mt-1';
            descDiv.textContent = descriptions[role];
            this.parentNode.appendChild(descDiv);
        }
    });
});

function checkPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (password.match(/[a-z]/)) score++;
    if (password.match(/[A-Z]/)) score++;
    if (password.match(/[0-9]/)) score++;
    if (password.match(/[^a-zA-Z0-9]/)) score++;
    
    if (score < 2) {
        return { color: 'danger', icon: 'times-circle', text: '密码强度：弱' };
    } else if (score < 4) {
        return { color: 'warning', icon: 'exclamation-circle', text: '密码强度：中等' };
    } else {
        return { color: 'success', icon: 'check-circle', text: '密码强度：强' };
    }
}
</script>
@endpush
