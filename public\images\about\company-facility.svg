<svg width="500" height="350" viewBox="0 0 500 350" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="equipmentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="500" height="350" fill="url(#bgGradient)"/>
  
  <!-- 地面 -->
  <rect x="0" y="280" width="500" height="70" fill="#e5e7eb"/>
  
  <!-- 实验室墙面 -->
  <rect x="0" y="0" width="500" height="280" fill="#f9fafb"/>
  
  <!-- 天花板灯光 -->
  <rect x="50" y="20" width="80" height="8" rx="4" fill="#fbbf24" opacity="0.8"/>
  <rect x="200" y="20" width="80" height="8" rx="4" fill="#fbbf24" opacity="0.8"/>
  <rect x="350" y="20" width="80" height="8" rx="4" fill="#fbbf24" opacity="0.8"/>
  
  <!-- 主要测试设备 -->
  <g filter="url(#shadow)">
    <!-- 测试机柜 -->
    <rect x="80" y="120" width="120" height="160" rx="8" fill="url(#equipmentGradient)"/>
    <rect x="90" y="130" width="100" height="80" rx="4" fill="url(#screenGradient)"/>
    
    <!-- 屏幕显示内容 -->
    <rect x="95" y="135" width="90" height="70" rx="2" fill="#000000"/>
    <text x="140" y="155" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="8" font-weight="bold">TESTING</text>
    <text x="140" y="170" text-anchor="middle" fill="#fbbf24" font-family="Arial" font-size="6">温度: -40°C</text>
    <text x="140" y="185" text-anchor="middle" fill="#ef4444" font-family="Arial" font-size="6">状态: 运行中</text>
    <text x="140" y="200" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="6">精度: ±0.1°C</text>
    
    <!-- 控制面板 -->
    <rect x="90" y="220" width="100" height="50" rx="4" fill="#e5e7eb"/>
    <circle cx="110" cy="235" r="6" fill="#10b981"/>
    <circle cx="130" cy="235" r="6" fill="#fbbf24"/>
    <circle cx="150" cy="235" r="6" fill="#ef4444"/>
    <circle cx="170" cy="235" r="6" fill="#3b82f6"/>
    
    <!-- 按钮 -->
    <rect x="95" y="250" width="20" height="15" rx="2" fill="#6b7280"/>
    <rect x="120" y="250" width="20" height="15" rx="2" fill="#6b7280"/>
    <rect x="145" y="250" width="20" height="15" rx="2" fill="#6b7280"/>
    <rect x="170" y="250" width="15" height="15" rx="2" fill="#dc2626"/>
  </g>
  
  <!-- 辅助设备 -->
  <g filter="url(#shadow)">
    <!-- 温度控制器 -->
    <rect x="250" y="180" width="80" height="100" rx="6" fill="#64748b"/>
    <rect x="260" y="190" width="60" height="40" rx="3" fill="#1f2937"/>
    <text x="290" y="210" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="8">-40°C</text>
    <text x="290" y="225" text-anchor="middle" fill="#fbbf24" font-family="Arial" font-size="6">CTRL</text>
    
    <!-- 控制旋钮 -->
    <circle cx="270" cy="250" r="8" fill="#374151"/>
    <circle cx="290" cy="250" r="8" fill="#374151"/>
    <circle cx="310" cy="250" r="8" fill="#374151"/>
    
    <!-- 指示灯 -->
    <circle cx="270" cy="265" r="3" fill="#10b981"/>
    <circle cx="290" cy="265" r="3" fill="#fbbf24"/>
    <circle cx="310" cy="265" r="3" fill="#ef4444"/>
  </g>
  
  <!-- 监控设备 -->
  <g filter="url(#shadow)">
    <rect x="360" y="140" width="100" height="140" rx="6" fill="#f3f4f6"/>
    <rect x="370" y="150" width="80" height="60" rx="3" fill="#000000"/>
    
    <!-- 监控屏幕内容 -->
    <text x="410" y="170" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="7">芯片测试监控</text>
    <text x="410" y="185" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="6">实时数据采集</text>
    <text x="410" y="200" text-anchor="middle" fill="#fbbf24" font-family="Arial" font-size="6">智能分析中...</text>
    
    <!-- 数据线 -->
    <line x1="200" y1="200" x2="360" y2="180" stroke="#6b7280" stroke-width="2"/>
    <line x1="330" y1="220" x2="360" y2="200" stroke="#6b7280" stroke-width="2"/>
    
    <!-- 键盘 -->
    <rect x="370" y="220" width="80" height="25" rx="3" fill="#e5e7eb"/>
    <rect x="375" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="385" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="395" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="405" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="415" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="425" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="435" y="225" width="8" height="6" rx="1" fill="#9ca3af"/>
    
    <rect x="375" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="385" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="395" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="405" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="415" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="425" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    <rect x="435" y="235" width="8" height="6" rx="1" fill="#9ca3af"/>
    
    <!-- 鼠标 -->
    <ellipse cx="420" cy="260" rx="8" ry="12" fill="#6b7280"/>
    <ellipse cx="420" cy="260" rx="6" ry="10" fill="#9ca3af"/>
  </g>
  
  <!-- 芯片样品架 -->
  <g filter="url(#shadow)">
    <rect x="30" y="200" width="40" height="80" rx="4" fill="#f59e0b"/>
    <rect x="35" y="210" width="30" height="5" rx="1" fill="#d97706"/>
    <rect x="35" y="220" width="30" height="5" rx="1" fill="#d97706"/>
    <rect x="35" y="230" width="30" height="5" rx="1" fill="#d97706"/>
    <rect x="35" y="240" width="30" height="5" rx="1" fill="#d97706"/>
    <rect x="35" y="250" width="30" height="5" rx="1" fill="#d97706"/>
    <rect x="35" y="260" width="30" height="5" rx="1" fill="#d97706"/>
    
    <!-- 芯片样品 -->
    <rect x="37" y="212" width="6" height="3" rx="0.5" fill="#1f2937"/>
    <rect x="45" y="212" width="6" height="3" rx="0.5" fill="#1f2937"/>
    <rect x="53" y="212" width="6" height="3" rx="0.5" fill="#1f2937"/>
    <rect x="37" y="222" width="6" height="3" rx="0.5" fill="#1f2937"/>
    <rect x="45" y="222" width="6" height="3" rx="0.5" fill="#1f2937"/>
    <rect x="53" y="222" width="6" height="3" rx="0.5" fill="#1f2937"/>
  </g>
  
  <!-- 工作台 -->
  <rect x="20" y="270" width="460" height="10" rx="2" fill="#9ca3af"/>
  
  <!-- 公司标识 -->
  <text x="250" y="320" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="14" font-weight="bold">睿测微智能科技</text>
  <text x="250" y="340" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="10">芯片高低温测试实验室</text>
</svg>
