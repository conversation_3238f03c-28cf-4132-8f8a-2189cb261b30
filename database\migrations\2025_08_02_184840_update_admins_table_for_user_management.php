<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            // 确保status字段存在
            if (!Schema::hasColumn('admins', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('role');
            }

            // 确保role字段有正确的值
            if (Schema::hasColumn('admins', 'role')) {
                // 更新role字段为enum类型
                $table->enum('role', ['super_admin', 'admin', 'operator'])->default('admin')->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            // 不需要回滚，保持字段
        });
    }
};
