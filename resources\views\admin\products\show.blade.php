@extends('admin.layouts.app')

@section('title', '产品详情')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.products.index') }}">产品管理</a></li>
    <li class="breadcrumb-item active">{{ $product->name }}</li>
@endsection

@section('content')
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-cube me-2"></i>{{ $product->name }}
        </h1>
        <p class="text-muted">产品详情信息</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>编辑产品
        </a>
        <a href="{{ route('admin.products.index') }}" class="btn btn-secondary">
            <i class="fas fa-list me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- 基本信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>产品名称：</strong></td>
                                <td>{{ $product->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>产品别名：</strong></td>
                                <td><code>{{ $product->slug }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>产品分类：</strong></td>
                                <td>
                                    @if($product->category)
                                        <span class="badge bg-info">{{ $product->category->name }}</span>
                                    @else
                                        <span class="text-muted">未分类</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>产品型号：</strong></td>
                                <td>{{ $product->model ?: '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>产品品牌：</strong></td>
                                <td>{{ $product->brand ?: '-' }}</td>
                            </tr>

                            <tr>
                                <td><strong>排序：</strong></td>
                                <td>{{ $product->sort_order }}</td>
                            </tr>
                            <tr>
                                <td><strong>状态：</strong></td>
                                <td>
                                    @if($product->is_published)
                                        <span class="badge bg-success">已发布</span>
                                    @else
                                        <span class="badge bg-secondary">草稿</span>
                                    @endif
                                    @if($product->is_featured)
                                        <span class="badge bg-warning ms-1">推荐</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td>{{ $product->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            <tr>
                                <td><strong>更新时间：</strong></td>
                                <td>{{ $product->updated_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        @if($product->image)
                            <div class="text-center">
                                <img src="{{ Storage::url($product->image) }}"
                                     alt="{{ $product->name }}"
                                     class="img-fluid rounded shadow"
                                     style="max-width: 250px;">
                                <div class="mt-2 text-muted">产品主图</div>
                            </div>
                        @else
                            <div class="text-center">
                                <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                     style="width: 250px; height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                                <div class="mt-2 text-muted">暂无图片</div>
                            </div>
                        @endif
                    </div>
                </div>

                @if($product->description)
                    <div class="mt-4">
                        <h6><strong>产品描述：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            {{ $product->description }}
                        </div>
                    </div>
                @endif

                @if($product->content)
                    <div class="mt-4">
                        <h6><strong>产品详情：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            {!! $product->content !!}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- 产品图片集 -->
        @if($product->images && count($product->images) > 0)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>产品图片集 ({{ count($product->images) }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($product->images as $image)
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="{{ Storage::url($image) }}"
                                         alt="{{ $product->name }}"
                                         class="card-img-top"
                                         style="height: 200px; object-fit: cover;">
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- 产品规格 -->
        @if($product->specifications && count($product->specifications) > 0)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ul me-2"></i>产品规格
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>规格名称</th>
                                    <th>规格值</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($product->specifications as $name => $value)
                                    <tr>
                                        <td><strong>{{ $name }}</strong></td>
                                        <td>{{ $value }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif

        <!-- SEO信息 -->
        @if($product->meta_title || $product->meta_description || $product->meta_keywords)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>SEO信息
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        @if($product->meta_title)
                            <tr>
                                <td width="120"><strong>SEO标题：</strong></td>
                                <td>{{ $product->meta_title }}</td>
                            </tr>
                        @endif
                        @if($product->meta_description)
                            <tr>
                                <td><strong>SEO描述：</strong></td>
                                <td>{{ $product->meta_description }}</td>
                            </tr>
                        @endif
                        @if($product->meta_keywords)
                            <tr>
                                <td><strong>SEO关键词：</strong></td>
                                <td>{{ $product->meta_keywords }}</td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>
        @endif
    </div>

    <div class="col-lg-4">
        <!-- 统计信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>产品统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 class="text-primary mb-1">{{ $product->id }}</h3>
                            <small class="text-muted">产品ID</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success mb-1">{{ $product->model ?: '-' }}</h3>
                        <small class="text-muted">产品型号</small>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1">{{ $product->images ? count($product->images) : 0 }}</h4>
                            <small class="text-muted">图片数量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1">{{ $product->specifications ? count($product->specifications) : 0 }}</h4>
                        <small class="text-muted">规格参数</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>编辑产品
                    </a>
                    <button type="button" class="btn btn-success" onclick="toggleStatus({{ $product->id }})">
                        @if($product->is_published)
                            <i class="fas fa-eye-slash me-2"></i>设为草稿
                        @else
                            <i class="fas fa-eye me-2"></i>发布产品
                        @endif
                    </button>
                    <button type="button" class="btn btn-warning" onclick="toggleFeatured({{ $product->id }})">
                        @if($product->is_featured)
                            <i class="fas fa-star-half-alt me-2"></i>取消推荐
                        @else
                            <i class="fas fa-star me-2"></i>设为推荐
                        @endif
                    </button>
                    <form action="{{ route('admin.products.destroy', $product) }}"
                          method="POST"
                          onsubmit="return confirm('确定要删除这个产品吗？此操作不可恢复！')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-2"></i>删除产品
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 相关信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>相关信息
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($product->category)
                        <a href="{{ route('admin.categories.show', $product->category) }}" class="btn btn-outline-info">
                            <i class="fas fa-tag me-2"></i>查看分类
                        </a>
                    @endif
                    <a href="{{ route('admin.products.index') }}?category={{ $product->category_id }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>同分类产品
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function toggleStatus(productId) {
        // 这里可以添加AJAX请求来切换产品状态
        alert('状态切换功能需要后续实现');
    }

    function toggleFeatured(productId) {
        // 这里可以添加AJAX请求来切换推荐状态
        alert('推荐状态切换功能需要后续实现');
    }
</script>
@endpush
