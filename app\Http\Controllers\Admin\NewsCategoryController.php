<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NewsCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = NewsCategory::withCount('news')
            ->ordered()
            ->paginate(15);

        return view('admin.news-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.news-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news_categories,slug',
            'description' => 'nullable|string',
            'color' => 'nullable|string|size:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        // 处理slug
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        NewsCategory::create($data);

        return redirect()->route('admin.news-categories.index')
            ->with('success', '新闻分类创建成功！');
    }

    /**
     * Display the specified resource.
     */
    public function show(NewsCategory $newsCategory)
    {
        $newsCategory->load(['news' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.news-categories.show', compact('newsCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(NewsCategory $newsCategory)
    {
        return view('admin.news-categories.edit', compact('newsCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, NewsCategory $newsCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news_categories,slug,' . $newsCategory->id,
            'description' => 'nullable|string',
            'color' => 'nullable|string|size:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        // 处理slug
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $newsCategory->update($data);

        return redirect()->route('admin.news-categories.index')
            ->with('success', '新闻分类更新成功！');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(NewsCategory $newsCategory)
    {
        // 检查是否有关联新闻
        if ($newsCategory->news()->count() > 0) {
            return redirect()->route('admin.news-categories.index')
                ->with('error', '该分类下还有新闻，无法删除！');
        }

        $newsCategory->delete();

        return redirect()->route('admin.news-categories.index')
            ->with('success', '新闻分类删除成功！');
    }
}
