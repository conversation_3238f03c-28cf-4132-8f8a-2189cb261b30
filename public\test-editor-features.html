<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑器功能测试</title>
    <link rel="stylesheet" href="/assets/css/simple-editor.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.new {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status.enhanced {
            background: #f3e5f5;
            color: #7b1fa2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>富文本编辑器功能测试</h1>
        <p>测试新增的多图片上传和PDF上传功能</p>

        <div class="test-section">
            <h3>新增功能列表</h3>
            <ul class="feature-list">
                <li>📷 单张图片上传 <span class="status">原有功能</span></li>
                <li>🖼 多张图片上传 <span class="status new">新增</span></li>
                <li>📄 PDF文件上传 <span class="status new">新增</span></li>
                <li>🔗 链接插入 <span class="status">原有功能</span></li>
                <li>📝 富文本编辑 <span class="status enhanced">增强</span></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>编辑器测试</h3>
            <form>
                <div style="margin-bottom: 20px;">
                    <label for="content" style="display: block; margin-bottom: 10px; font-weight: bold;">产品内容：</label>
                    <textarea id="content" name="content" style="width: 100%; height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 10px;">
                        <h2>产品介绍</h2>
                        <p>这是一个测试内容，您可以：</p>
                        <ul>
                            <li>点击 📷 按钮上传单张图片</li>
                            <li>点击 🖼 按钮上传多张图片</li>
                            <li>点击 📄 按钮上传PDF文件</li>
                        </ul>
                        <p>请测试各种功能！</p>
                    </textarea>
                </div>
                
                <div style="margin-top: 20px;">
                    <button type="button" onclick="getContent()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">获取内容</button>
                    <button type="button" onclick="clearContent()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">清空内容</button>
                </div>
            </form>
        </div>

        <div class="test-section">
            <h3>内容预览</h3>
            <div id="content-preview" style="border: 1px solid #ddd; padding: 15px; border-radius: 4px; background: white; min-height: 100px;">
                内容将在这里显示...
            </div>
        </div>

        <div class="test-section">
            <h3>使用说明</h3>
            <div style="line-height: 1.6;">
                <h4>📷 单张图片上传</h4>
                <p>点击图片按钮，选择一张图片文件，支持 JPEG、PNG、GIF、WebP 格式，最大 5MB。</p>
                
                <h4>🖼 多张图片上传</h4>
                <p>点击多图按钮，可以同时选择多张图片，会显示上传进度，完成后以画廊形式展示。</p>
                
                <h4>📄 PDF文件上传</h4>
                <p>点击PDF按钮，选择PDF文件，最大 10MB，上传后会显示为可点击的文件链接。</p>
                
                <h4>技术特性</h4>
                <ul>
                    <li>支持拖拽上传（开发中）</li>
                    <li>实时上传进度显示</li>
                    <li>文件类型和大小验证</li>
                    <li>错误处理和用户提示</li>
                    <li>响应式设计</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="/assets/js/simple-editor.js"></script>
    <script>
        let editor;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化编辑器
            editor = initSimpleEditor('#content', {
                height: '400px',
                placeholder: '请输入内容，测试新功能...',
                uploadUrl: '/admin/upload/image',
                csrfToken: 'test-token' // 在实际使用中需要真实的CSRF token
            });
            
            // 初始预览
            updatePreview();
            
            // 监听内容变化
            document.getElementById('content').addEventListener('input', updatePreview);
        });
        
        function getContent() {
            const content = document.getElementById('content').value;
            alert('当前内容长度: ' + content.length + ' 字符\n\n内容预览:\n' + content.substring(0, 200) + (content.length > 200 ? '...' : ''));
        }
        
        function clearContent() {
            if (confirm('确定要清空所有内容吗？')) {
                editor.setContent('');
                updatePreview();
            }
        }
        
        function updatePreview() {
            const content = document.getElementById('content').value;
            document.getElementById('content-preview').innerHTML = content || '<em style="color: #999;">暂无内容</em>';
        }
        
        // 模拟CSRF token获取
        function getCsrfToken() {
            // 在实际应用中，这应该从meta标签或其他地方获取
            return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 'demo-token';
        }
    </script>
</body>
</html>
