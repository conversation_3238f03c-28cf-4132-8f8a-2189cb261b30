<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>富文本编辑器功能演示</title>
    <link rel="stylesheet" href="/assets/css/simple-editor.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .editor-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-new {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status-enhanced {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        .status-original {
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">富文本编辑器功能演示</h1>
                    <p class="lead mb-0">支持多图片上传、PDF文件上传的增强型编辑器</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="feature-icon mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-edit"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 功能特性 -->
        <div class="row mb-5">
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-images"></i>
                    </div>
                    <h5 class="fw-bold">多图片上传</h5>
                    <p class="text-muted">批量选择和上传多张图片，实时显示进度</p>
                    <span class="status-badge status-new">新功能</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <h5 class="fw-bold">PDF文件上传</h5>
                    <p class="text-muted">支持PDF文档上传，最大10MB</p>
                    <span class="status-badge status-new">新功能</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h5 class="fw-bold">富文本编辑</h5>
                    <p class="text-muted">完整的文本格式化功能</p>
                    <span class="status-badge status-enhanced">增强</span>
                </div>
            </div>
        </div>

        <!-- 编辑器演示 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="editor-container">
                    <h3 class="mb-4">
                        <i class="fas fa-edit text-primary me-2"></i>
                        编辑器演示
                    </h3>
                    
                    <form>
                        <div class="mb-3">
                            <label for="demo-content" class="form-label fw-bold">内容编辑</label>
                            <textarea id="demo-content" name="content" class="form-control" rows="10">
                                <h2>欢迎使用增强型富文本编辑器</h2>
                                <p>这个编辑器支持以下功能：</p>
                                <ul>
                                    <li><strong>文本格式化</strong>：粗体、斜体、下划线等</li>
                                    <li><strong>标题样式</strong>：H1、H2、H3等标题格式</li>
                                    <li><strong>列表功能</strong>：有序和无序列表</li>
                                    <li><strong>对齐方式</strong>：左对齐、居中、右对齐</li>
                                </ul>
                                
                                <h3>新增功能</h3>
                                <p>点击工具栏中的按钮来测试新功能：</p>
                                <ul>
                                    <li>📷 <strong>单张图片上传</strong>：选择一张图片上传</li>
                                    <li>🖼 <strong>多张图片上传</strong>：批量上传多张图片</li>
                                    <li>📄 <strong>PDF文件上传</strong>：上传PDF文档</li>
                                </ul>
                                
                                <p>请尝试使用这些功能！</p>
                            </textarea>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="getContent()">
                                <i class="fas fa-eye me-1"></i>预览内容
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearContent()">
                                <i class="fas fa-trash me-1"></i>清空内容
                            </button>
                            <button type="button" class="btn btn-success" onclick="saveContent()">
                                <i class="fas fa-save me-1"></i>保存内容
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-card">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        使用说明
                    </h5>
                    
                    <div class="mb-3">
                        <h6 class="fw-semibold">📷 单张图片</h6>
                        <small class="text-muted">支持 JPEG、PNG、GIF、WebP，最大 5MB</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-semibold">🖼 多张图片</h6>
                        <small class="text-muted">可同时选择多张图片，显示上传进度</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-semibold">📄 PDF文件</h6>
                        <small class="text-muted">支持PDF格式，最大 10MB</small>
                    </div>
                    
                    <hr>
                    
                    <h6 class="fw-semibold mb-2">应用页面</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-1"></i> 产品创建页面</li>
                        <li><i class="fas fa-check text-success me-1"></i> 产品编辑页面</li>
                        <li><i class="fas fa-check text-success me-1"></i> 新闻创建页面</li>
                        <li><i class="fas fa-check text-success me-1"></i> 新闻编辑页面</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-eye text-primary me-2"></i>
                        内容预览
                    </h5>
                    <div id="content-preview" style="border: 1px solid #dee2e6; border-radius: 8px; padding: 1rem; background: #f8f9fa; min-height: 200px;">
                        <em class="text-muted">内容预览将在这里显示...</em>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/assets/js/simple-editor.js"></script>
    <script>
        let demoEditor;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化演示编辑器
            demoEditor = initSimpleEditor('#demo-content', {
                height: '400px',
                placeholder: '开始编辑内容...',
                uploadUrl: '/admin/upload/image',
                csrfToken: 'demo-token'
            });
            
            // 初始预览
            updatePreview();
            
            // 监听内容变化
            document.getElementById('demo-content').addEventListener('input', updatePreview);
        });
        
        function getContent() {
            const content = document.getElementById('demo-content').value;
            alert('内容长度: ' + content.length + ' 字符\n\n' + 
                  '包含图片: ' + (content.match(/<img/g) || []).length + ' 张\n' +
                  '包含PDF: ' + (content.match(/pdf-link/g) || []).length + ' 个');
        }
        
        function clearContent() {
            if (confirm('确定要清空所有内容吗？')) {
                demoEditor.setContent('');
                updatePreview();
            }
        }
        
        function saveContent() {
            const content = document.getElementById('demo-content').value;
            // 这里可以添加保存逻辑
            alert('内容已保存！\n长度: ' + content.length + ' 字符');
        }
        
        function updatePreview() {
            const content = document.getElementById('demo-content').value;
            const preview = document.getElementById('content-preview');
            if (content.trim()) {
                preview.innerHTML = content;
            } else {
                preview.innerHTML = '<em class="text-muted">暂无内容</em>';
            }
        }
    </script>
</body>
</html>
