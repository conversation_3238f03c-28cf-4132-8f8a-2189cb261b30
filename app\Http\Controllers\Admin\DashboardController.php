<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\News;
use App\Models\Admin;

class DashboardController extends Controller
{
    /**
     * 后台首页
     */
    public function index()
    {
        // 获取统计数据（使用try-catch防止表不存在的错误）
        $stats = [
            'products' => $this->safeCount(Product::class),
            'categories' => $this->safeCount(Category::class),
            'news' => $this->safeCount(News::class),
            'admins' => $this->safeCount(Admin::class),
        ];

        // 获取最新产品
        $latestProducts = $this->safeGet(Product::class, 5);

        // 获取最新新闻
        $latestNews = $this->safeGet(News::class, 5);

        return view('admin.dashboard', compact('stats', 'latestProducts', 'latestNews'));
    }

    /**
     * 安全获取模型数量
     */
    private function safeCount($modelClass)
    {
        try {
            return $modelClass::count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 安全获取模型数据
     */
    private function safeGet($modelClass, $limit = 5)
    {
        try {
            return $modelClass::latest()->take($limit)->get();
        } catch (\Exception $e) {
            return collect();
        }
    }

    /**
     * 产品管理
     */
    public function products()
    {
        $products = Product::with('category')->paginate(15);
        return view('admin.products.index', compact('products'));
    }

    /**
     * 分类管理
     */
    public function categories()
    {
        $categories = Category::withCount('products')->paginate(15);
        return view('admin.categories.index', compact('categories'));
    }

    /**
     * 新闻管理
     */
    public function news()
    {
        $news = News::paginate(15);
        return view('admin.news.index', compact('news'));
    }

    /**
     * 管理员管理
     */
    public function admins()
    {
        $admins = Admin::paginate(15);
        return view('admin.admins.index', compact('admins'));
    }

    /**
     * 系统设置
     */
    public function settings()
    {
        return view('admin.settings');
    }
}
