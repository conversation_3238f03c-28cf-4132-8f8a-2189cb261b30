<?php
// 测试产品排序逻辑

echo "<h1>产品排序测试</h1>";

// 模拟数据库连接（这里只是演示逻辑）
$products = [
    ['id' => 1, 'name' => '普通产品A', 'is_featured' => 0, 'sort_order' => 10, 'created_at' => '2024-01-01'],
    ['id' => 2, 'name' => '推荐产品B', 'is_featured' => 1, 'sort_order' => 20, 'created_at' => '2024-01-02'],
    ['id' => 3, 'name' => '普通产品C', 'is_featured' => 0, 'sort_order' => 5, 'created_at' => '2024-01-03'],
    ['id' => 4, 'name' => '推荐产品D', 'is_featured' => 1, 'sort_order' => 15, 'created_at' => '2024-01-04'],
    ['id' => 5, 'name' => '普通产品E', 'is_featured' => 0, 'sort_order' => 8, 'created_at' => '2024-01-05'],
    ['id' => 6, 'name' => '推荐产品F', 'is_featured' => 1, 'sort_order' => 5, 'created_at' => '2024-01-06'],
];

echo "<h2>原始数据</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>产品名称</th><th>是否推荐</th><th>排序值</th><th>创建时间</th></tr>";
foreach ($products as $product) {
    $featured = $product['is_featured'] ? '✅ 是' : '❌ 否';
    echo "<tr>";
    echo "<td>{$product['id']}</td>";
    echo "<td>{$product['name']}</td>";
    echo "<td>{$featured}</td>";
    echo "<td>{$product['sort_order']}</td>";
    echo "<td>{$product['created_at']}</td>";
    echo "</tr>";
}
echo "</table>";

// 应用排序逻辑：推荐优先 -> 排序值升序 -> 创建时间降序
usort($products, function($a, $b) {
    // 首先按推荐状态排序（推荐的在前）
    if ($a['is_featured'] != $b['is_featured']) {
        return $b['is_featured'] - $a['is_featured'];
    }
    
    // 然后按排序值升序
    if ($a['sort_order'] != $b['sort_order']) {
        return $a['sort_order'] - $b['sort_order'];
    }
    
    // 最后按创建时间降序
    return strcmp($b['created_at'], $a['created_at']);
});

echo "<h2>排序后结果</h2>";
echo "<p><strong>排序规则：</strong></p>";
echo "<ol>";
echo "<li>推荐产品优先显示</li>";
echo "<li>相同推荐状态下，按排序值升序排列</li>";
echo "<li>排序值相同时，按创建时间降序排列</li>";
echo "</ol>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>显示顺序</th><th>ID</th><th>产品名称</th><th>是否推荐</th><th>排序值</th><th>创建时间</th><th>说明</th></tr>";
foreach ($products as $index => $product) {
    $featured = $product['is_featured'] ? '✅ 是' : '❌ 否';
    $order = $index + 1;
    $explanation = '';
    
    if ($product['is_featured']) {
        $explanation = '推荐产品，优先显示';
    } else {
        $explanation = '普通产品，按排序值排列';
    }
    
    $rowStyle = $product['is_featured'] ? 'background-color: #fff3cd;' : '';
    
    echo "<tr style='$rowStyle'>";
    echo "<td><strong>$order</strong></td>";
    echo "<td>{$product['id']}</td>";
    echo "<td>{$product['name']}</td>";
    echo "<td>{$featured}</td>";
    echo "<td>{$product['sort_order']}</td>";
    echo "<td>{$product['created_at']}</td>";
    echo "<td><em>$explanation</em></td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>实际效果说明</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ 优化前的问题</h3>";
echo "<ul>";
echo "<li>推荐产品在单独区块显示</li>";
echo "<li>主产品列表中推荐产品会重复出现</li>";
echo "<li>用户看到重复的产品信息</li>";
echo "</ul>";

echo "<h3>✅ 优化后的效果</h3>";
echo "<ul>";
echo "<li>移除了独立的推荐产品区块</li>";
echo "<li>推荐产品在主列表中优先显示</li>";
echo "<li>推荐产品有明显的标识（⭐推荐标签）</li>";
echo "<li>避免了产品重复显示</li>";
echo "<li>用户体验更加流畅</li>";
echo "</ul>";

echo "<h3>🎯 排序逻辑</h3>";
echo "<ol>";
echo "<li><strong>推荐优先</strong>：is_featured = 1 的产品排在前面</li>";
echo "<li><strong>排序值</strong>：sort_order 值小的排在前面</li>";
echo "<li><strong>创建时间</strong>：新创建的产品排在前面</li>";
echo "</ol>";
echo "</div>";

echo "<h2>测试链接</h2>";
echo "<ul>";
echo "<li><a href='/products' target='_blank'>查看产品中心页面</a></li>";
echo "<li><a href='/' target='_blank'>查看首页（仍有推荐产品区块）</a></li>";
echo "<li><a href='/admin/products' target='_blank'>后台产品管理</a></li>";
echo "</ul>";

?>

<style>
table { margin: 20px 0; }
th, td { padding: 10px; text-align: left; }
th { background-color: #f2f2f2; }
h2 { color: #333; margin-top: 30px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
.highlight { background-color: #fff3cd; }
</style>
