<?php
// 检查编辑器配置状态

echo "<h1>编辑器功能配置检查</h1>";

$pages = [
    '产品创建' => 'resources/views/admin/products/create.blade.php',
    '产品编辑' => 'resources/views/admin/products/edit.blade.php',
    '新闻创建' => 'resources/views/admin/news/create.blade.php',
    '新闻编辑' => 'resources/views/admin/news/edit.blade.php'
];

$requiredFiles = [
    'SimpleEditor JS' => 'public/assets/js/simple-editor.js',
    'SimpleEditor CSS' => 'public/assets/css/simple-editor.css',
    'Upload Controller' => 'app/Http/Controllers/Admin/UploadController.php'
];

echo "<h2>核心文件检查</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>文件</th><th>路径</th><th>状态</th><th>大小</th></tr>";

foreach ($requiredFiles as $name => $path) {
    $fullPath = __DIR__ . '/../' . $path;
    $exists = file_exists($fullPath);
    $size = $exists ? filesize($fullPath) : 0;
    $status = $exists ? '✅ 存在' : '❌ 缺失';
    
    echo "<tr>";
    echo "<td>$name</td>";
    echo "<td>$path</td>";
    echo "<td>$status</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : 'N/A') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>页面配置检查</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>页面</th><th>SimpleEditor JS</th><th>SimpleEditor CSS</th><th>初始化代码</th><th>上传URL</th></tr>";

foreach ($pages as $name => $path) {
    $fullPath = __DIR__ . '/../' . $path;
    
    if (file_exists($fullPath)) {
        $content = file_get_contents($fullPath);
        
        $hasJS = strpos($content, 'simple-editor.js') !== false ? '✅' : '❌';
        $hasCSS = strpos($content, 'simple-editor.css') !== false ? '✅' : '❌';
        $hasInit = strpos($content, 'initSimpleEditor') !== false ? '✅' : '❌';
        $hasUploadUrl = strpos($content, 'uploadUrl') !== false ? '✅' : '❌';
        
        echo "<tr>";
        echo "<td>$name</td>";
        echo "<td>$hasJS</td>";
        echo "<td>$hasCSS</td>";
        echo "<td>$hasInit</td>";
        echo "<td>$hasUploadUrl</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td>$name</td>";
        echo "<td colspan='4'>❌ 文件不存在</td>";
        echo "</tr>";
    }
}
echo "</table>";

echo "<h2>功能特性检查</h2>";
$jsPath = __DIR__ . '/assets/js/simple-editor.js';
if (file_exists($jsPath)) {
    $jsContent = file_get_contents($jsPath);
    
    $features = [
        '多图片上传' => 'insertMultipleImages',
        'PDF上传' => 'insertPdf',
        '单图片上传' => 'insertImage',
        '链接插入' => 'createLink',
        '进度显示' => 'progress-bar'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>功能</th><th>状态</th><th>说明</th></tr>";
    
    foreach ($features as $feature => $keyword) {
        $exists = strpos($jsContent, $keyword) !== false;
        $status = $exists ? '✅ 支持' : '❌ 缺失';
        $description = '';
        
        switch ($feature) {
            case '多图片上传':
                $description = '支持批量选择和上传多张图片';
                break;
            case 'PDF上传':
                $description = '支持PDF文件上传，最大10MB';
                break;
            case '单图片上传':
                $description = '传统的单张图片上传功能';
                break;
            case '链接插入':
                $description = '插入超链接功能';
                break;
            case '进度显示':
                $description = '上传进度条显示';
                break;
        }
        
        echo "<tr>";
        echo "<td>$feature</td>";
        echo "<td>$status</td>";
        echo "<td>$description</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ SimpleEditor JS文件不存在，无法检查功能特性</p>";
}

echo "<h2>路由检查</h2>";
$routesPath = __DIR__ . '/../routes/web.php';
if (file_exists($routesPath)) {
    $routesContent = file_get_contents($routesPath);
    
    $routes = [
        '图片上传路由' => 'upload/image',
        'PDF上传路由' => 'upload/pdf'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>路由</th><th>状态</th><th>URL</th></tr>";
    
    foreach ($routes as $name => $route) {
        $exists = strpos($routesContent, $route) !== false;
        $status = $exists ? '✅ 已配置' : '❌ 缺失';
        $url = '/admin/' . $route;
        
        echo "<tr>";
        echo "<td>$name</td>";
        echo "<td>$status</td>";
        echo "<td>$url</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ 路由文件不存在</p>";
}

echo "<h2>测试链接</h2>";
echo "<ul>";
echo "<li><a href='/admin/products/create' target='_blank'>产品创建页面</a></li>";
echo "<li><a href='/admin/products' target='_blank'>产品管理页面</a></li>";
echo "<li><a href='/admin/news/create' target='_blank'>新闻创建页面</a></li>";
echo "<li><a href='/admin/news' target='_blank'>新闻管理页面</a></li>";
echo "<li><a href='/editor-demo.html' target='_blank'>编辑器功能演示</a></li>";
echo "</ul>";

echo "<h2>使用说明</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>新增功能</h3>";
echo "<ul>";
echo "<li><strong>📷 单张图片上传</strong>：点击图片按钮，选择一张图片</li>";
echo "<li><strong>🖼 多张图片上传</strong>：点击多图按钮，可同时选择多张图片</li>";
echo "<li><strong>📄 PDF文件上传</strong>：点击PDF按钮，选择PDF文件（最大10MB）</li>";
echo "</ul>";

echo "<h3>支持页面</h3>";
echo "<ul>";
echo "<li>✅ 产品创建和编辑页面</li>";
echo "<li>✅ 新闻创建和编辑页面</li>";
echo "</ul>";

echo "<h3>技术特性</h3>";
echo "<ul>";
echo "<li>实时上传进度显示</li>";
echo "<li>文件类型和大小验证</li>";
echo "<li>美观的文件展示样式</li>";
echo "<li>错误处理和用户提示</li>";
echo "</ul>";
echo "</div>";

?>

<style>
table { margin: 20px 0; }
th, td { padding: 10px; text-align: left; }
th { background-color: #f2f2f2; }
h2 { color: #333; margin-top: 30px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
