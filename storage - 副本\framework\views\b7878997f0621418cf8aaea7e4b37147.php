<?php $__env->startSection('title', '新闻分类管理'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item active">新闻分类</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-folder me-2"></i>新闻分类管理
        </h1>
        <p class="text-muted">管理新闻分类信息</p>
    </div>
    <a href="<?php echo e(route('admin.news-categories.create')); ?>" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>添加分类
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>分类列表
        </h5>
    </div>
    <div class="card-body">
        <?php if($categories->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>分类名称</th>
                            <th>别名</th>
                            <th>颜色</th>
                            <th>新闻数量</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($category->id); ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: <?php echo e($category->color); ?>;"></div>
                                        <div>
                                            <strong><?php echo e($category->name); ?></strong>
                                            <?php if($category->description): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($category->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td><code><?php echo e($category->slug); ?></code></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 30px; height: 20px; background-color: <?php echo e($category->color); ?>;"></div>
                                        <code><?php echo e($category->color); ?></code>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($category->news_count); ?></span>
                                </td>
                                <td><?php echo e($category->sort_order); ?></td>
                                <td>
                                    <?php if($category->is_active): ?>
                                        <span class="badge bg-success">启用</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">禁用</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($category->created_at->format('Y-m-d')); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.news-categories.show', $category)); ?>" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.news-categories.edit', $category)); ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.news-categories.destroy', $category)); ?>" 
                                              method="POST" 
                                              class="d-inline"
                                              onsubmit="return confirm('确定要删除这个分类吗？')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="d-flex justify-content-center">
                <?php echo e($categories->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-folder fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无分类</h4>
                <p class="text-muted">点击上方按钮添加第一个新闻分类</p>
                <a href="<?php echo e(route('admin.news-categories.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加分类
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/news-categories/index.blade.php ENDPATH**/ ?>