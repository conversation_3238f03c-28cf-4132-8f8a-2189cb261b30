<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TestRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'equipment_id',
        'user_id',
        'test_type',
        'test_name',
        'start_time',
        'end_time',
        'status',
        'result',
        'test_data',
        'notes',
        'report_file',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'test_data' => 'array',
    ];

    /**
     * 获取测试设备
     */
    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    /**
     * 获取操作员
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取测试参数
     */
    public function testParameters(): HasMany
    {
        return $this->hasMany(TestParameter::class);
    }

    /**
     * 获取已完成的测试记录
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * 获取通过的测试记录
     */
    public function scopePassed($query)
    {
        return $query->where('result', 'pass');
    }
}
