<?php
// 测试图片路径问题
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>图片路径测试</h1>";

$testImagePath = 'uploads/editor/1753962532_cnEQXuR2.png';

echo "<h2>路径信息</h2>";
echo "<p>数据库中的路径: {$testImagePath}</p>";
echo "<p>APP_URL: " . config('app.url') . "</p>";

echo "<h2>文件存在检查</h2>";
$fullPath = storage_path('app/public/' . $testImagePath);
echo "<p>完整文件路径: {$fullPath}</p>";
echo "<p>文件存在: " . (file_exists($fullPath) ? '是' : '否') . "</p>";

echo "<h2>URL生成测试</h2>";
$storageUrl = \Storage::url($testImagePath);
echo "<p>Storage::url() 生成: {$storageUrl}</p>";

$manualUrl = config('app.url') . '/storage/' . $testImagePath;
echo "<p>手动构建URL: {$manualUrl}</p>";

echo "<h2>图片显示测试</h2>";
echo "<p>使用Storage::url():</p>";
echo "<img src='{$storageUrl}' style='max-width: 300px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"加载失败: {$storageUrl}\"'>";

echo "<p>使用手动URL:</p>";
echo "<img src='{$manualUrl}' style='max-width: 300px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"加载失败: {$manualUrl}\"'>";

echo "<h2>直接访问测试</h2>";
echo "<p><a href='{$storageUrl}' target='_blank'>点击直接访问图片 (Storage::url)</a></p>";
echo "<p><a href='{$manualUrl}' target='_blank'>点击直接访问图片 (手动URL)</a></p>";

echo "<h2>产品数据检查</h2>";
try {
    $products = \App\Models\Product::whereNotNull('image')->take(5)->get();
    
    echo "<table border='1' cellpadding='10' cellspacing='0'>";
    echo "<tr><th>产品名称</th><th>图片路径</th><th>生成的URL</th><th>文件存在</th><th>预览</th></tr>";
    
    foreach ($products as $product) {
        $imagePath = $product->image;
        $imageUrl = $product->image_url;
        $fileExists = file_exists(storage_path('app/public/' . $imagePath)) ? '是' : '否';
        
        echo "<tr>";
        echo "<td>{$product->name}</td>";
        echo "<td>{$imagePath}</td>";
        echo "<td>{$imageUrl}</td>";
        echo "<td>{$fileExists}</td>";
        echo "<td><img src='{$imageUrl}' style='max-width: 100px;' onerror='this.alt=\"加载失败\"'></td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
