@extends('admin.layouts.app')

@section('title', '分类详情')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.categories.index') }}">产品分类</a></li>
    <li class="breadcrumb-item active">{{ $category->name }}</li>
@endsection

@section('content')
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-tag me-2"></i>{{ $category->name }}
        </h1>
        <p class="text-muted">分类详情信息</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>编辑分类
        </a>
        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
            <i class="fas fa-list me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- 基本信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>分类名称：</strong></td>
                                <td>{{ $category->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>别名：</strong></td>
                                <td><code>{{ $category->slug }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>父级分类：</strong></td>
                                <td>
                                    @if($category->parent)
                                        <span class="badge bg-info">{{ $category->parent->name }}</span>
                                    @else
                                        <span class="text-muted">顶级分类</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>排序：</strong></td>
                                <td>{{ $category->sort_order }}</td>
                            </tr>
                            <tr>
                                <td><strong>状态：</strong></td>
                                <td>
                                    @if($category->is_active)
                                        <span class="badge bg-success">启用</span>
                                    @else
                                        <span class="badge bg-secondary">禁用</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td>{{ $category->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            <tr>
                                <td><strong>更新时间：</strong></td>
                                <td>{{ $category->updated_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        @if($category->image)
                            <div class="text-center">
                                <img src="{{ Storage::url($category->image) }}"
                                     alt="{{ $category->name }}"
                                     class="img-fluid rounded shadow"
                                     style="max-width: 200px;">
                                <div class="mt-2 text-muted">分类图片</div>
                            </div>
                        @else
                            <div class="text-center">
                                <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                     style="width: 200px; height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                                <div class="mt-2 text-muted">暂无图片</div>
                            </div>
                        @endif
                    </div>
                </div>

                @if($category->description)
                    <div class="mt-4">
                        <h6><strong>分类描述：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            {{ $category->description }}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- 子分类 -->
        @if($category->children()->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sitemap me-2"></i>子分类 ({{ $category->children()->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($category->children as $child)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            @if($child->image)
                                                <img src="{{ Storage::url($child->image) }}"
                                                     alt="{{ $child->name }}"
                                                     class="rounded me-3"
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-tag text-muted"></i>
                                                </div>
                                            @endif
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ $child->name }}</h6>
                                                <small class="text-muted">{{ $child->products()->count() }} 个产品</small>
                                            </div>
                                            <a href="{{ route('admin.categories.show', $child) }}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- 产品列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cube me-2"></i>分类产品 ({{ $category->products()->count() }})
                </h5>
                @if($category->products()->count() > 0)
                    <a href="{{ route('admin.products.index') }}?category={{ $category->id }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                @endif
            </div>
            <div class="card-body">
                @if($category->products->count() > 0)
                    <div class="row">
                        @foreach($category->products as $product)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            @if($product->image)
                                                <img src="{{ Storage::url($product->image) }}"
                                                     alt="{{ $product->name }}"
                                                     class="rounded me-3"
                                                     style="width: 60px; height: 60px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                     style="width: 60px; height: 60px;">
                                                    <i class="fas fa-cube text-muted"></i>
                                                </div>
                                            @endif
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ $product->name }}</h6>
                                                @if($product->model)
                                                    <small class="text-muted">型号：{{ $product->model }}</small><br>
                                                @endif
                                                @if($product->price)
                                                    <small class="text-success">￥{{ number_format($product->price, 2) }}</small>
                                                @endif
                                                <div class="mt-1">
                                                    @if($product->is_published)
                                                        <span class="badge bg-success">已发布</span>
                                                    @else
                                                        <span class="badge bg-secondary">草稿</span>
                                                    @endif
                                                    @if($product->is_featured)
                                                        <span class="badge bg-warning">推荐</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <a href="{{ route('admin.products.show', $product) }}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-cube fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无产品</h5>
                        <p class="text-muted">该分类下还没有产品</p>
                        <a href="{{ route('admin.products.create') }}?category={{ $category->id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加产品
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- 统计信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>统计信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 class="text-primary mb-1">{{ $category->products()->count() }}</h3>
                            <small class="text-muted">产品数量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success mb-1">{{ $category->children()->count() }}</h3>
                        <small class="text-muted">子分类</small>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1">{{ $category->products()->where('is_published', true)->count() }}</h4>
                            <small class="text-muted">已发布</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1">{{ $category->products()->where('is_featured', true)->count() }}</h4>
                        <small class="text-muted">推荐产品</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>编辑分类
                    </a>
                    <a href="{{ route('admin.products.create') }}?category={{ $category->id }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>添加产品
                    </a>
                    @if($category->products()->count() == 0 && $category->children()->count() == 0)
                        <form action="{{ route('admin.categories.destroy', $category) }}"
                              method="POST"
                              onsubmit="return confirm('确定要删除这个分类吗？此操作不可恢复！')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>删除分类
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
