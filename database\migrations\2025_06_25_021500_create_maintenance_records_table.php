<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('maintenance_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('equipment_id')->constrained('equipment')->comment('设备ID');
            $table->foreignId('user_id')->constrained('users')->comment('维护人员ID');
            $table->enum('type', ['preventive', 'corrective', 'calibration', 'inspection'])->comment('维护类型');
            $table->datetime('maintenance_date')->comment('维护日期');
            $table->text('description')->comment('维护描述');
            $table->text('parts_replaced')->nullable()->comment('更换部件');
            $table->decimal('cost', 10, 2)->nullable()->comment('维护费用');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled')->comment('维护状态');
            $table->text('notes')->nullable()->comment('备注');
            $table->datetime('next_maintenance_date')->nullable()->comment('下次维护日期');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('maintenance_records');
    }
};
