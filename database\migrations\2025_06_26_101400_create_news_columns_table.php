<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_columns', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('栏目名称');
            $table->string('slug')->unique()->comment('栏目别名');
            $table->text('description')->nullable()->comment('栏目描述');
            $table->string('color', 7)->default('#007bff')->comment('栏目颜色');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_columns');
    }
};
