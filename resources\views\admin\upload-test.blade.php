@extends('admin.layouts.app')

@section('title', '图片上传测试')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">图片上传功能测试</h5>
                </div>
                <div class="card-body">
                    <!-- 系统信息 -->
                    <div class="alert alert-info">
                        <h6>系统配置信息</h6>
                        <ul class="mb-0">
                            <li><strong>PHP版本:</strong> {{ PHP_VERSION }}</li>
                            <li><strong>上传最大文件:</strong> {{ ini_get('upload_max_filesize') }}</li>
                            <li><strong>POST最大大小:</strong> {{ ini_get('post_max_size') }}</li>
                            <li><strong>Storage路径:</strong> {{ storage_path('app/public') }}</li>
                            <li><strong>Public Storage:</strong> {{ public_path('storage') }}</li>
                            <li><strong>Storage链接:</strong> {{ file_exists(public_path('storage')) ? '✅ 存在' : '❌ 不存在' }}</li>
                            <li><strong>上传目录:</strong> {{ file_exists(storage_path('app/public/uploads/editor')) ? '✅ 存在' : '❌ 不存在' }}</li>
                        </ul>
                    </div>

                    <!-- 测试表单 -->
                    <form id="testForm" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <h6>1. 普通文件上传测试</h6>
                                <div class="mb-3">
                                    <label for="normalFile" class="form-label">选择图片文件</label>
                                    <input type="file" class="form-control" id="normalFile" accept="image/*" onchange="previewNormalImage(this)">
                                    <div class="form-text">模拟产品/新闻主图上传</div>
                                </div>
                                <div id="normal-preview" class="mb-3" style="display: none;">
                                    <img id="normal-img" src="" alt="预览" class="img-thumbnail" style="max-width: 200px;">
                                </div>
                                <button type="button" class="btn btn-primary" onclick="testNormalUpload()">测试普通上传</button>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>2. TinyMCE编辑器上传测试</h6>
                                <div class="mb-3">
                                    <label for="editorFile" class="form-label">选择图片文件</label>
                                    <input type="file" class="form-control" id="editorFile" accept="image/*" onchange="previewEditorImage(this)">
                                    <div class="form-text">模拟TinyMCE编辑器上传</div>
                                </div>
                                <div id="editor-preview" class="mb-3" style="display: none;">
                                    <img id="editor-img" src="" alt="预览" class="img-thumbnail" style="max-width: 200px;">
                                </div>
                                <button type="button" class="btn btn-success" onclick="testEditorUpload()">测试编辑器上传</button>
                            </div>
                        </div>
                    </form>

                    <!-- 结果显示 -->
                    <div id="result" class="mt-4" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 预览功能
function previewNormalImage(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('normal-img').src = e.target.result;
            document.getElementById('normal-preview').style.display = 'block';
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function previewEditorImage(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('editor-img').src = e.target.result;
            document.getElementById('editor-preview').style.display = 'block';
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// 测试普通上传
function testNormalUpload() {
    const input = document.getElementById('normalFile');
    const file = input.files[0];
    
    if (!file) {
        alert('请先选择一个图片文件');
        return;
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('_token', '{{ csrf_token() }}');
    
    showResult('普通上传测试中...', 'info');
    
    fetch('{{ route("admin.upload.image") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showResult(`
                <h6>✅ 普通上传成功！</h6>
                <p><strong>URL:</strong> ${result.url}</p>
                <p><strong>路径:</strong> ${result.path}</p>
                <img src="${result.url}" class="img-thumbnail mt-2" style="max-width: 300px;">
            `, 'success');
        } else {
            showResult(`❌ 普通上传失败: ${result.message}`, 'danger');
        }
    })
    .catch(error => {
        showResult(`❌ 普通上传错误: ${error.message}`, 'danger');
    });
}

// 测试编辑器上传
function testEditorUpload() {
    const input = document.getElementById('editorFile');
    const file = input.files[0];
    
    if (!file) {
        alert('请先选择一个图片文件');
        return;
    }
    
    // 模拟TinyMCE的blob上传方式
    const formData = new FormData();
    formData.append('image', file, file.name);
    formData.append('_token', '{{ csrf_token() }}');
    
    showResult('编辑器上传测试中...', 'info');
    
    fetch('{{ route("admin.upload.image") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showResult(`
                <h6>✅ 编辑器上传成功！</h6>
                <p><strong>URL:</strong> ${result.url}</p>
                <p><strong>路径:</strong> ${result.path}</p>
                <img src="${result.url}" class="img-thumbnail mt-2" style="max-width: 300px;">
            `, 'success');
        } else {
            showResult(`❌ 编辑器上传失败: ${result.message}`, 'danger');
        }
    })
    .catch(error => {
        showResult(`❌ 编辑器上传错误: ${error.message}`, 'danger');
    });
}

function showResult(message, type) {
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    resultDiv.style.display = 'block';
}
</script>
@endsection
