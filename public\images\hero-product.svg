<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 400">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc"/>
      <stop offset="50%" style="stop-color:#e2e8f0"/>
      <stop offset="100%" style="stop-color:#cbd5e1"/>
    </linearGradient>
    
    <linearGradient id="deviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff"/>
      <stop offset="100%" style="stop-color:#f1f5f9"/>
    </linearGradient>
    
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e4a8c"/>
      <stop offset="100%" style="stop-color:#3b82f6"/>
    </linearGradient>
    
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a"/>
      <stop offset="100%" style="stop-color:#1e293b"/>
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="5" flood-color="rgba(0,0,0,0.2)"/>
    </filter>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 网格图案 -->
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.5"/>
    </pattern>
  </defs>

  <!-- 背景 -->
  <rect width="600" height="400" fill="url(#bgGradient)"/>
  
  <!-- 装饰性网格 -->
  <rect width="600" height="400" fill="url(#grid)"/>

  <!-- 主测试设备 -->
  <g transform="translate(75, 60)">
    <!-- 设备主体 -->
    <rect x="0" y="0" width="450" height="280" rx="15" ry="15" 
          fill="url(#deviceGradient)" 
          stroke="#cbd5e1" 
          stroke-width="2" 
          filter="url(#shadow)"/>
    
    <!-- 设备面板 -->
    <rect x="15" y="15" width="420" height="250" rx="10" ry="10" 
          fill="rgba(30, 74, 140, 0.05)" 
          stroke="#1e4a8c" 
          stroke-width="1"/>

    <!-- 左侧控制面板 -->
    <rect x="30" y="30" width="180" height="220" rx="8" ry="8" 
          fill="url(#screenGradient)" 
          stroke="#475569" 
          stroke-width="1"/>
    
    <!-- 显示屏 -->
    <rect x="40" y="40" width="160" height="100" rx="5" ry="5" 
          fill="#0f172a" 
          stroke="#3b82f6" 
          stroke-width="1"/>
    
    <!-- 显示屏内容 -->
    <text x="120" y="60" text-anchor="middle" fill="#3b82f6" 
          font-family="Arial, sans-serif" font-size="12" font-weight="bold">
      睿测微测试系统
    </text>
    <text x="120" y="80" text-anchor="middle" fill="#10b981" 
          font-family="Arial, sans-serif" font-size="10">
      温度范围: -55°C ~ +150°C
    </text>
    <text x="120" y="95" text-anchor="middle" fill="#f59e0b" 
          font-family="Arial, sans-serif" font-size="10">
      精度: ±0.1°C | 状态: 运行中
    </text>
    <text x="120" y="110" text-anchor="middle" fill="#ef4444" 
          font-family="Arial, sans-serif" font-size="10">
      测试进度: 85%
    </text>
    <text x="120" y="125" text-anchor="middle" fill="#8b5cf6" 
          font-family="Arial, sans-serif" font-size="10">
      智能化 | 高精度 | 定制化
    </text>

    <!-- 控制按钮 -->
    <circle cx="70" cy="170" r="12" fill="#10b981" filter="url(#glow)"/>
    <text x="70" y="175" text-anchor="middle" fill="white" font-size="8">ON</text>
    
    <circle cx="120" cy="170" r="12" fill="#f59e0b"/>
    <text x="120" y="175" text-anchor="middle" fill="white" font-size="8">SET</text>
    
    <circle cx="170" cy="170" r="12" fill="#ef4444"/>
    <text x="170" y="175" text-anchor="middle" fill="white" font-size="8">OFF</text>

    <!-- 指示灯 -->
    <circle cx="60" cy="200" r="4" fill="#10b981" filter="url(#glow)">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="200" r="4" fill="#3b82f6" filter="url(#glow)">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="100" cy="200" r="4" fill="#f59e0b"/>

    <!-- 右侧测试区域 -->
    <rect x="240" y="40" width="180" height="200" rx="10" ry="10" 
          fill="rgba(255,255,255,0.9)" 
          stroke="#1e4a8c" 
          stroke-width="2"/>
    
    <!-- 测试平台 -->
    <rect x="260" y="60" width="140" height="80" rx="5" ry="5" 
          fill="#f1f5f9" 
          stroke="#64748b" 
          stroke-width="1"/>

    <!-- 芯片测试位置 -->
    <rect x="310" y="85" width="40" height="30" rx="3" ry="3" 
          fill="#fbbf24" 
          stroke="#f59e0b" 
          stroke-width="1" 
          filter="url(#glow)"/>
    
    <!-- 芯片细节 -->
    <rect x="318" y="92" width="24" height="16" rx="1" ry="1" fill="#1e293b"/>
    <circle cx="330" cy="100" r="2" fill="#3b82f6" filter="url(#glow)">
      <animate attributeName="r" values="2;3;2" dur="1.5s" repeatCount="indefinite"/>
    </circle>

    <!-- 测试探针 -->
    <line x1="290" y1="100" x2="310" y2="100" stroke="#64748b" stroke-width="2"/>
    <line x1="350" y1="100" x2="370" y2="100" stroke="#64748b" stroke-width="2"/>
    <circle cx="288" cy="100" r="3" fill="#ef4444"/>
    <circle cx="372" cy="100" r="3" fill="#10b981"/>

    <!-- 温度传感器 -->
    <rect x="270" y="160" width="30" height="15" rx="2" ry="2" fill="#3b82f6"/>
    <text x="285" y="170" text-anchor="middle" fill="white" font-size="8">TEMP</text>
    
    <rect x="320" y="160" width="30" height="15" rx="2" ry="2" fill="#10b981"/>
    <text x="335" y="170" text-anchor="middle" fill="white" font-size="8">CTRL</text>
    
    <rect x="370" y="160" width="30" height="15" rx="2" ry="2" fill="#f59e0b"/>
    <text x="385" y="170" text-anchor="middle" fill="white" font-size="8">DATA</text>

    <!-- 数据线 -->
    <path d="M 285 175 Q 285 190 330 190 Q 375 190 385 175" 
          stroke="#64748b" 
          stroke-width="2" 
          fill="none" 
          stroke-dasharray="3,2"/>
  </g>

  <!-- 底部标题 -->
  <text x="300" y="360" text-anchor="middle" fill="#1e4a8c" 
        font-family="Microsoft YaHei, Arial, sans-serif" 
        font-size="20" 
        font-weight="bold">
    睿测微智能芯片测试设备
  </text>
  
  <text x="300" y="380" text-anchor="middle" fill="#64748b" 
        font-family="Arial, sans-serif" 
        font-size="14">
    Intelligent Chip Testing Equipment
  </text>
</svg>
