<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class News extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'title',
        'slug',
        'summary',
        'content',
        'image',
        'author',
        'source',
        'type',
        'views',
        'is_featured',
        'is_published',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * 自动生成slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($news) {
            if (empty($news->slug)) {
                $news->slug = Str::slug($news->title);
            }
            if (empty($news->published_at) && $news->is_published) {
                $news->published_at = now();
            }
        });

        static::updating(function ($news) {
            if ($news->isDirty('title') && empty($news->slug)) {
                $news->slug = Str::slug($news->title);
            }
            if ($news->isDirty('is_published') && $news->is_published && empty($news->published_at)) {
                $news->published_at = now();
            }
        });
    }

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(NewsCategory::class, 'category_id');
    }

    /**
     * 获取已发布的新闻
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)->whereNotNull('published_at');
    }

    /**
     * 获取推荐新闻
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 按发布时间排序
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('published_at', 'desc')->orderBy('created_at', 'desc');
    }

    /**
     * 增加浏览次数
     */
    public function incrementViews()
    {
        $this->increment('views');
    }

    /**
     * 获取新闻状态
     */
    public function getStatusAttribute()
    {
        return $this->is_published ? 'published' : 'draft';
    }

    /**
     * 获取格式化的发布时间
     */
    public function getFormattedPublishedAtAttribute()
    {
        return $this->published_at ? $this->published_at->format('Y-m-d H:i') : '';
    }

    /**
     * 获取新闻图片，如果没有则返回默认图片
     */
    public function getImageUrlAttribute()
    {
        if ($this->image && file_exists(public_path('storage/' . $this->image))) {
            return asset('storage/' . $this->image);
        }

        return asset('images/default-news.svg');
    }

    /**
     * 获取新闻摘要，如果没有则从内容中截取
     */
    public function getExcerptAttribute()
    {
        if ($this->summary) {
            return $this->summary;
        }

        // 从内容中提取纯文本并截取前150个字符
        $content = strip_tags($this->content);
        return Str::limit($content, 150);
    }
}
