<?php $__env->startSection('title', '产品详情'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.products.index')); ?>">产品管理</a></li>
    <li class="breadcrumb-item active"><?php echo e($product->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-cube me-2"></i><?php echo e($product->name); ?>

        </h1>
        <p class="text-muted">产品详情信息</p>
    </div>
    <div class="d-flex gap-2">
        <a href="<?php echo e(route('admin.products.edit', $product)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>编辑产品
        </a>
        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-list me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- 基本信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>产品名称：</strong></td>
                                <td><?php echo e($product->name); ?></td>
                            </tr>
                            <tr>
                                <td><strong>产品别名：</strong></td>
                                <td><code><?php echo e($product->slug); ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>产品分类：</strong></td>
                                <td>
                                    <?php if($product->category): ?>
                                        <span class="badge bg-info"><?php echo e($product->category->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>产品型号：</strong></td>
                                <td><?php echo e($product->model ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>产品品牌：</strong></td>
                                <td><?php echo e($product->brand ?: '-'); ?></td>
                            </tr>

                            <tr>
                                <td><strong>排序：</strong></td>
                                <td><?php echo e($product->sort_order); ?></td>
                            </tr>
                            <tr>
                                <td><strong>状态：</strong></td>
                                <td>
                                    <?php if($product->is_published): ?>
                                        <span class="badge bg-success">已发布</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">草稿</span>
                                    <?php endif; ?>
                                    <?php if($product->is_featured): ?>
                                        <span class="badge bg-warning ms-1">推荐</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td><?php echo e($product->created_at->format('Y-m-d H:i:s')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>更新时间：</strong></td>
                                <td><?php echo e($product->updated_at->format('Y-m-d H:i:s')); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <?php if($product->image): ?>
                            <div class="text-center">
                                <img src="<?php echo e(Storage::url($product->image)); ?>"
                                     alt="<?php echo e($product->name); ?>"
                                     class="img-fluid rounded shadow"
                                     style="max-width: 250px;">
                                <div class="mt-2 text-muted">产品主图</div>
                            </div>
                        <?php else: ?>
                            <div class="text-center">
                                <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                     style="width: 250px; height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                                <div class="mt-2 text-muted">暂无图片</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if($product->description): ?>
                    <div class="mt-4">
                        <h6><strong>产品描述：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo e($product->description); ?>

                        </div>
                    </div>
                <?php endif; ?>

                <?php if($product->content): ?>
                    <div class="mt-4">
                        <h6><strong>产品详情：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo $product->content; ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 产品图片集 -->
        <?php if($product->images && count($product->images) > 0): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>产品图片集 (<?php echo e(count($product->images)); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php $__currentLoopData = $product->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="<?php echo e(Storage::url($image)); ?>"
                                         alt="<?php echo e($product->name); ?>"
                                         class="card-img-top"
                                         style="height: 200px; object-fit: cover;">
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- 产品规格 -->
        <?php if($product->specifications && count($product->specifications) > 0): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ul me-2"></i>产品规格
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>规格名称</th>
                                    <th>规格值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $product->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $name => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><strong><?php echo e($name); ?></strong></td>
                                        <td><?php echo e($value); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- SEO信息 -->
        <?php if($product->meta_title || $product->meta_description || $product->meta_keywords): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>SEO信息
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <?php if($product->meta_title): ?>
                            <tr>
                                <td width="120"><strong>SEO标题：</strong></td>
                                <td><?php echo e($product->meta_title); ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if($product->meta_description): ?>
                            <tr>
                                <td><strong>SEO描述：</strong></td>
                                <td><?php echo e($product->meta_description); ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if($product->meta_keywords): ?>
                            <tr>
                                <td><strong>SEO关键词：</strong></td>
                                <td><?php echo e($product->meta_keywords); ?></td>
                            </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <div class="col-lg-4">
        <!-- 统计信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>产品统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 class="text-primary mb-1"><?php echo e($product->id); ?></h3>
                            <small class="text-muted">产品ID</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success mb-1"><?php echo e($product->model ?: '-'); ?></h3>
                        <small class="text-muted">产品型号</small>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1"><?php echo e($product->images ? count($product->images) : 0); ?></h4>
                            <small class="text-muted">图片数量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1"><?php echo e($product->specifications ? count($product->specifications) : 0); ?></h4>
                        <small class="text-muted">规格参数</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.products.edit', $product)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>编辑产品
                    </a>
                    <button type="button" class="btn btn-success" onclick="toggleStatus(<?php echo e($product->id); ?>)">
                        <?php if($product->is_published): ?>
                            <i class="fas fa-eye-slash me-2"></i>设为草稿
                        <?php else: ?>
                            <i class="fas fa-eye me-2"></i>发布产品
                        <?php endif; ?>
                    </button>
                    <button type="button" class="btn btn-warning" onclick="toggleFeatured(<?php echo e($product->id); ?>)">
                        <?php if($product->is_featured): ?>
                            <i class="fas fa-star-half-alt me-2"></i>取消推荐
                        <?php else: ?>
                            <i class="fas fa-star me-2"></i>设为推荐
                        <?php endif; ?>
                    </button>
                    <form action="<?php echo e(route('admin.products.destroy', $product)); ?>"
                          method="POST"
                          onsubmit="return confirm('确定要删除这个产品吗？此操作不可恢复！')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-2"></i>删除产品
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 相关信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>相关信息
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if($product->category): ?>
                        <a href="<?php echo e(route('admin.categories.show', $product->category)); ?>" class="btn btn-outline-info">
                            <i class="fas fa-tag me-2"></i>查看分类
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('admin.products.index')); ?>?category=<?php echo e($product->category_id); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>同分类产品
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function toggleStatus(productId) {
        // 这里可以添加AJAX请求来切换产品状态
        alert('状态切换功能需要后续实现');
    }

    function toggleFeatured(productId) {
        // 这里可以添加AJAX请求来切换推荐状态
        alert('推荐状态切换功能需要后续实现');
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/products/show.blade.php ENDPATH**/ ?>