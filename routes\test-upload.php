<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// 测试页面
Route::get('/test-product-upload', function () {
    return view('test-product-upload');
});

// 测试产品图片上传
Route::post('/test-product-upload', function (Request $request) {
    try {
        // 检查是否有文件
        if (!$request->hasFile('image')) {
            return response()->json([
                'success' => false,
                'message' => '没有接收到图片文件',
                'files' => $request->allFiles()
            ]);
        }

        $file = $request->file('image');

        // 验证文件
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // 保存文件
        $path = $file->store('products', 'public');

        return response()->json([
            'success' => true,
            'message' => '上传成功',
            'path' => $path,
            'url' => asset('storage/' . $path),
            'file_info' => [
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'type' => $file->getMimeType()
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '上传失败: ' . $e->getMessage()
        ]);
    }
});
