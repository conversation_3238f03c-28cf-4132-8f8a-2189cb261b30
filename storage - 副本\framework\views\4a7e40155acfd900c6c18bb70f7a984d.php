<?php $__env->startSection('title', '服务支持 - 上海睿测微智能科技有限公司'); ?>
<?php $__env->startSection('description', '上海睿测微智能科技有限公司提供全方位服务支持，包括售前服务、售中服务、售后服务等专业技术支持'); ?>

<?php $__env->startSection('content'); ?>
<!-- 服务支持Banner -->
<section class="py-4">
    <div class="container">
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="services-hero-banner">
                        <div class="services-banner-content">
                            <!-- 左侧文字内容区域 -->
                            <div class="services-text-section">
                                <h1 class="company-name">
                                    服务支持
                                </h1>
                                <p class="company-tagline">
                                    全方位专业服务体系
                                </p>
                                <p class="company-description">
                                    从售前咨询到售后维护，我们提供完整的服务支持体系，
                                    专业的技术团队确保您的设备始终处于最佳运行状态，为您的业务保驾护航。
                                </p>
                                <div class="cta-buttons">
                                    <a href="tel:13917268669" class="btn-primary-custom">
                                        <i class="fas fa-headset me-2"></i>技术支持
                                    </a>
                                    <a href="#services-list" class="btn-outline-custom">
                                        <i class="fas fa-list me-2"></i>服务详情
                                    </a>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="services-image-section">
                                <!-- 背景图片通过CSS设置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- 全方位服务支持 -->
<section class="py-5" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
    <div class="container">
        <!-- 页面标题 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-4 fw-bold text-primary mb-4">全方位服务支持</h2>
                <p class="lead text-muted">专业团队为您提供从售前到售后的完整服务体系</p>
            </div>
        </div>

        <!-- 服务卡片 -->
        <div class="row g-4 mb-5">
            <!-- 售前服务 -->
            <div class="col-lg-4" data-aos="fade-up">
                <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                    <div class="card-body p-5 text-white text-center">
                        <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 80px; height: 80px;">
                            <i class="fas fa-handshake fa-2x text-white"></i>
                        </div>
                        <h3 class="h3 fw-bold mb-4">售前服务</h3>
                        <div class="text-start">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>产品选型建议</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>方案定制咨询</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>设备样机演示</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>免费初步测试评估</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 售中服务 -->
            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <div class="card-body p-5 text-white text-center">
                        <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 80px; height: 80px;">
                            <i class="fas fa-cogs fa-2x text-white"></i>
                        </div>
                        <h3 class="h3 fw-bold mb-4">售中服务</h3>
                        <div class="text-start">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>安装调试指导</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>操作培训</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>系统对接</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>项目进度可视化</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 售后服务 -->
            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="400">
                <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <div class="card-body p-5 text-white text-center">
                        <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 80px; height: 80px;">
                            <i class="fas fa-shield-alt fa-2x text-white"></i>
                        </div>
                        <h3 class="h3 fw-bold mb-4">售后服务</h3>
                        <div class="text-start">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>故障诊断与维修</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>远程在线协助</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>软件更新与升级</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-3 text-warning"></i>
                                <span>一年质保，终身技术支持</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术支持热线 -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);">
                    <div class="card-body p-5 text-center text-white">
                        <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 80px; height: 80px;">
                            <i class="fas fa-phone fa-2x text-white"></i>
                        </div>
                        <h3 class="h3 fw-bold mb-3">技术支持热线</h3>
                        <p class="lead mb-4">我们的专业团队随时为您提供全面的技术支持与问题解决方案</p>
                        <div class="d-flex flex-column flex-md-row justify-content-center gap-4">
                            <a href="tel:15821796586" class="btn btn-light btn-lg px-4 py-3 fw-bold text-primary">
                                <i class="fas fa-phone me-2"></i>158-2179-6586
                            </a>
                            <a href="tel:13917268669" class="btn btn-outline-light btn-lg px-4 py-3 fw-bold">
                                <i class="fas fa-phone me-2"></i>139-1726-8669
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* 服务支持Banner样式 - 左右分栏布局 */
.services-hero-banner {
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
}

.services-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 10;
}

.services-text-section {
    flex: 1;
    padding: 60px;
    color: #1e293b;
    position: relative;
    z-index: 3;
}

.services-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    background: url('<?php echo e(asset('images/banner/product.png')); ?>') right center/contain no-repeat;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.company-name {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e40af;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.company-tagline {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0369a1;
    margin-bottom: 1.5rem;
    border-left: 4px solid #0369a1;
    padding-left: 1rem;
}

.company-description {
    font-size: 1.1rem;
    color: #475569;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #0369a1, #1e40af);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(3, 105, 161, 0.3);
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.4);
    color: white;
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid #0369a1;
    color: #0369a1;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: #0369a1;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .services-hero-banner {
        height: auto;
        min-height: 600px;
    }

    .services-banner-content {
        flex-direction: column;
    }

    .services-text-section {
        flex: none;
        padding: 40px 30px;
        text-align: center;
    }

    .services-image-section {
        flex: none;
        height: 300px;
        background-size: contain;
        background-position: center;
    }

    .company-name {
        font-size: 2.2rem;
    }

    .company-tagline {
        font-size: 1.2rem;
    }

    .cta-buttons {
        justify-content: center;
    }
}
    /* 服务支持Banner样式 */
    .services-hero-banner {
        height: 600px;
        display: flex;
        align-items: center;
        position: relative;
    }

    .services-bg-image {
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)),
                    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><linearGradient id="serviceGrad" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:%23e8f5e8;stop-opacity:1" /><stop offset="100%" style="stop-color:%23f1f8e9;stop-opacity:1" /></linearGradient></defs><rect width="1200" height="800" fill="url(%23serviceGrad)"/><g fill="%23388e3c" opacity="0.8"><rect x="100" y="500" width="200" height="250" rx="20"/><rect x="350" y="480" width="180" height="270" rx="15"/><rect x="580" y="460" width="220" height="290" rx="25"/><rect x="850" y="490" width="160" height="260" rx="18"/></g><g fill="%234caf50" opacity="0.6"><rect x="120" y="520" width="160" height="200" rx="10"/><rect x="370" y="500" width="140" height="220" rx="8"/><rect x="600" y="480" width="180" height="240" rx="12"/><rect x="870" y="510" width="120" height="210" rx="6"/></g><g fill="%23ffffff" opacity="0.9"><rect x="140" y="540" width="30" height="40" rx="5"/><rect x="180" y="540" width="30" height="40" rx="5"/><rect x="220" y="540" width="30" height="40" rx="5"/><rect x="140" y="590" width="30" height="40" rx="5"/><rect x="180" y="590" width="30" height="40" rx="5"/><rect x="220" y="590" width="30" height="40" rx="5"/><rect x="390" y="520" width="25" height="35" rx="4"/><rect x="425" y="520" width="25" height="35" rx="4"/><rect x="460" y="520" width="25" height="35" rx="4"/><rect x="620" y="500" width="35" height="45" rx="6"/><rect x="665" y="500" width="35" height="45" rx="6"/><rect x="710" y="500" width="35" height="45" rx="6"/><rect x="890" y="530" width="20" height="30" rx="3"/><rect x="920" y="530" width="20" height="30" rx="3"/><rect x="950" y="530" width="20" height="30" rx="3"/></g><g fill="%23ff9800" opacity="0.7"><circle cx="200" cy="450" r="20"/><circle cx="440" cy="430" r="18"/><circle cx="690" cy="410" r="22"/><circle cx="930" cy="440" r="16"/></g><g fill="%232196f3" opacity="0.6"><rect x="50" y="300" width="80" height="120" rx="10"/><rect x="300" y="280" width="70" height="140" rx="8"/><rect x="550" y="260" width="90" height="160" rx="12"/><rect x="800" y="290" width="75" height="130" rx="9"/><rect x="1050" y="320" width="65" height="110" rx="7"/></g><g stroke="%23333333" stroke-width="2" fill="none" opacity="0.4"><path d="M150,200 Q300,150 450,200 T750,200"/><circle cx="200" cy="100" r="30"/><circle cx="450" cy="80" r="35"/><circle cx="700" cy="90" r="32"/><circle cx="950" cy="110" r="28"/></g><g fill="%23e91e63" opacity="0.5"><circle cx="600" cy="600" r="40"/><path d="M580,600 Q600,580 620,600 Q600,620 580,600"/></g></svg>');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .company-badge {
        animation: badgeFloat 3s ease-in-out infinite;
    }

    @keyframes badgeFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .services-hero-banner {
            height: 400px;
        }

        .services-bg-image {
            background-attachment: scroll;
        }

        .hero-content h1 {
            font-size: 2.5rem !important;
        }

        .hero-content .lead {
            font-size: 1.1rem !important;
        }
    }

    /* 服务卡片悬停效果 */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .d-flex.flex-md-row {
            flex-direction: column !important;
        }

        .gap-4 {
            gap: 1rem !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/frontend/services.blade.php ENDPATH**/ ?>