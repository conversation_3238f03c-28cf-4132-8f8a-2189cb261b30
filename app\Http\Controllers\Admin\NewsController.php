<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\NewsCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class NewsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = News::with('category');

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('summary', 'like', "%{$search}%")
                  ->orWhere('author', 'like', "%{$search}%");
            });
        }

        // 分类筛选
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // 状态筛选
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->where('is_published', true);
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }



        $news = $query->latest()->paginate(15);
        $categories = NewsCategory::active()->ordered()->get();

        return view('admin.news.index', compact('news', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $categories = NewsCategory::active()->ordered()->get();
        $selectedCategory = $request->get('category');

        return view('admin.news.create', compact('categories', 'selectedCategory'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:news_categories,id',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug',
            'summary' => 'nullable|string',
            'content' => 'required|string',
            'image' => 'nullable|string', // 现在是图片路径字符串
            'author' => 'nullable|string|max:255',
            'source' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // 处理slug
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // 处理图片（从隐藏字段获取已上传的路径）
        if ($request->filled('image')) {
            $data['image'] = $request->input('image');
        }

        // 处理发布时间
        if ($data['is_published'] && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        News::create($data);

        return redirect()->route('admin.news.index')
            ->with('success', '新闻创建成功！');
    }

    /**
     * Display the specified resource.
     */
    public function show(News $news)
    {
        return view('admin.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(News $news)
    {
        $categories = NewsCategory::active()->ordered()->get();
        return view('admin.news.edit', compact('news', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, News $news)
    {
        $request->validate([
            'category_id' => 'required|exists:news_categories,id',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug,' . $news->id,
            'summary' => 'nullable|string',
            'content' => 'required|string',
            'image' => 'nullable|string', // 现在是图片路径字符串
            'author' => 'nullable|string|max:255',
            'source' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // 处理slug
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // 处理图片（从隐藏字段获取已上传的路径）
        if ($request->filled('image')) {
            $newImagePath = $request->input('image');
            // 只有当新图片路径与原图片路径不同时才删除旧图片
            if ($news->image && $news->image !== $newImagePath) {
                Storage::disk('public')->delete($news->image);
            }
            $data['image'] = $newImagePath;
        } else {
            // 如果没有提供新图片，保留原有图片
            $data['image'] = $news->image;
        }

        // 处理发布时间
        if ($data['is_published'] && !$news->published_at && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        $news->update($data);

        return redirect()->route('admin.news.index')
            ->with('success', '新闻更新成功！');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(News $news)
    {
        // 删除图片
        if ($news->image) {
            Storage::disk('public')->delete($news->image);
        }

        $news->delete();

        return redirect()->route('admin.news.index')
            ->with('success', '新闻删除成功！');
    }
}
