<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态资源测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-image {
            max-width: 300px;
            max-height: 200px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin: 10px 0;
        }
        .image-test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .path-code {
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-image"></i> 静态资源访问测试
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
            <p class="mb-0">此页面用于测试Laravel项目中的静态图片资源是否能正常访问。如果图片显示正常，说明路径配置正确；如果显示为破损图标，说明存在访问问题。</p>
        </div>

        <!-- Banner目录图片测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-folder"></i> Banner目录图片测试</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>product.png</h5>
                    <div class="path-code mb-2">/images/banner/product.png</div>
                    <img src="/images/banner/product.png" 
                         alt="Product Banner" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
                
                <div class="col-md-6">
                    <h5>jjfa.png</h5>
                    <div class="path-code mb-2">/images/banner/jjfa.png</div>
                    <img src="/images/banner/jjfa.png" 
                         alt="JJFA Banner" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        </div>

        <!-- JJFA目录图片测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-folder"></i> JJFA目录图片测试</h3>
            
            <div class="row">
                <div class="col-md-4">
                    <h5>4_.png</h5>
                    <div class="path-code mb-2">/images/jjfa/4_.png</div>
                    <img src="/images/jjfa/4_.png" 
                         alt="JJFA 4" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
                
                <div class="col-md-4">
                    <h5>5_AI.png</h5>
                    <div class="path-code mb-2">/images/jjfa/5_AI.png</div>
                    <img src="/images/jjfa/5_AI.png" 
                         alt="JJFA 5 AI" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
                
                <div class="col-md-4">
                    <h5>4_1.png</h5>
                    <div class="path-code mb-2">/images/jjfa/4_1.png</div>
                    <img src="/images/jjfa/4_1.png" 
                         alt="JJFA 4_1" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        </div>

        <!-- 其他目录图片测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-folder"></i> 其他目录图片测试</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>about.png (banner目录)</h5>
                    <div class="path-code mb-2">/images/banner/about.png</div>
                    <img src="/images/banner/about.png" 
                         alt="About Banner" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
                
                <div class="col-md-6">
                    <h5>content.png (about目录)</h5>
                    <div class="path-code mb-2">/images/about/content.png</div>
                    <img src="/images/about/content.png" 
                         alt="About Content" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        </div>

        <!-- Laravel asset() 函数测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-code"></i> Laravel asset() 函数测试</h3>
            <p class="text-muted">以下是使用Laravel asset()函数生成的URL测试：</p>
            
            <div class="row">
                <div class="col-12">
                    <h5>使用asset()函数的URL</h5>
                    <div class="alert alert-warning">
                        <strong>注意：</strong> 这些链接需要在Laravel应用中才能正常工作，在静态HTML中可能无法访问。
                    </div>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <code>{{ asset('images/banner/product.png') }}</code>
                            <br><small class="text-muted">应该生成类似：http://localhost:8000/images/banner/product.png</small>
                        </li>
                        <li class="list-group-item">
                            <code>{{ asset('images/jjfa/4_.png') }}</code>
                            <br><small class="text-muted">应该生成类似：http://localhost:8000/images/jjfa/4_.png</small>
                        </li>
                        <li class="list-group-item">
                            <code>{{ asset('images/jjfa/5_AI.png') }}</code>
                            <br><small class="text-muted">应该生成类似：http://localhost:8000/images/jjfa/5_AI.png</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 问题诊断 -->
        <div class="image-test-card">
            <h3><i class="fas fa-stethoscope"></i> 问题诊断</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-danger">可能的问题原因</h5>
                    <ul>
                        <li><strong>文件权限问题</strong> - 服务器无法读取图片文件</li>
                        <li><strong>路径错误</strong> - 文件路径或文件名不正确</li>
                        <li><strong>服务器配置</strong> - Web服务器静态资源配置问题</li>
                        <li><strong>缓存问题</strong> - 浏览器或服务器缓存导致</li>
                        <li><strong>文件名特殊字符</strong> - 文件名包含特殊字符</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5 class="text-success">解决方案</h5>
                    <ul>
                        <li><strong>检查文件权限</strong> - 确保文件可读</li>
                        <li><strong>验证路径</strong> - 确认文件确实存在</li>
                        <li><strong>清除缓存</strong> - 清除浏览器和Laravel缓存</li>
                        <li><strong>重启服务</strong> - 重启Web服务器</li>
                        <li><strong>检查.htaccess</strong> - 确认重写规则正确</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 命令行检查 -->
        <div class="image-test-card">
            <h3><i class="fas fa-terminal"></i> 命令行检查</h3>
            <p>在项目根目录执行以下命令来检查文件：</p>
            
            <div class="bg-dark text-light p-3 rounded">
                <code>
                # 检查文件是否存在<br>
                ls -la public/images/banner/product.png<br>
                ls -la public/images/jjfa/4_.png<br>
                ls -la public/images/jjfa/5_AI.png<br><br>
                
                # 检查文件权限<br>
                chmod 644 public/images/banner/product.png<br>
                chmod 644 public/images/jjfa/*.png<br><br>
                
                # 清除Laravel缓存<br>
                php artisan cache:clear<br>
                php artisan config:clear<br>
                php artisan route:clear<br>
                php artisan view:clear
                </code>
            </div>
        </div>

        <!-- 直接链接测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-link"></i> 直接链接测试</h3>
            <p>点击以下链接直接访问图片：</p>
            
            <div class="list-group">
                <a href="/images/banner/product.png" target="_blank" class="list-group-item list-group-item-action">
                    <i class="fas fa-external-link-alt"></i> /images/banner/product.png
                </a>
                <a href="/images/jjfa/4_.png" target="_blank" class="list-group-item list-group-item-action">
                    <i class="fas fa-external-link-alt"></i> /images/jjfa/4_.png
                </a>
                <a href="/images/jjfa/5_AI.png" target="_blank" class="list-group-item list-group-item-action">
                    <i class="fas fa-external-link-alt"></i> /images/jjfa/5_AI.png
                </a>
                <a href="/images/jjfa/4_1.png" target="_blank" class="list-group-item list-group-item-action">
                    <i class="fas fa-external-link-alt"></i> /images/jjfa/4_1.png
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
