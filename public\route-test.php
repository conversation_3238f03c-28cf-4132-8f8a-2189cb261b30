<?php
echo "<h1>路由测试页面</h1>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>请求URI: " . $_SERVER['REQUEST_URI'] . "</p>";

echo "<h2>测试链接</h2>";
echo "<p>点击测试Laravel路由是否工作：</p>";
echo "<ul>";
echo "<li><a href='http://localhost:8080/'>首页</a></li>";
echo "<li><a href='http://localhost:8080/products'>产品页面</a></li>";
echo "<li><a href='http://localhost:8080/about'>关于我们</a></li>";
echo "<li><a href='http://localhost:8080/solutions'>解决方案</a></li>";
echo "</ul>";

echo "<h2>绕过URL重写测试</h2>";
echo "<ul>";
echo "<li><a href='http://localhost:8080/index.php/products'>产品页面(直接)</a></li>";
echo "<li><a href='http://localhost:8080/index.php/about'>关于我们(直接)</a></li>";
echo "<li><a href='http://localhost:8080/index.php/solutions'>解决方案(直接)</a></li>";
echo "</ul>";
?>
