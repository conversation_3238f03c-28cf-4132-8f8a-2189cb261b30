<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Equipment extends Model
{
    use HasFactory;

    protected $table = 'equipment';

    protected $fillable = [
        'category_id',
        'name',
        'model',
        'serial_number',
        'manufacturer',
        'purchase_date',
        'purchase_price',
        'status',
        'location',
        'specifications',
        'description',
        'parameters',
        'is_active',
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'purchase_price' => 'decimal:2',
        'parameters' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * 获取设备分类
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(EquipmentCategory::class, 'category_id');
    }

    /**
     * 获取设备的测试记录
     */
    public function testRecords(): HasMany
    {
        return $this->hasMany(TestRecord::class);
    }

    /**
     * 获取设备的维护记录
     */
    public function maintenanceRecords(): HasMany
    {
        return $this->hasMany(MaintenanceRecord::class);
    }

    /**
     * 获取可用状态的设备
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * 获取启用状态的设备
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
