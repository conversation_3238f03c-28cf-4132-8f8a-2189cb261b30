<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建超级管理员
        Admin::create([
            'name' => '超级管理员',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123456'),
            'role' => 'super_admin',
            'status' => 'active',
        ]);

        // 创建普通管理员
        Admin::create([
            'name' => '管理员',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123'),
            'role' => 'admin',
            'status' => 'active',
        ]);
    }
}
