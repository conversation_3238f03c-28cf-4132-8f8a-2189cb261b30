{{-- TinyMCE 统一配置 --}}
<script>
function initTinyMCE(selector = '.tinymce-editor') {
    tinymce.init({
        selector: selector,
        height: 400,
        language: 'zh_CN',
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | image media link | code preview fullscreen | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; -webkit-font-smoothing: antialiased; }',
        
        // 图片上传配置
        images_upload_url: '{{ route("admin.upload.image") }}',
        images_upload_base_path: '',
        images_upload_credentials: true,
        images_upload_handler: function (blobInfo, success, failure, progress) {
            console.log('TinyMCE 图片上传开始', {
                filename: blobInfo.filename(),
                size: blobInfo.blob().size,
                type: blobInfo.blob().type
            });

            // 创建FormData对象
            var formData = new FormData();
            formData.append('image', blobInfo.blob(), blobInfo.filename());
            formData.append('_token', '{{ csrf_token() }}');

            // 发送AJAX请求上传图片
            fetch('{{ route("admin.upload.image") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('上传响应状态:', response.status);
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error('HTTP ' + response.status + ': ' + text);
                    });
                }
                return response.json();
            })
            .then(result => {
                console.log('上传结果:', result);
                if (result.success) {
                    success(result.url);
                } else {
                    failure('图片上传失败: ' + (result.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('上传错误:', error);
                failure('图片上传失败: ' + error.message);
            });
        },
        
        // 自动上传
        automatic_uploads: true,
        
        // 文件选择器
        file_picker_types: 'image',
        file_picker_callback: function(callback, value, meta) {
            if (meta.filetype === 'image') {
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.onchange = function() {
                    var file = this.files[0];
                    if (!file) return;

                    console.log('文件选择器上传开始', {
                        name: file.name,
                        size: file.size,
                        type: file.type
                    });

                    var formData = new FormData();
                    formData.append('image', file);
                    formData.append('_token', '{{ csrf_token() }}');

                    fetch('{{ route("admin.upload.image") }}', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.text().then(text => {
                                throw new Error('HTTP ' + response.status + ': ' + text);
                            });
                        }
                        return response.json();
                    })
                    .then(result => {
                        if (result.success) {
                            callback(result.url, { alt: file.name });
                        } else {
                            alert('图片上传失败: ' + (result.message || '未知错误'));
                        }
                    })
                    .catch(error => {
                        console.error('文件选择器上传错误:', error);
                        alert('图片上传失败: ' + error.message);
                    });
                };
                input.click();
            }
        },
        
        // 其他配置
        branding: false,
        menubar: false,
        statusbar: true,
        resize: true,
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        
        // 内容过滤
        valid_elements: '*[*]',
        extended_valid_elements: '*[*]',
        
        // 初始化完成回调
        init_instance_callback: function(editor) {
            console.log('TinyMCE 编辑器初始化完成:', editor.id);
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (typeof tinymce !== 'undefined') {
        initTinyMCE();
    }
});
</script>
