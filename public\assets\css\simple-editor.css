/* 简化富文本编辑器样式 */
.simple-editor {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.simple-editor-toolbar {
    background: linear-gradient(to bottom, #fafafa, #f4f4f4);
    border-bottom: 1px solid #e1e1e1;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 2px;
    flex-wrap: wrap;
    min-height: 44px;
}

.simple-editor-toolbar button {
    border: 1px solid transparent;
    background: none;
    padding: 6px 10px;
    cursor: pointer;
    border-radius: 4px;
    font-weight: 600;
    font-size: 13px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #333;
}

.simple-editor-toolbar button:hover {
    background: #e9ecef;
    border-color: #d1d5db;
}

.simple-editor-toolbar button:active {
    background: #dee2e6;
    transform: translateY(1px);
}

.simple-editor-toolbar button.active {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.simple-editor-separator {
    width: 1px;
    height: 24px;
    background: #d1d5db;
    margin: 0 6px;
}

.simple-editor-content {
    min-height: 200px;
    padding: 16px;
    outline: none;
    line-height: 1.6;
    font-size: 14px;
    color: #333;
    background: #fff;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.simple-editor-content:focus {
    box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.simple-editor-content:empty:before {
    content: attr(data-placeholder);
    color: #999;
    pointer-events: none;
    font-style: italic;
}

/* 编辑器内容样式 */
.simple-editor-content h1 {
    font-size: 2em;
    font-weight: 700;
    margin: 0.5em 0;
    color: #1a1a1a;
    line-height: 1.2;
}

.simple-editor-content h2 {
    font-size: 1.5em;
    font-weight: 600;
    margin: 0.6em 0;
    color: #1a1a1a;
    line-height: 1.3;
}

.simple-editor-content h3 {
    font-size: 1.25em;
    font-weight: 600;
    margin: 0.7em 0;
    color: #1a1a1a;
    line-height: 1.4;
}

.simple-editor-content p {
    margin: 0.8em 0;
    line-height: 1.6;
}

.simple-editor-content ul,
.simple-editor-content ol {
    margin: 1em 0;
    padding-left: 2em;
}

.simple-editor-content li {
    margin: 0.3em 0;
    line-height: 1.5;
}

.simple-editor-content blockquote {
    margin: 1em 0;
    padding: 0.5em 1em;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
    font-style: italic;
}

.simple-editor-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 0.5em 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.simple-editor-content a {
    color: #007bff;
    text-decoration: none;
}

.simple-editor-content a:hover {
    text-decoration: underline;
}

.simple-editor-content code {
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
}

.simple-editor-content pre {
    background: #f8f9fa;
    padding: 1em;
    border-radius: 4px;
    overflow-x: auto;
    border: 1px solid #e9ecef;
}

.simple-editor-content pre code {
    background: none;
    padding: 0;
}

.simple-editor-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}

.simple-editor-content table th,
.simple-editor-content table td {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    text-align: left;
}

.simple-editor-content table th {
    background: #f8f9fa;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .simple-editor-toolbar {
        padding: 6px 8px;
        gap: 1px;
    }
    
    .simple-editor-toolbar button {
        padding: 4px 6px;
        min-width: 28px;
        height: 28px;
        font-size: 12px;
    }
    
    .simple-editor-content {
        padding: 12px;
        font-size: 16px; /* 防止iOS缩放 */
    }
}

/* 上传进度样式 */
.simple-editor-upload-progress {
    display: inline-block;
    padding: 4px 8px;
    background: #e3f2fd;
    color: #1976d2;
    border-radius: 4px;
    font-size: 12px;
    margin: 0 4px;
}

/* 多图片容器样式 */
.simple-editor-content .image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.simple-editor-content .image-gallery img {
    max-width: 200px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.simple-editor-content .image-gallery img:hover {
    transform: scale(1.05);
}

/* PDF链接样式 */
.simple-editor-content .pdf-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    margin: 10px 0;
    border: 1px solid #28a745;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
    transition: all 0.2s ease;
}

.simple-editor-content .pdf-link:hover {
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
    transform: translateY(-1px);
}

.simple-editor-content .pdf-icon {
    font-size: 32px;
    color: #28a745;
}

.simple-editor-content .pdf-info {
    flex: 1;
}

.simple-editor-content .pdf-title {
    font-weight: bold;
    color: #28a745;
    margin-bottom: 4px;
}

.simple-editor-content .pdf-meta {
    font-size: 12px;
    color: #666;
}

.simple-editor-content .pdf-actions a {
    background: #28a745;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: background 0.2s ease;
}

.simple-editor-content .pdf-actions a:hover {
    background: #218838;
}

/* 上传进度条样式 */
.upload-progress-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    background: #f8f9fa;
}

.upload-progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.upload-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.upload-progress-text {
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* 错误状态 */
.simple-editor-error {
    border-color: #dc3545 !important;
}

.simple-editor-error .simple-editor-content {
    border-color: #dc3545;
}

/* 成功状态 */
.simple-editor-success {
    border-color: #28a745 !important;
}

/* 禁用状态 */
.simple-editor.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.simple-editor.disabled .simple-editor-content {
    background: #f8f9fa;
    color: #6c757d;
}
