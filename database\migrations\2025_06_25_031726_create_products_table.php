<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('categories')->comment('产品分类ID');
            $table->string('name')->comment('产品名称');
            $table->string('slug')->unique()->comment('产品别名');
            $table->text('description')->nullable()->comment('产品描述');
            $table->text('content')->nullable()->comment('产品详情');
            $table->string('image')->nullable()->comment('产品主图');
            $table->json('images')->nullable()->comment('产品图片集');
            $table->json('specifications')->nullable()->comment('产品规格');
            $table->decimal('price', 10, 2)->nullable()->comment('产品价格');
            $table->string('model')->nullable()->comment('产品型号');
            $table->string('brand')->nullable()->comment('产品品牌');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_featured')->default(false)->comment('是否推荐');
            $table->boolean('is_published')->default(true)->comment('是否发布');
            $table->string('meta_title')->nullable()->comment('SEO标题');
            $table->text('meta_description')->nullable()->comment('SEO描述');
            $table->string('meta_keywords')->nullable()->comment('SEO关键词');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
