<?php $__env->startSection('title', $news->meta_title ?: $news->title . ' - 睿测微智能科技'); ?>
<?php $__env->startSection('description', $news->meta_description ?: Str::limit(strip_tags($news->summary ?: $news->content), 160)); ?>
<?php $__env->startSection('keywords', $news->meta_keywords ?: '新闻资讯,' . $news->title . ',睿测微'); ?>

<?php $__env->startSection('content'); ?>
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">首页</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('news.index')); ?>">新闻资讯</a></li>
            <?php if($news->category): ?>
                <li class="breadcrumb-item">
                    <a href="<?php echo e(route('news.index', ['category' => $news->category->id])); ?>">
                        <?php echo e($news->category->name); ?>

                    </a>
                </li>
            <?php endif; ?>
            <li class="breadcrumb-item active"><?php echo e(Str::limit($news->title, 50)); ?></li>
        </ol>
    </div>
</nav>

<!-- 新闻详情Banner -->
<section class="py-4">
    <div class="container">
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="news-detail-hero-banner">
                        <div class="news-detail-banner-content">
                            <!-- 左侧文字内容区域 -->
                            <div class="news-detail-text-section">
                                <h1 class="news-title">
                                    <?php echo e($news->title); ?>

                                </h1>
                                <p class="news-category">
                                    <?php echo e($news->category ? $news->category->name : '新闻资讯'); ?>

                                </p>
                                <p class="news-summary">
                                    <?php echo e($news->summary ?: '深度阅读行业资讯，了解最新技术动态和市场趋势。'); ?>

                                </p>

                                <!-- 新闻元信息 -->
                                <div class="news-meta-info">
                                    <div class="meta-item">
                                        <i class="fas fa-calendar me-2"></i>
                                        <span><?php echo e($news->published_at->format('m月d日')); ?></span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-eye me-2"></i>
                                        <span><?php echo e($news->views); ?> 次</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="news-detail-image-section">
                                <!-- 背景图片通过CSS设置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<div class="container py-5">
    <div class="row">
        <!-- 新闻内容 -->
        <div class="col-lg-8">
            <article class="news-article">




                <!-- 新闻内容 -->
                <div class="news-content" data-aos="fade-up" data-aos-delay="200">
                    <?php echo $news->content; ?>

                </div>

                <!-- 分享按钮 -->
                <div class="d-flex justify-content-between align-items-center mt-5 pt-4 border-top">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="shareNews()">
                            <i class="fas fa-share-alt me-2"></i>分享
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printNews()">
                            <i class="fas fa-print me-2"></i>打印
                        </button>
                    </div>
                    <div class="text-muted small">
                        最后更新：<?php echo e($news->updated_at->format('Y-m-d H:i')); ?>

                    </div>
                </div>

                <!-- 上一篇/下一篇 -->
                <?php if($prevNews || $nextNews): ?>
                    <div class="row mt-4">
                        <?php if($prevNews): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="text-muted mb-2">
                                            <i class="fas fa-chevron-left me-2"></i>上一篇
                                        </h6>
                                        <a href="<?php echo e(route('news.show', $prevNews->slug)); ?>"
                                           class="text-decoration-none">
                                            <?php echo e(Str::limit($prevNews->title, 50)); ?>

                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <?php if($nextNews): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-end">
                                        <h6 class="text-muted mb-2">
                                            下一篇 <i class="fas fa-chevron-right ms-2"></i>
                                        </h6>
                                        <a href="<?php echo e(route('news.show', $nextNews->slug)); ?>"
                                           class="text-decoration-none">
                                            <?php echo e(Str::limit($nextNews->title, 50)); ?>

                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </article>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 新闻栏目 -->
            <?php if($categories->count() > 0): ?>
                <div class="card border-0 shadow-sm mb-4 rounded-4" data-aos="fade-left">
                    <div class="card-header bg-gradient text-white border-0 rounded-top-4"
                         style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <h6 class="mb-0 fw-bold"><i class="fas fa-folder me-2"></i>新闻栏目</h6>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('news.index', ['category' => $category->id])); ?>"
                               class="d-flex justify-content-between align-items-center text-decoration-none text-dark p-3 border-bottom hover-bg-light position-relative">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle me-3"
                                         style="width: 12px; height: 12px; background-color: <?php echo e($category->color); ?>;"></div>
                                    <span class="fw-bold"><?php echo e($category->name); ?></span>
                                </div>
                                <span class="badge bg-secondary rounded-pill"><?php echo e($category->news_count); ?></span>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 返回列表 -->
            <div class="card border-0 shadow-sm" data-aos="fade-left" data-aos-delay="100">
                <div class="card-body text-center">
                    <a href="<?php echo e(route('news.index')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>返回新闻列表
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .news-content img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 1rem 0;
    }
    .news-content p {
        line-height: 1.8;
        margin-bottom: 1rem;
    }
    .news-content h1, .news-content h2, .news-content h3,
    .news-content h4, .news-content h5, .news-content h6 {
        color: var(--primary-blue);
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .news-content blockquote {
        border-left: 4px solid var(--primary-blue);
        padding-left: 1rem;
        margin: 1.5rem 0;
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0 8px 8px 0;
    }
    .hover-bg-light:hover {
        background-color: #f8f9fa !important;
    }

    /* 新闻详情Banner样式 - 左右分栏布局 */
    .news-detail-hero-banner {
        height: 500px;
        position: relative;
        overflow: hidden;
        border-radius: 20px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
    }

    .news-detail-banner-content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
    }

    .news-detail-text-section {
        flex: 1;
        padding: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        z-index: 2;
    }

    .news-detail-image-section {
        flex: 1;
        position: relative;
        height: 100%;
        background: url('<?php echo e(asset('images/banner/product.png')); ?>') right center/contain no-repeat;
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px;
    }

    .news-title {
        font-size: 2.8rem;
        font-weight: 700;
        color: #1e40af;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .news-category {
        font-size: 1.4rem;
        color: #3b82f6;
        margin-bottom: 1.5rem;
        font-weight: 500;
    }

    .news-summary {
        font-size: 1.1rem;
        color: #64748b;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .news-meta-info {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .news-meta-info .meta-item {
        display: flex;
        align-items: center;
        color: #64748b;
        font-size: 1rem;
    }

    .news-meta-info .meta-item i {
        color: #3b82f6;
    }

    /* 装饰元素 */
    .news-detail-hero-banner::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .news-detail-hero-banner {
            height: auto;
            min-height: 600px;
        }

        .news-detail-banner-content {
            flex-direction: column;
        }

        .news-detail-text-section {
            flex: none;
            padding: 40px 30px;
            text-align: center;
        }

        .news-detail-image-section {
            flex: none;
            height: 300px;
            background-size: contain;
            background-position: center;
        }

        .news-title {
            font-size: 2.2rem;
        }

        .news-category {
            font-size: 1.2rem;
        }

        .news-meta-info {
            justify-content: center;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function shareNews() {
        if (navigator.share) {
            navigator.share({
                title: '<?php echo e($news->title); ?>',
                text: '<?php echo e(Str::limit(strip_tags($news->summary ?: $news->content), 100)); ?>',
                url: window.location.href
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(window.location.href).then(function() {
                alert('新闻链接已复制到剪贴板');
            });
        }
    }

    function printNews() {
        window.print();
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/frontend/news/show.blade.php ENDPATH**/ ?>