<?php

// 简单的数据插入脚本
require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\News;
use App\Models\NewsCategory;
use Carbon\Carbon;

try {
    echo "开始添加新闻数据...\n";

    // 创建分类
    $industryCategory = NewsCategory::firstOrCreate([
        'name' => '行业动态'
    ], [
        'slug' => 'industry-news',
        'description' => '半导体芯片测试行业最新动态和技术发展',
        'color' => '#3b82f6',
        'sort_order' => 1,
        'is_active' => true
    ]);

    $techCategory = NewsCategory::firstOrCreate([
        'name' => '技术发展'
    ], [
        'slug' => 'technology',
        'description' => '芯片测试技术发展和创新',
        'color' => '#10b981',
        'sort_order' => 2,
        'is_active' => true
    ]);

    $companyCategory = NewsCategory::firstOrCreate([
        'name' => '公司新闻'
    ], [
        'slug' => 'company-news',
        'description' => '公司最新动态和发展',
        'color' => '#f59e0b',
        'sort_order' => 3,
        'is_active' => true
    ]);

    echo "分类创建完成\n";

    // 新闻数据
    $newsData = [
        [
            'category_id' => $industryCategory->id,
            'title' => '半导体刻蚀中ICP和CCP技术对比分析',
            'slug' => 'icp-vs-ccp-etching-analysis',
            'summary' => 'ICP与CCP的差异本质在于能量耦合方式，二者在先进制程中形成互补，为芯片制造提供了更多技术选择。',
            'content' => '<p>在半导体制造工艺中，等离子体刻蚀技术是关键工艺之一。ICP（感应耦合等离子体）和CCP（容性耦合等离子体）作为两种主要的刻蚀技术，各有其独特的优势和应用场景。</p>

<h3>ICP技术特点</h3>
<p>ICP刻蚀设备中，感应线圈位于反应腔室的上方或周围，当射频电流通过线圈时，会产生周期性变化的磁场。根据法拉第电磁感应定律，变化的磁场会在腔室内感应出环形的电场，从而产生高密度等离子体。</p>

<h3>CCP技术优势</h3>
<p>CCP技术通过平行板电极产生等离子体，具有结构简单、成本较低的优势。在特定的刻蚀应用中，CCP能够提供良好的均匀性和可控性。</p>

<h3>应用前景</h3>
<p>随着芯片制程的不断缩小，对刻蚀精度和选择性的要求越来越高。ICP和CCP技术的结合使用，为先进制程提供了更多的工艺选择和优化空间。</p>',
            'type' => 'industry',
            'author' => '技术编辑部',
            'source' => '半导体行业观察',
            'published_at' => Carbon::now()->subDays(2),
            'is_published' => true,
            'is_featured' => true,
            'views' => 250
        ],
        [
            'category_id' => $techCategory->id,
            'title' => '芯片测试中BSCAN技术的原理和应用',
            'slug' => 'bscan-technology-in-chip-testing',
            'summary' => 'BSCAN是一种用于芯片测试和调试的重要技术，主要用于检测和诊断芯片内部或PCB上的互连故障。',
            'content' => '<p>BSCAN（Boundary Scan）技术是现代芯片测试中不可或缺的重要技术，它为复杂电子系统的测试和调试提供了有效的解决方案。</p>

<h3>BSCAN技术原理</h3>
<p>BSCAN技术基于IEEE 1149.1标准，通过在芯片的I/O引脚周围添加边界扫描单元，形成一个可控的测试链路。这种设计使得测试设备能够直接访问芯片内部的连接状态。</p>

<h3>主要应用场景</h3>
<ul>
<li><strong>互连测试</strong>：检测芯片与PCB之间的连接是否正常</li>
<li><strong>功能测试</strong>：验证芯片的基本功能是否符合设计要求</li>
<li><strong>故障诊断</strong>：快速定位系统中的故障点</li>
<li><strong>在线编程</strong>：对可编程器件进行配置</li>
</ul>

<h3>技术优势</h3>
<p>BSCAN技术具有测试覆盖率高、故障定位精确、测试成本低等优势，特别适用于高密度封装和复杂系统的测试需求。</p>',
            'type' => 'technology',
            'author' => '测试技术专家',
            'source' => '芯片测试技术',
            'published_at' => Carbon::now()->subDays(5),
            'is_published' => true,
            'is_featured' => false,
            'views' => 180
        ],
        [
            'category_id' => $companyCategory->id,
            'title' => '上海睿测微智能科技有限公司高低温测试系统获得行业认可',
            'slug' => 'ruicewei-temperature-testing-system-recognition',
            'summary' => '公司自主研发的芯片高低温智能测试系统在多个客户现场成功应用，获得了行业内的广泛认可和好评。',
            'content' => '<p>近日，上海睿测微智能科技有限公司自主研发的芯片高低温智能测试系统在多个重要客户现场成功部署并稳定运行，获得了行业内的广泛认可。</p>

<h3>技术突破</h3>
<p>该系统采用先进的温度控制算法和智能监控技术，能够在-40℃到+150℃的宽温度范围内进行精确控制，温度精度达到±0.5℃，满足了高端芯片测试的严格要求。</p>

<h3>客户反馈</h3>
<p>多家知名半导体企业在使用该系统后表示，设备运行稳定，测试效率显著提升，特别是在汽车电子和工业控制芯片的可靠性测试方面表现优异。</p>

<h3>市场前景</h3>
<p>随着新能源汽车、5G通信、人工智能等领域的快速发展，对芯片在极端环境下的可靠性要求越来越高，高低温测试设备市场需求持续增长。</p>

<h3>未来规划</h3>
<p>公司将继续加大研发投入，不断优化产品性能，为客户提供更加专业、可靠的芯片测试解决方案。</p>',
            'type' => 'company',
            'author' => '市场部',
            'source' => '公司新闻',
            'published_at' => Carbon::now()->subDays(3),
            'is_published' => true,
            'is_featured' => true,
            'views' => 120
        ]
    ];

    // 插入新闻数据
    foreach ($newsData as $news) {
        $existing = News::where('slug', $news['slug'])->first();
        if (!$existing) {
            News::create($news);
            echo "新闻 '{$news['title']}' 已创建\n";
        } else {
            echo "新闻 '{$news['title']}' 已存在\n";
        }
    }

    echo "\n数据添加完成！\n";
    echo "总分类数: " . NewsCategory::count() . "\n";
    echo "总新闻数: " . News::count() . "\n";

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
