<!DOCTYPE html>
<html>
<head>
    <title>测试URL生成</title>
</head>
<body>
    <h1>URL测试</h1>
    
    <h2>配置信息</h2>
    <p><strong>APP_URL:</strong> {{ config('app.url') }}</p>
    <p><strong>当前URL:</strong> {{ url('/') }}</p>
    
    <h2>Storage URL测试</h2>
    <p><strong>测试路径:</strong> uploads/editor/test.png</p>
    <p><strong>asset() 生成:</strong> {{ asset('storage/uploads/editor/test.png') }}</p>
    <p><strong>Storage::url() 生成:</strong> {{ Storage::url('uploads/editor/test.png') }}</p>
    
    <h2>实际文件测试</h2>
    @php
        $files = Storage::disk('public')->files('uploads/editor');
        $latestFile = collect($files)->last();
    @endphp
    
    @if($latestFile)
        <p><strong>最新文件:</strong> {{ $latestFile }}</p>
        <p><strong>asset() URL:</strong> {{ asset('storage/' . $latestFile) }}</p>
        <p><strong>Storage::url() URL:</strong> {{ Storage::url($latestFile) }}</p>
        
        <h3>图片预览</h3>
        <img src="{{ asset('storage/' . $latestFile) }}" alt="测试图片" style="max-width: 200px;">
    @endif
</body>
</html>
