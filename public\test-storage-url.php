<?php
// 测试存储URL配置
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>存储URL配置测试</h1>";

try {
    echo "<h2>配置信息</h2>";
    echo "<p>APP_URL: " . config('app.url') . "</p>";
    echo "<p>Public磁盘URL: " . config('filesystems.disks.public.url') . "</p>";
    echo "<p>Storage URL函数: " . \Storage::url('test.jpg') . "</p>";
    
    echo "<h2>产品图片URL测试</h2>";
    
    // 获取一个有图片的产品
    $product = \App\Models\Product::whereNotNull('image')->first();
    
    if ($product) {
        echo "<p>产品名称: {$product->name}</p>";
        echo "<p>图片路径: {$product->image}</p>";
        echo "<p>图片URL (模型方法): {$product->image_url}</p>";
        echo "<p>图片URL (Storage::url): " . \Storage::url($product->image) . "</p>";
        
        echo "<h3>图片显示测试</h3>";
        echo "<p>使用模型方法:</p>";
        echo "<img src='{$product->image_url}' style='max-width: 200px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"图片加载失败\"'>";
        
        echo "<p>使用Storage::url:</p>";
        $storageUrl = \Storage::url($product->image);
        echo "<img src='{$storageUrl}' style='max-width: 200px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"图片加载失败\"'>";
        
        echo "<h3>文件存在检查</h3>";
        $filePath = storage_path('app/public/' . $product->image);
        echo "<p>文件路径: {$filePath}</p>";
        echo "<p>文件存在: " . (file_exists($filePath) ? '是' : '否') . "</p>";
        
    } else {
        echo "<p>没有找到有图片的产品</p>";
    }
    
    echo "<h2>手动URL测试</h2>";
    $testImagePath = 'products/a9NmiDOnEif2n8FgCWWkTYUfnvPqeXzEgvSO7Gok.png';
    $manualUrl = config('app.url') . '/storage/' . $testImagePath;
    echo "<p>手动构建的URL: {$manualUrl}</p>";
    echo "<img src='{$manualUrl}' style='max-width: 200px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"图片加载失败\"'>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
