<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片访问测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        img { max-width: 300px; border: 2px solid #007bff; }
    </style>
</head>
<body>
    <h1>🔍 图片访问测试</h1>
    
    <div class="test-box">
        <h3>测试1: 相对路径访问</h3>
        <p><strong>路径:</strong> /images/banner/product.png</p>
        <img src="/images/banner/product.png" alt="Product Banner" 
             onload="this.parentElement.className='test-box success'" 
             onerror="this.parentElement.className='test-box error'; this.style.display='none'; this.nextElementSibling.style.display='block';">
        <p style="display:none; color: red;">❌ 图片加载失败</p>
    </div>
    
    <div class="test-box">
        <h3>测试2: 相对路径访问（当前目录）</h3>
        <p><strong>路径:</strong> ./images/banner/product.png</p>
        <img src="./images/banner/product.png" alt="Product Banner" 
             onload="this.parentElement.className='test-box success'" 
             onerror="this.parentElement.className='test-box error'; this.style.display='none'; this.nextElementSibling.style.display='block';">
        <p style="display:none; color: red;">❌ 图片加载失败</p>
    </div>
    
    <div class="test-box">
        <h3>测试3: 完整URL访问</h3>
        <p><strong>路径:</strong> <span id="full-url"></span></p>
        <img id="full-url-img" alt="Product Banner" 
             onload="this.parentElement.className='test-box success'" 
             onerror="this.parentElement.className='test-box error'; this.style.display='none'; this.nextElementSibling.style.display='block';">
        <p style="display:none; color: red;">❌ 图片加载失败</p>
    </div>
    
    <div class="test-box">
        <h3>📋 诊断信息</h3>
        <p><strong>当前页面URL:</strong> <span id="current-url"></span></p>
        <p><strong>预期图片URL:</strong> <span id="expected-url"></span></p>
        <p><strong>建议检查:</strong></p>
        <ul>
            <li>文件是否存在于正确位置</li>
            <li>PHPStudy是否正在运行</li>
            <li>网站根目录配置是否正确</li>
            <li>文件权限是否正确</li>
        </ul>
    </div>
    
    <script>
        // 设置当前URL信息
        const currentUrl = window.location.href;
        const baseUrl = window.location.origin + window.location.pathname.replace('/test-image-access.html', '');
        const expectedImageUrl = baseUrl + '/images/banner/product.png';
        
        document.getElementById('current-url').textContent = currentUrl;
        document.getElementById('expected-url').textContent = expectedImageUrl;
        document.getElementById('full-url').textContent = expectedImageUrl;
        document.getElementById('full-url-img').src = expectedImageUrl;
        
        // 添加直接链接
        const linkDiv = document.createElement('div');
        linkDiv.className = 'test-box';
        linkDiv.innerHTML = `
            <h3>🔗 直接访问链接</h3>
            <p><a href="${expectedImageUrl}" target="_blank">点击直接访问图片</a></p>
        `;
        document.body.appendChild(linkDiv);
    </script>
</body>
</html>
