<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'department',
        'phone',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];

    /**
     * 获取用户的测试记录
     */
    public function testRecords()
    {
        return $this->hasMany(TestRecord::class);
    }

    /**
     * 获取用户的维护记录
     */
    public function maintenanceRecords()
    {
        return $this->hasMany(MaintenanceRecord::class);
    }

    /**
     * 获取启用状态的用户
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 检查用户是否为超级管理员
     */
    public function isSuperAdmin()
    {
        return $this->role === 'super_admin';
    }

    /**
     * 检查用户是否为管理员
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * 检查用户是否为操作员
     */
    public function isOperator()
    {
        return $this->role === 'operator';
    }

    /**
     * 检查用户是否有管理权限（超级管理员或管理员）
     */
    public function hasAdminAccess()
    {
        return in_array($this->role, ['super_admin', 'admin']);
    }

    /**
     * 检查用户是否可以管理其他用户
     */
    public function canManageUsers()
    {
        return $this->role === 'super_admin';
    }

    /**
     * 获取角色显示名称
     */
    public function getRoleDisplayName()
    {
        $roles = [
            'super_admin' => '超级管理员',
            'admin' => '管理员',
            'operator' => '操作员',
            'user' => '普通用户'
        ];

        return $roles[$this->role] ?? '未知角色';
    }
}
