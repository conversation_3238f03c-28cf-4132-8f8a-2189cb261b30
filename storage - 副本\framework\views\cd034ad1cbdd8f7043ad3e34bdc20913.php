<?php $__env->startSection('title', '产品分类管理'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item active">产品分类</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-tags me-2"></i>产品分类管理
        </h1>
        <p class="text-muted">管理产品分类信息</p>
    </div>
    <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>添加分类
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>分类列表
        </h5>
    </div>
    <div class="card-body">
        <?php if($categories->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>分类名称</th>
                            <th>别名</th>
                            <th>父级分类</th>
                            <th>产品数量</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($category->id); ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($category->image): ?>
                                            <img src="<?php echo e(Storage::url($category->image)); ?>"
                                                 alt="<?php echo e($category->name); ?>"
                                                 class="rounded me-2"
                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo e($category->name); ?></strong>
                                            <?php if($category->description): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($category->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td><code><?php echo e($category->slug); ?></code></td>
                                <td>
                                    <?php if($category->parent): ?>
                                        <span class="badge bg-info"><?php echo e($category->parent->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">顶级分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($category->products_count); ?></span>
                                </td>
                                <td><?php echo e($category->sort_order); ?></td>
                                <td>
                                    <?php if($category->is_active): ?>
                                        <span class="badge bg-success">启用</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">禁用</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($category->created_at->format('Y-m-d')); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.categories.show', $category)); ?>"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.categories.edit', $category)); ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.categories.destroy', $category)); ?>"
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('确定要删除这个分类吗？')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center">
                <?php echo e($categories->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无分类</h4>
                <p class="text-muted">点击上方按钮添加第一个产品分类</p>
                <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加分类
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>