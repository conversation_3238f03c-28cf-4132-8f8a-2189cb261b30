@extends('frontend.layouts.app')

@section('title', '联系我们 - 上海睿测微智能科技有限公司')

@section('content')
<!-- 联系我们Banner -->
<section class="py-4">
    <div class="container">
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="contact-hero-banner">
                        <div class="contact-banner-content">
                            <!-- 左侧文字内容区域 -->
                            <div class="contact-text-section">
                                <h1 class="company-name">
                                    联系我们
                                </h1>
                                <p class="company-tagline">
                                    开启合作之旅
                                </p>
                                <p class="company-description">
                                    专业的技术支持团队随时为您服务，提供定制化的芯片测试解决方案，
                                    从产品咨询到技术支持，我们致力于为每一位客户创造最大价值。
                                </p>
                                <div class="cta-buttons">
                                    <a href="tel:13917268669" class="btn-primary-custom">
                                        <i class="fas fa-phone me-2"></i>立即咨询
                                    </a>
                                    <a href="mailto:<EMAIL>" class="btn-outline-custom">
                                        <i class="fas fa-envelope me-2"></i>发送邮件
                                    </a>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="contact-image-section">
                                <!-- 背景图片通过CSS设置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 联系信息详情 -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- 左侧联系信息 -->
            <div class="col-lg-6">
                <div class="contact-info-card">
                    <h2 class="contact-title mb-4">联系我们</h2>

                    <div class="contact-item mb-4">
                        <h5 class="contact-label">公司地址</h5>
                        <p class="contact-text">上海市杨浦区长白新村街道军工路1076号机械共性研究院401室</p>
                    </div>

                    <div class="contact-item mb-4">
                        <h5 class="contact-label">联系电话</h5>
                        <p class="contact-text">13917268669 / 15821796586</p>
                    </div>

                    <div class="contact-item mb-4">
                        <h5 class="contact-label">电子邮箱</h5>
                        <p class="contact-text">
                            <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>
                        </p>
                    </div>

                    <div class="contact-item mb-4">
                        <p class="welcome-text">欢迎通过以下联系方式，我们将竭诚为您提供专业的技术支持。</p>
                    </div>

                    <!-- 二维码区域 -->
                    <div class="qr-codes d-flex gap-3 mb-4">
                        <div class="qr-placeholder"></div>
                        <div class="qr-placeholder"></div>
                    </div>

                    <!-- 行动按钮 -->
                    <div class="action-buttons d-flex gap-3">
                        <button class="btn btn-primary px-4 py-2">免费测试技术方案咨询</button>
                        <button class="btn btn-outline-primary px-4 py-2">获取产品资料</button>
                    </div>
                </div>
            </div>

            <!-- 右侧图片 -->
            <div class="col-lg-6">
                <div class="contact-image">
                    <img src="{{ asset('images/about/content.png') }}" alt="联系我们" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </div>
</section>



<!-- 地图展示 -->
<section class="py-5" id="map-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-4 fw-bold text-primary mb-4">公司位置</h2>
                <p class="lead text-muted fs-3">上海市杨浦区军工路1076号机械共性研究院401室</p>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-0">
                        <!-- 高德地图容器 -->
                        <div id="amap-container" style="height: 400px; width: 100%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
/* 联系我们Banner样式 - 左右分栏布局 */
.contact-hero-banner {
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
}

.contact-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 10;
}

.contact-text-section {
    flex: 1;
    padding: 60px;
    color: #1e293b;
    position: relative;
    z-index: 3;
}

.contact-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    background: url('{{ asset('images/banner/product.png') }}') right center/contain no-repeat;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.company-name {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e40af;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.company-tagline {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0369a1;
    margin-bottom: 1.5rem;
    border-left: 4px solid #0369a1;
    padding-left: 1rem;
}

.company-description {
    font-size: 1.1rem;
    color: #475569;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #0369a1, #1e40af);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(3, 105, 161, 0.3);
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.4);
    color: white;
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid #0369a1;
    color: #0369a1;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: #0369a1;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .contact-hero-banner {
        height: auto;
        min-height: 600px;
    }

    .contact-banner-content {
        flex-direction: column;
    }

    .contact-text-section {
        flex: none;
        padding: 40px 30px;
        text-align: center;
    }

    .contact-image-section {
        flex: none;
        height: 300px;
        background-size: contain;
        background-position: center;
    }

    .company-name {
        font-size: 2.2rem;
    }

    .company-tagline {
        font-size: 1.2rem;
    }

    .cta-buttons {
        justify-content: center;
    }
}


    .company-badge {
        animation: badgeFloat 3s ease-in-out infinite;
    }

    @keyframes badgeFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    .contact-info-card {
        background: #ffffff;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        height: 100%;
    }

    .contact-title {
        font-size: 2rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 2rem;
    }

    .contact-item {
        margin-bottom: 1.5rem;
    }

    .contact-label {
        font-size: 1rem;
        color: #666;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .contact-text {
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 0;
        line-height: 1.6;
    }

    .email-link {
        color: #2563eb;
        text-decoration: none;
    }

    .email-link:hover {
        text-decoration: underline;
    }

    .welcome-text {
        color: #666;
        font-size: 1rem;
        line-height: 1.6;
    }

    .qr-codes {
        margin: 1.5rem 0;
    }

    .qr-placeholder {
        width: 60px;
        height: 60px;
        background: #f0f0f0;
        border: 2px solid #ddd;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 0.8rem;
    }

    .action-buttons .btn {
        border-radius: 6px;
        font-weight: 500;
    }

    .btn-primary {
        background-color: #2563eb;
        border-color: #2563eb;
    }

    .btn-outline-primary {
        color: #2563eb;
        border-color: #2563eb;
    }

    .contact-image img {
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    /* 地图样式 */
    #map-section {
        background: #f8f9fa;
    }

    #map-section .card {
        border-radius: 15px;
        overflow: hidden;
    }

    #amap-container {
        border-radius: 15px;
    }



        .contact-info-card {
            margin-bottom: 2rem;
        }

        .action-buttons {
            flex-direction: column;
        }

        .action-buttons .btn {
            margin-bottom: 0.5rem;
        }
    }
</style>
@endpush

@push('scripts')
<!-- 高德地图API -->
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_API_KEY"></script>
<script>
    // 初始化高德地图
    function initMap() {
        // 创建地图实例
        var map = new AMap.Map('amap-container', {
            zoom: 16,
            center: [121.5273, 31.3041], // 上海市杨浦区军工路1076号
            mapStyle: 'amap://styles/normal'
        });

        // 创建标记点
        var marker = new AMap.Marker({
            position: [121.5273, 31.3041],
            title: '睿测微科技'
        });

        // 将标记点添加到地图
        map.add(marker);

        // 创建信息窗体
        var infoWindow = new AMap.InfoWindow({
            content: `
                <div style="padding: 10px; font-size: 14px;">
                    <h5 style="color: #1e4a8c; margin-bottom: 10px;">睿测微科技</h5>
                    <p style="margin: 5px 0;"><i class="fas fa-map-marker-alt" style="color: #dc3545;"></i> 上海市杨浦区长白新村街道</p>
                    <p style="margin: 5px 0;"><i class="fas fa-building" style="color: #28a745;"></i> 军工路1076号机械共性研究院401室</p>
                    <p style="margin: 5px 0;"><i class="fas fa-phone" style="color: #007bff;"></i> 139-1726-8669</p>
                    <p style="margin: 5px 0;"><i class="fas fa-phone" style="color: #007bff;"></i> 158-2179-6586</p>
                </div>
            `,
            offset: new AMap.Pixel(0, -30)
        });

        // 点击标记点时打开信息窗体
        marker.on('click', function() {
            infoWindow.open(map, marker.getPosition());
        });

        // 默认打开信息窗体
        infoWindow.open(map, marker.getPosition());
    }

    // 页面加载完成后初始化地图
    document.addEventListener('DOMContentLoaded', function() {
        // 检查高德地图API是否加载完成
        if (typeof AMap !== 'undefined') {
            initMap();
        } else {
            // 如果API未加载，显示静态地图信息
            document.getElementById('amap-container').innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                    <div class="text-center">
                        <i class="fas fa-map-marker-alt fa-4x text-primary mb-3"></i>
                        <h4 class="text-primary">公司地址</h4>
                        <p class="text-muted mb-2">上海市杨浦区长白新村街道</p>
                        <p class="text-muted">军工路1076号机械共性研究院401室</p>
                    </div>
                </div>
            `;
        }
    });
</script>
@endpush
