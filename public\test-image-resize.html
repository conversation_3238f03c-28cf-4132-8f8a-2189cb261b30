<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片大小调整功能测试</title>
    <link rel="stylesheet" href="/assets/css/simple-editor.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .editor-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .size-demo {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .size-demo img {
            display: block;
            margin: 10px auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">图片大小调整功能</h1>
                    <p class="lead mb-0">智能的图片大小选择和调整功能演示</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="feature-icon mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 功能介绍 -->
        <div class="row mb-5">
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-upload"></i>
                    </div>
                    <h5 class="fw-bold">上传时选择大小</h5>
                    <p class="text-muted">上传图片时可以选择显示大小</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h5 class="fw-bold">点击调整大小</h5>
                    <p class="text-muted">点击已插入的图片可以重新调整大小</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-align-center"></i>
                    </div>
                    <h5 class="fw-bold">对齐方式</h5>
                    <p class="text-muted">支持左对齐、居中、右对齐</p>
                </div>
            </div>
        </div>

        <!-- 大小示例 -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="feature-card">
                    <h3 class="mb-4">
                        <i class="fas fa-images text-success me-2"></i>
                        图片大小示例
                    </h3>
                    
                    <div class="size-demo">
                        <h6 class="fw-bold">小图 (200px)</h6>
                        <p class="text-muted small">适合图标、小插图等</p>
                        <img src="https://via.placeholder.com/400x300/28a745/ffffff?text=示例图片" 
                             style="max-width: 200px; height: auto;" alt="小图示例">
                    </div>
                    
                    <div class="size-demo">
                        <h6 class="fw-bold">中图 (400px)</h6>
                        <p class="text-muted small">适合一般的内容图片</p>
                        <img src="https://via.placeholder.com/400x300/20c997/ffffff?text=示例图片" 
                             style="max-width: 400px; height: auto;" alt="中图示例">
                    </div>
                    
                    <div class="size-demo">
                        <h6 class="fw-bold">大图 (600px)</h6>
                        <p class="text-muted small">适合重要的展示图片</p>
                        <img src="https://via.placeholder.com/600x400/17a2b8/ffffff?text=示例图片" 
                             style="max-width: 600px; height: auto;" alt="大图示例">
                    </div>
                    
                    <div class="size-demo">
                        <h6 class="fw-bold">原图 (100%)</h6>
                        <p class="text-muted small">占满容器宽度</p>
                        <img src="https://via.placeholder.com/800x300/6f42c1/ffffff?text=示例图片" 
                             style="max-width: 100%; width: 100%; height: auto;" alt="原图示例">
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑器测试 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="editor-container">
                    <h3 class="mb-4">
                        <i class="fas fa-edit text-success me-2"></i>
                        编辑器测试
                    </h3>
                    
                    <form>
                        <div class="mb-3">
                            <label for="test-content" class="form-label fw-bold">测试图片大小调整功能</label>
                            <textarea id="test-content" name="content" class="form-control" rows="10">
                                <h2>图片大小调整功能测试</h2>
                                <p>请使用编辑器工具栏中的图片按钮来测试新功能：</p>
                                <ul>
                                    <li><strong>📷 单张图片</strong>：上传时可以选择大小</li>
                                    <li><strong>🖼 多张图片</strong>：批量上传，默认为小图</li>
                                    <li><strong>点击图片</strong>：可以重新调整已插入图片的大小</li>
                                </ul>
                                
                                <h3>功能特点</h3>
                                <p>新的图片功能包括：</p>
                                <ol>
                                    <li>上传时选择大小（小图、中图、大图、原图）</li>
                                    <li>点击图片可以重新调整大小</li>
                                    <li>支持对齐方式设置（左对齐、居中、右对齐）</li>
                                    <li>可以删除不需要的图片</li>
                                    <li>鼠标悬停有缩放效果</li>
                                </ol>
                                
                                <p>请尝试上传一些图片来测试这些功能！</p>
                            </textarea>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" onclick="getContent()">
                                <i class="fas fa-eye me-1"></i>预览内容
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearContent()">
                                <i class="fas fa-trash me-1"></i>清空内容
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-card">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        使用说明
                    </h5>
                    
                    <div class="mb-3">
                        <h6 class="fw-semibold">📷 上传单张图片</h6>
                        <small class="text-muted">点击图片按钮，选择文件后会弹出大小选择对话框</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-semibold">🖼 批量上传</h6>
                        <small class="text-muted">多张图片默认为小图，上传后可以单独调整</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-semibold">🖱️ 调整已有图片</h6>
                        <small class="text-muted">点击编辑器中的图片，可以重新设置大小和对齐</small>
                    </div>
                    
                    <hr>
                    
                    <h6 class="fw-semibold mb-2">图片大小选项</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-circle text-success me-1" style="font-size: 0.5rem;"></i> 小图：200px 宽度</li>
                        <li><i class="fas fa-circle text-info me-1" style="font-size: 0.5rem;"></i> 中图：400px 宽度</li>
                        <li><i class="fas fa-circle text-warning me-1" style="font-size: 0.5rem;"></i> 大图：600px 宽度</li>
                        <li><i class="fas fa-circle text-danger me-1" style="font-size: 0.5rem;"></i> 原图：100% 宽度</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        提示
                    </h5>
                    <ul class="small">
                        <li>鼠标悬停在图片上会有缩放效果</li>
                        <li>点击图片可以打开调整对话框</li>
                        <li>支持删除不需要的图片</li>
                        <li>对齐方式会影响文字环绕效果</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="/assets/js/simple-editor.js"></script>
    <script>
        let testEditor;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化测试编辑器
            testEditor = initSimpleEditor('#test-content', {
                height: '400px',
                placeholder: '开始测试图片功能...',
                uploadUrl: '/admin/upload/image',
                csrfToken: 'test-token'
            });
        });
        
        function getContent() {
            const content = document.getElementById('test-content').value;
            const imageCount = (content.match(/<img/g) || []).length;
            alert(`内容长度: ${content.length} 字符\n包含图片: ${imageCount} 张`);
        }
        
        function clearContent() {
            if (confirm('确定要清空所有内容吗？')) {
                testEditor.setContent('');
            }
        }
    </script>
</body>
</html>
