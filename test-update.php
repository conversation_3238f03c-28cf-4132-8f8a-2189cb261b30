<?php
// 测试产品和新闻更新功能
require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// 模拟测试数据
echo "测试产品和新闻更新功能\n";
echo "========================\n\n";

// 测试1: 检查产品是否存在
echo "1. 检查产品数据...\n";
$products = DB::table('products')->select('id', 'name', 'image', 'images')->take(3)->get();
foreach ($products as $product) {
    echo "产品ID: {$product->id}, 名称: {$product->name}\n";
    echo "主图: " . ($product->image ?: '无') . "\n";
    echo "多图: " . ($product->images ? json_encode(json_decode($product->images)) : '无') . "\n";
    echo "---\n";
}

echo "\n2. 检查新闻数据...\n";
$news = DB::table('news')->select('id', 'title', 'image')->take(3)->get();
foreach ($news as $item) {
    echo "新闻ID: {$item->id}, 标题: {$item->title}\n";
    echo "图片: " . ($item->image ?: '无') . "\n";
    echo "---\n";
}

echo "\n测试完成！\n";
echo "现在可以通过浏览器测试更新功能：\n";
echo "1. 访问产品编辑页面，不上传新图片直接保存\n";
echo "2. 访问新闻编辑页面，不上传新图片直接保存\n";
echo "3. 检查图片是否保留\n";
?>
