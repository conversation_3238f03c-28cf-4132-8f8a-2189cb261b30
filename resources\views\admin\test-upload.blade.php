<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>图片上传测试</h1>

        <div class="card">
            <div class="card-body">
                <h5>系统信息</h5>
                <ul>
                    <li>PHP版本: {{ PHP_VERSION }}</li>
                    <li>上传最大文件: {{ ini_get('upload_max_filesize') }}</li>
                    <li>POST最大大小: {{ ini_get('post_max_size') }}</li>
                    <li>Storage目录: {{ storage_path('app/public') }}</li>
                    <li>Public Storage: {{ public_path('storage') }}</li>
                    <li>Storage链接存在: {{ file_exists(public_path('storage')) ? '是' : '否' }}</li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body">
                <h5>上传测试</h5>
                <input type="file" id="imageInput" accept="image/*" class="form-control mb-3">
                <button onclick="uploadImage()" class="btn btn-primary">上传图片</button>
                <button onclick="testTinyMCE()" class="btn btn-secondary">测试TinyMCE上传</button>
            </div>
        </div>

        <div id="result" class="mt-3" style="display: none;"></div>
    </div>

    <script>
        function uploadImage() {
            const input = document.getElementById('imageInput');
            const file = input.files[0];

            if (!file) {
                alert('请选择一个图片文件');
                return;
            }

            const formData = new FormData();
            formData.append('image', file);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info">上传中...</div>';

            console.log('开始上传:', {
                name: file.name,
                size: file.size,
                type: file.type,
                url: '{{ route("admin.upload.image") }}'
            });

            fetch('{{ route("admin.upload.image") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('响应状态:', response.status);
                return response.text().then(text => {
                    console.log('原始响应:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('响应不是JSON: ' + text.substring(0, 100));
                    }
                });
            })
            .then(result => {
                console.log('结果:', result);
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>上传成功！</h5>
                            <p><strong>URL:</strong> ${result.url}</p>
                            <p><strong>路径:</strong> ${result.path}</p>
                            <img src="${result.url}" style="max-width: 300px;" class="img-thumbnail mt-2">
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>上传失败</h5>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('错误:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>上传错误</h5>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }

        function testTinyMCE() {
            // 模拟TinyMCE的上传方式
            const input = document.getElementById('imageInput');
            const file = input.files[0];

            if (!file) {
                alert('请先选择一个图片文件');
                return;
            }

            const formData = new FormData();
            formData.append('image', file, file.name);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info">TinyMCE方式上传中...</div>';

            fetch('{{ route("admin.upload.image") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>TinyMCE方式上传成功！</h5>
                            <p><strong>URL:</strong> ${result.url}</p>
                            <img src="${result.url}" style="max-width: 300px;" class="img-thumbnail mt-2">
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>TinyMCE方式上传失败</h5>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>TinyMCE方式上传错误</h5>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }
    </script>
</body>
</html>
