<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Asset函数测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-image {
            max-width: 300px;
            max-height: 200px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin: 10px 0;
        }
        .image-test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .path-code {
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #dee2e6;
        }
        .url-display {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-image"></i> Laravel Asset函数测试
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
            <p class="mb-0">此页面使用Laravel的asset()函数来生成图片URL，可以检查静态资源是否能正常访问。</p>
        </div>

        <!-- 环境信息 -->
        <div class="image-test-card">
            <h3><i class="fas fa-server"></i> 环境信息</h3>
            <div class="row">
                <div class="col-md-6">
                    <strong>APP_URL:</strong> {{ config('app.url') }}<br>
                    <strong>当前URL:</strong> {{ url()->current() }}<br>
                    <strong>Laravel版本:</strong> {{ app()->version() }}
                </div>
                <div class="col-md-6">
                    <strong>环境:</strong> {{ app()->environment() }}<br>
                    <strong>调试模式:</strong> {{ config('app.debug') ? '开启' : '关闭' }}<br>
                    <strong>时间:</strong> {{ now()->format('Y-m-d H:i:s') }}
                </div>
            </div>
        </div>

        <!-- Banner目录图片测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-folder"></i> Banner目录图片测试</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>product.png</h5>
                    <div class="url-display mb-2">{{ asset('images/banner/product.png') }}</div>
                    <img src="{{ asset('images/banner/product.png') }}" 
                         alt="Product Banner" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                    <div class="mt-2">
                        <small class="text-muted">文件存在检查: 
                            @if(file_exists(public_path('images/banner/product.png')))
                                <span class="text-success">✅ 文件存在</span>
                            @else
                                <span class="text-danger">❌ 文件不存在</span>
                            @endif
                        </small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5>jjfa.png</h5>
                    <div class="url-display mb-2">{{ asset('images/banner/jjfa.png') }}</div>
                    <img src="{{ asset('images/banner/jjfa.png') }}" 
                         alt="JJFA Banner" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                    <div class="mt-2">
                        <small class="text-muted">文件存在检查: 
                            @if(file_exists(public_path('images/banner/jjfa.png')))
                                <span class="text-success">✅ 文件存在</span>
                            @else
                                <span class="text-danger">❌ 文件不存在</span>
                            @endif
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- JJFA目录图片测试 -->
        <div class="image-test-card">
            <h3><i class="fas fa-folder"></i> JJFA目录图片测试</h3>
            
            <div class="row">
                <div class="col-md-4">
                    <h5>4_.png</h5>
                    <div class="url-display mb-2">{{ asset('images/jjfa/4_.png') }}</div>
                    <img src="{{ asset('images/jjfa/4_.png') }}" 
                         alt="JJFA 4" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                    <div class="mt-2">
                        <small class="text-muted">文件存在检查: 
                            @if(file_exists(public_path('images/jjfa/4_.png')))
                                <span class="text-success">✅ 文件存在</span>
                            @else
                                <span class="text-danger">❌ 文件不存在</span>
                            @endif
                        </small>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <h5>5_AI.png</h5>
                    <div class="url-display mb-2">{{ asset('images/jjfa/5_AI.png') }}</div>
                    <img src="{{ asset('images/jjfa/5_AI.png') }}" 
                         alt="JJFA 5 AI" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                    <div class="mt-2">
                        <small class="text-muted">文件存在检查: 
                            @if(file_exists(public_path('images/jjfa/5_AI.png')))
                                <span class="text-success">✅ 文件存在</span>
                            @else
                                <span class="text-danger">❌ 文件不存在</span>
                            @endif
                        </small>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <h5>4_1.png</h5>
                    <div class="url-display mb-2">{{ asset('images/jjfa/4_1.png') }}</div>
                    <img src="{{ asset('images/jjfa/4_1.png') }}" 
                         alt="JJFA 4_1" 
                         class="test-image"
                         onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                    <div class="mt-2">
                        <small class="text-muted">文件存在检查: 
                            @if(file_exists(public_path('images/jjfa/4_1.png')))
                                <span class="text-success">✅ 文件存在</span>
                            @else
                                <span class="text-danger">❌ 文件不存在</span>
                            @endif
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件系统信息 -->
        <div class="image-test-card">
            <h3><i class="fas fa-folder-open"></i> 文件系统信息</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Public路径信息</h5>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <strong>public_path():</strong><br>
                            <code>{{ public_path() }}</code>
                        </li>
                        <li class="list-group-item">
                            <strong>images目录:</strong><br>
                            <code>{{ public_path('images') }}</code><br>
                            <small class="text-muted">
                                存在: {{ is_dir(public_path('images')) ? '✅ 是' : '❌ 否' }}
                            </small>
                        </li>
                        <li class="list-group-item">
                            <strong>banner目录:</strong><br>
                            <code>{{ public_path('images/banner') }}</code><br>
                            <small class="text-muted">
                                存在: {{ is_dir(public_path('images/banner')) ? '✅ 是' : '❌ 否' }}
                            </small>
                        </li>
                        <li class="list-group-item">
                            <strong>jjfa目录:</strong><br>
                            <code>{{ public_path('images/jjfa') }}</code><br>
                            <small class="text-muted">
                                存在: {{ is_dir(public_path('images/jjfa')) ? '✅ 是' : '❌ 否' }}
                            </small>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>文件权限检查</h5>
                    <ul class="list-group">
                        @php
                            $files = [
                                'images/banner/product.png',
                                'images/banner/jjfa.png',
                                'images/jjfa/4_.png',
                                'images/jjfa/5_AI.png',
                                'images/jjfa/4_1.png'
                            ];
                        @endphp
                        
                        @foreach($files as $file)
                            <li class="list-group-item">
                                <strong>{{ basename($file) }}:</strong><br>
                                @if(file_exists(public_path($file)))
                                    <span class="text-success">✅ 存在</span><br>
                                    <small class="text-muted">
                                        权限: {{ substr(sprintf('%o', fileperms(public_path($file))), -4) }}<br>
                                        大小: {{ number_format(filesize(public_path($file)) / 1024, 2) }} KB
                                    </small>
                                @else
                                    <span class="text-danger">❌ 不存在</span>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- 解决方案 -->
        <div class="image-test-card">
            <h3><i class="fas fa-tools"></i> 常见问题解决方案</h3>
            
            <div class="accordion" id="solutionAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingOne">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                            文件权限问题
                        </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#solutionAccordion">
                        <div class="accordion-body">
                            <strong>问题：</strong>文件存在但无法访问<br>
                            <strong>解决方案：</strong>
                            <pre class="bg-dark text-light p-2 rounded mt-2"><code>chmod 644 public/images/banner/*.png
chmod 644 public/images/jjfa/*.png
chmod 755 public/images/banner
chmod 755 public/images/jjfa</code></pre>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                            缓存问题
                        </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#solutionAccordion">
                        <div class="accordion-body">
                            <strong>问题：</strong>文件更新后仍显示旧版本或无法访问<br>
                            <strong>解决方案：</strong>
                            <pre class="bg-dark text-light p-2 rounded mt-2"><code>php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear</code></pre>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                            服务器配置问题
                        </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#solutionAccordion">
                        <div class="accordion-body">
                            <strong>问题：</strong>Web服务器无法提供静态文件<br>
                            <strong>解决方案：</strong>
                            <ul>
                                <li>检查.htaccess文件是否正确</li>
                                <li>确认Apache/Nginx配置允许访问public目录</li>
                                <li>重启Web服务器</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
