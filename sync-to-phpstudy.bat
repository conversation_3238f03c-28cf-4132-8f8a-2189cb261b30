@echo off
echo ========================================
echo 同步前端文件到PHPStudy部署目录
echo ========================================
echo.

set "SOURCE_DIR=c:\Users\<USER>\Desktop\hanwang-test-equipment"
set "TARGET_DIR=D:\phpstudy_pro\WWW\hanwang-test-equipment"

echo 源目录: %SOURCE_DIR%
echo 目标目录: %TARGET_DIR%
echo.

echo 1. 检查源目录是否存在...
if not exist "%SOURCE_DIR%" (
    echo ❌ 源目录不存在: %SOURCE_DIR%
    pause
    exit /b 1
)
echo ✅ 源目录存在

echo.
echo 2. 检查目标目录是否存在...
if not exist "%TARGET_DIR%" (
    echo 📁 创建目标目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%"
)
echo ✅ 目标目录准备完成

echo.
echo 3. 同步前端资源文件...

echo 📂 复制 resources 目录...
xcopy "%SOURCE_DIR%\resources" "%TARGET_DIR%\resources" /E /Y /I
if %errorlevel% == 0 (
    echo ✅ resources 目录复制完成
) else (
    echo ❌ resources 目录复制失败
)

echo.
echo 📂 复制 public 目录...
xcopy "%SOURCE_DIR%\public" "%TARGET_DIR%\public" /E /Y /I
if %errorlevel% == 0 (
    echo ✅ public 目录复制完成
) else (
    echo ❌ public 目录复制失败
)

echo.
echo 📂 复制前端配置文件...
copy "%SOURCE_DIR%\package.json" "%TARGET_DIR%\package.json" /Y
copy "%SOURCE_DIR%\vite.config.js" "%TARGET_DIR%\vite.config.js" /Y
if exist "%SOURCE_DIR%\package-lock.json" copy "%SOURCE_DIR%\package-lock.json" "%TARGET_DIR%\package-lock.json" /Y
if exist "%SOURCE_DIR%\yarn.lock" copy "%SOURCE_DIR%\yarn.lock" "%TARGET_DIR%\yarn.lock" /Y

echo.
echo 📂 复制Laravel核心文件...
copy "%SOURCE_DIR%\composer.json" "%TARGET_DIR%\composer.json" /Y
copy "%SOURCE_DIR%\composer.lock" "%TARGET_DIR%\composer.lock" /Y
copy "%SOURCE_DIR%\.env" "%TARGET_DIR%\.env" /Y
copy "%SOURCE_DIR%\artisan" "%TARGET_DIR%\artisan" /Y

echo.
echo 📂 复制应用程序文件...
xcopy "%SOURCE_DIR%\app" "%TARGET_DIR%\app" /E /Y /I
xcopy "%SOURCE_DIR%\config" "%TARGET_DIR%\config" /E /Y /I
xcopy "%SOURCE_DIR%\database" "%TARGET_DIR%\database" /E /Y /I
xcopy "%SOURCE_DIR%\routes" "%TARGET_DIR%\routes" /E /Y /I

echo.
echo 📂 复制其他重要文件...
xcopy "%SOURCE_DIR%\bootstrap" "%TARGET_DIR%\bootstrap" /E /Y /I
xcopy "%SOURCE_DIR%\storage" "%TARGET_DIR%\storage" /E /Y /I

echo.
echo ========================================
echo 文件同步完成！
echo ========================================
echo.
echo 接下来需要在目标目录中执行：
echo 1. composer install
echo 2. npm install
echo 3. php artisan key:generate
echo 4. php artisan storage:link
echo.
pause
