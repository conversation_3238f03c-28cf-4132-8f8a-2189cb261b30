@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-tachometer-alt me-2"></i>汉王测试设备管理系统 - 仪表板</h4>
                </div>

                <div class="card-body">
                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>设备总数</h5>
                                            <h2>{{ $totalEquipment }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-microchip fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>可用设备</h5>
                                            <h2>{{ $availableEquipment }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>使用中</h5>
                                            <h2>{{ $inUseEquipment }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-play-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>维护中</h5>
                                            <h2>{{ $maintenanceEquipment }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-tools fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试统计 -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>测试总数</h5>
                                            <h2>{{ $totalTests }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clipboard-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>通过测试</h5>
                                            <h2>{{ $passedTests }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5>失败测试</h5>
                                            <h2>{{ $failedTests }}</h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-times fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 设备分类统计 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-pie me-2"></i>设备分类统计</h5>
                                </div>
                                <div class="card-body">
                                    @if($categoryStats->count() > 0)
                                        @foreach($categoryStats as $category)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ $category->name }}</span>
                                            <span class="badge bg-primary">{{ $category->equipment_count }}</span>
                                        </div>
                                        @endforeach
                                    @else
                                        <p class="text-muted">暂无设备分类数据</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-history me-2"></i>最近测试记录</h5>
                                </div>
                                <div class="card-body">
                                    @if($recentTests->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>设备</th>
                                                        <th>测试类型</th>
                                                        <th>结果</th>
                                                        <th>时间</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($recentTests->take(5) as $test)
                                                    <tr>
                                                        <td>{{ $test->equipment->name ?? 'N/A' }}</td>
                                                        <td>{{ $test->test_type }}</td>
                                                        <td>
                                                            @if($test->result == 'pass')
                                                                <span class="badge bg-success">通过</span>
                                                            @elseif($test->result == 'fail')
                                                                <span class="badge bg-danger">失败</span>
                                                            @else
                                                                <span class="badge bg-secondary">未知</span>
                                                            @endif
                                                        </td>
                                                        <td>{{ $test->created_at->format('m-d H:i') }}</td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-muted">暂无测试记录</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-bolt me-2"></i>快速操作</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="{{ route('equipment.create') }}" class="btn btn-primary btn-lg w-100 mb-2">
                                                <i class="fas fa-plus me-2"></i>添加设备
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('test-records.create') }}" class="btn btn-success btn-lg w-100 mb-2">
                                                <i class="fas fa-play me-2"></i>开始测试
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('equipment.index') }}" class="btn btn-info btn-lg w-100 mb-2">
                                                <i class="fas fa-list me-2"></i>设备列表
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('maintenance-records.create') }}" class="btn btn-warning btn-lg w-100 mb-2">
                                                <i class="fas fa-wrench me-2"></i>维护记录
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
