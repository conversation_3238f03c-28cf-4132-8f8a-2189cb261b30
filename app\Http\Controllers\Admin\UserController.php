<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use App\Models\Admin;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * 显示管理员列表
     */
    public function index(Request $request)
    {
        // 检查权限：只有超级管理员可以访问
        if (!Auth::guard('admin')->check() || !Auth::guard('admin')->user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        $query = Admin::query();

        // 搜索功能
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        // 角色筛选
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.users.index', compact('users'));
    }

    /**
     * 显示创建管理员表单
     */
    public function create()
    {
        // 检查权限：只有超级管理员可以创建用户
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        return view('admin.users.create');
    }

    /**
     * 存储新管理员
     */
    public function store(Request $request)
    {
        // 检查权限：只有超级管理员可以创建用户
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:super_admin,admin,operator,user',
            'department' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean'
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'department' => $request->department,
            'phone' => $request->phone,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.users.index')
                        ->with('success', '用户创建成功！');
    }

    /**
     * 显示管理员详情
     */
    public function show(User $user)
    {
        // 检查权限：只有超级管理员可以查看用户详情
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        return view('admin.users.show', compact('user'));
    }

    /**
     * 显示编辑管理员表单
     */
    public function edit(User $user)
    {
        // 检查权限：只有超级管理员可以编辑用户
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限访问此页面');
        }

        return view('admin.users.edit', compact('user'));
    }

    /**
     * 更新管理员信息
     */
    public function update(Request $request, User $user)
    {
        // 检查权限：只有超级管理员可以编辑用户
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:super_admin,admin,operator,user',
            'department' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean'
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'department' => $request->department,
            'phone' => $request->phone,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.users.index')
                        ->with('success', '用户信息更新成功！');
    }

    /**
     * 删除管理员
     */
    public function destroy(User $user)
    {
        // 检查权限：只有超级管理员可以删除用户
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        // 不能删除自己
        if ($user->id === Auth::id()) {
            return redirect()->route('admin.users.index')
                            ->with('error', '不能删除自己的账号！');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
                        ->with('success', '用户删除成功！');
    }

    /**
     * 显示修改密码表单
     */
    public function showChangePasswordForm(User $user = null)
    {
        // 如果没有指定用户，则修改自己的密码
        if (!$user) {
            $user = Auth::user();
        } else {
            // 只有超级管理员可以修改其他用户的密码
            if (!Auth::user()->isSuperAdmin()) {
                abort(403, '您没有权限修改其他用户的密码');
            }
        }

        return view('admin.users.change-password', compact('user'));
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request, User $user = null)
    {
        // 如果没有指定用户，则修改自己的密码
        if (!$user) {
            $user = Auth::user();
            $isOwnPassword = true;
        } else {
            // 只有超级管理员可以修改其他用户的密码
            if (!Auth::user()->isSuperAdmin()) {
                abort(403, '您没有权限修改其他用户的密码');
            }
            $isOwnPassword = false;
        }

        // 验证规则
        $rules = [
            'new_password' => 'required|string|min:8|confirmed',
        ];

        // 如果是修改自己的密码，需要验证当前密码
        if ($isOwnPassword) {
            $rules['current_password'] = 'required|string';
        }

        $request->validate($rules);

        // 如果是修改自己的密码，验证当前密码
        if ($isOwnPassword && !Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => '当前密码不正确']);
        }

        // 更新密码
        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        $message = $isOwnPassword ? '密码修改成功！' : "用户 {$user->name} 的密码修改成功！";

        return redirect()->route('admin.users.index')
                        ->with('success', $message);
    }

    /**
     * 切换用户状态
     */
    public function toggleStatus(User $user)
    {
        // 检查权限：只有超级管理员可以切换用户状态
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, '您没有权限执行此操作');
        }

        // 不能禁用自己
        if ($user->id === Auth::id()) {
            return response()->json(['success' => false, 'message' => '不能禁用自己的账号！']);
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? '启用' : '禁用';
        return response()->json(['success' => true, 'message' => "用户已{$status}！"]);
    }
}
