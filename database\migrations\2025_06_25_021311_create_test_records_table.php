<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('equipment_id')->constrained('equipment')->comment('设备ID');
            $table->foreignId('user_id')->constrained('users')->comment('操作员ID');
            $table->string('test_type')->comment('测试类型');
            $table->string('test_name')->comment('测试名称');
            $table->datetime('start_time')->comment('测试开始时间');
            $table->datetime('end_time')->nullable()->comment('测试结束时间');
            $table->enum('status', ['pending', 'running', 'completed', 'failed', 'cancelled'])->default('pending')->comment('测试状态');
            $table->enum('result', ['pass', 'fail', 'unknown'])->nullable()->comment('测试结果');
            $table->json('test_data')->nullable()->comment('测试数据');
            $table->text('notes')->nullable()->comment('备注');
            $table->string('report_file')->nullable()->comment('测试报告文件路径');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_records');
    }
};
