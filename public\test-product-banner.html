<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品Banner图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-image {
            max-width: 100%;
            height: auto;
            border: 2px solid #007bff;
            border-radius: 5px;
            margin: 10px 0;
        }
        .url-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .status-success { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .banner-preview {
            height: 300px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 产品Banner图片路径测试</h1>
        
        <div class="test-section">
            <h2>📍 当前配置信息</h2>
            <p><strong>物理文件路径:</strong> <code>hanwang-test-equipment\public\images\banner\product.png</code></p>
            <p><strong>Laravel项目根目录:</strong> <code>hanwang-test-equipment</code></p>
            <p><strong>Web服务器地址:</strong> <code>http://localhost:8000</code></p>
        </div>

        <div class="test-section">
            <h2>🌐 实际调用地址测试</h2>
            
            <h3>1. 直接路径访问</h3>
            <div class="url-display">http://localhost:8000/images/banner/product.png</div>
            <img src="/images/banner/product.png" 
                 alt="Product Banner - 直接路径" 
                 class="test-image"
                 onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                 onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
            <div>加载状态: <span>检测中...</span></div>
            
            <h3>2. 完整URL访问</h3>
            <div class="url-display">http://localhost:8000/images/banner/product.png</div>
            <img src="http://localhost:8000/images/banner/product.png" 
                 alt="Product Banner - 完整URL" 
                 class="test-image"
                 onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                 onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
            <div>加载状态: <span>检测中...</span></div>
            
            <h3>3. 相对路径访问</h3>
            <div class="url-display">./images/banner/product.png</div>
            <img src="./images/banner/product.png" 
                 alt="Product Banner - 相对路径" 
                 class="test-image"
                 onload="this.nextElementSibling.innerHTML='<span class=status-success>✅ 加载成功</span>'"
                 onerror="this.nextElementSibling.innerHTML='<span class=status-error>❌ 加载失败</span>'">
            <div>加载状态: <span>检测中...</span></div>
        </div>

        <div class="test-section">
            <h2>🎨 Banner效果预览</h2>
            <p>模拟产品中心页面的banner效果：</p>
            <div class="banner-preview">
                <div style="background: url('/images/banner/product.png') right center/contain no-repeat; width: 100%; height: 100%; border-radius: 15px;"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 直接链接测试</h2>
            <p><a href="/images/banner/product.png" target="_blank">点击直接访问图片</a></p>
            <p><a href="http://localhost:8000/images/banner/product.png" target="_blank">点击访问完整URL</a></p>
        </div>

        <div class="test-section">
            <h2>🔍 文件诊断信息</h2>
            <div id="file-info">
                <p><strong>文件路径:</strong> public\images\banner\product.png</p>
                <p><strong>预期URL:</strong> http://localhost:8000/images/banner/product.png</p>
                <p><strong>文件状态:</strong> <span id="file-status">检测中...</span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Laravel Asset Helper 模拟</h2>
            <p>在Laravel中，<code>{{ asset('images/banner/product.png') }}</code> 会生成：</p>
            <div class="url-display">http://localhost:8000/images/banner/product.png</div>
        </div>
    </div>

    <script>
        // 页面加载完成后显示实际的URL信息
        window.onload = function() {
            console.log('当前页面URL:', window.location.href);
            console.log('图片实际调用地址:', window.location.origin + '/images/banner/product.png');
        }
    </script>
</body>
</html>
