<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    /**
     * 上传图片（用于富文本编辑器）
     */
    public function uploadImage(Request $request)
    {
        try {
            // 简单验证
            if (!$request->hasFile('image')) {
                return response()->json([
                    'success' => false,
                    'message' => '没有接收到图片文件'
                ], 400);
            }

            $image = $request->file('image');

            // 验证文件
            $request->validate([
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120'
            ]);

            // 确保目录存在
            $uploadDir = 'uploads/editor';
            if (!Storage::disk('public')->exists($uploadDir)) {
                Storage::disk('public')->makeDirectory($uploadDir);
            }

            // 生成文件名
            $filename = time() . '_' . Str::random(8) . '.' . $image->getClientOriginalExtension();

            // 保存文件
            $path = $image->storeAs($uploadDir, $filename, 'public');

            // 生成URL
            $url = asset('storage/' . $path);

            return response()->json([
                'success' => true,
                'url' => $url,
                'path' => $path,
                'filename' => $filename
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '上传失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 上传PDF文件
     */
    public function uploadPdf(Request $request)
    {
        try {
            // 验证文件
            if (!$request->hasFile('pdf')) {
                return response()->json([
                    'success' => false,
                    'message' => '没有接收到PDF文件'
                ], 400);
            }

            $pdf = $request->file('pdf');

            // 验证文件类型和大小
            $request->validate([
                'pdf' => 'required|file|mimes:pdf|max:10240' // 最大10MB
            ]);

            // 确保目录存在
            $uploadDir = 'uploads/pdfs';
            if (!Storage::disk('public')->exists($uploadDir)) {
                Storage::disk('public')->makeDirectory($uploadDir);
            }

            // 生成文件名（保留原始文件名）
            $originalName = pathinfo($pdf->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $pdf->getClientOriginalExtension();
            $filename = time() . '_' . Str::slug($originalName) . '.' . $extension;

            // 保存文件
            $path = $pdf->storeAs($uploadDir, $filename, 'public');

            // 生成URL
            $url = asset('storage/' . $path);

            return response()->json([
                'success' => true,
                'url' => $url,
                'path' => $path,
                'filename' => $filename,
                'original_name' => $pdf->getClientOriginalName(),
                'size' => $pdf->getSize()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '上传失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
