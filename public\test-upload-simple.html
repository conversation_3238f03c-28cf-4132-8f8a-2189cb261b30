<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        .preview img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片上传测试</h1>
        <p>这个页面用于测试图片上传功能是否正常工作。</p>
        
        <div class="upload-area" id="uploadArea">
            <p>点击选择图片或拖拽图片到这里</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择图片
            </button>
            <input type="file" id="fileInput" accept="image/*" onchange="handleFileSelect(this.files[0])">
        </div>
        
        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="preview" id="preview" style="display: none;">
            <h3>预览:</h3>
            <img id="previewImg" alt="预览图片">
        </div>
        
        <div class="result" id="result"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const preview = document.getElementById('preview');
        const previewImg = document.getElementById('previewImg');
        const result = document.getElementById('result');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file) return;

            // 显示预览
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // 上传文件
            uploadFile(file);
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('image', file);
            
            // 获取CSRF token (如果需要)
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken) {
                formData.append('_token', metaToken.getAttribute('content'));
            }

            // 显示进度条
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            // 显示上传信息
            showResult('开始上传...', 'info');

            const xhr = new XMLHttpRequest();

            // 进度监听
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                }
            });

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    progressContainer.style.display = 'none';
                    
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                showResult(`上传成功！
文件路径: ${response.path || 'N/A'}
访问URL: ${response.url || 'N/A'}
文件名: ${response.filename || 'N/A'}`, 'success');
                            } else {
                                showResult(`上传失败: ${response.message || '未知错误'}`, 'error');
                            }
                        } catch (e) {
                            showResult(`响应解析失败: ${e.message}
原始响应: ${xhr.responseText}`, 'error');
                        }
                    } else {
                        showResult(`HTTP错误: ${xhr.status} ${xhr.statusText}
响应内容: ${xhr.responseText}`, 'error');
                    }
                }
            };

            xhr.onerror = function() {
                progressContainer.style.display = 'none';
                showResult('网络错误，请检查网络连接', 'error');
            };

            // 发送请求到Laravel上传接口
            xhr.open('POST', '/admin/upload/image', true);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            xhr.send(formData);
        }

        function showResult(message, type) {
            result.textContent = message;
            result.className = 'result ' + type;
        }

        // 页面加载时显示环境信息
        window.addEventListener('load', () => {
            showResult(`测试环境信息:
当前URL: ${window.location.href}
用户代理: ${navigator.userAgent}
上传接口: /admin/upload/image
时间: ${new Date().toLocaleString()}`, 'info');
        });
    </script>
</body>
</html>
