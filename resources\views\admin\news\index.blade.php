@extends('admin.layouts.app')

@section('title', '新闻管理')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item active">新闻管理</li>
@endsection

@section('content')
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-newspaper me-2"></i>新闻管理
        </h1>
        <p class="text-muted">管理网站新闻资讯</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ route('admin.news-categories.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-folder me-2"></i>分类管理
        </a>
        <a href="{{ route('admin.news.create') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>添加新闻
        </a>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.news.index') }}">
            <div class="row g-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search"
                           placeholder="搜索标题、摘要、作者..."
                           value="{{ request('search') }}">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="category">
                        <option value="">所有分类</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}"
                                    {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">所有状态</option>
                        <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>已发布</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>草稿</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>新闻列表
        </h5>
    </div>
    <div class="card-body">
        @if($news->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>新闻信息</th>
                            <th>分类</th>
                            <th>作者/来源</th>
                            <th>浏览量</th>
                            <th>状态</th>
                            <th>发布时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($news as $item)
                            <tr>
                                <td>{{ $item->id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($item->image)
                                            <img src="{{ Storage::url($item->image) }}"
                                                 alt="{{ $item->title }}"
                                                 class="rounded me-3"
                                                 style="width: 60px; height: 60px; object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <strong>{{ Str::limit($item->title, 40) }}</strong>
                                            @if($item->summary)
                                                <br><small class="text-muted">{{ Str::limit($item->summary, 60) }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($item->category)
                                        <span class="badge" style="background-color: {{ $item->category->color }};">
                                            {{ $item->category->name }}
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">未分类</span>
                                    @endif
                                </td>
                                <td>
                                    @if($item->author)
                                        <div><strong>{{ $item->author }}</strong></div>
                                    @endif
                                    @if($item->source)
                                        <small class="text-muted">{{ $item->source }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ $item->views }}</span>
                                </td>
                                <td>
                                    <div>
                                        @if($item->is_published)
                                            <span class="badge bg-success">已发布</span>
                                        @else
                                            <span class="badge bg-secondary">草稿</span>
                                        @endif
                                    </div>
                                    @if($item->is_featured)
                                        <div class="mt-1">
                                            <span class="badge bg-warning">推荐</span>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    @if($item->published_at)
                                        {{ $item->published_at->format('Y-m-d') }}
                                    @else
                                        <span class="text-muted">未发布</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.news.show', $item) }}"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.news.edit', $item) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.news.destroy', $item) }}"
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('确定要删除这条新闻吗？')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center">
                {{ $news->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无新闻</h4>
                <p class="text-muted">点击上方按钮添加第一条新闻</p>
                <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加新闻
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
