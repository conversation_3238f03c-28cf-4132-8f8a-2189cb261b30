<?php $__env->startSection('title', '编辑新闻分类'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.news-categories.index')); ?>">新闻分类</a></li>
    <li class="breadcrumb-item active">编辑分类</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-edit me-2"></i>编辑新闻分类
    </h1>
    <p class="text-muted">编辑分类：<?php echo e($newsCategory->name); ?></p>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.news-categories.update', $newsCategory)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="name" name="name" value="<?php echo e(old('name', $newsCategory->name)); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">别名</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="slug" name="slug" value="<?php echo e(old('slug', $newsCategory->slug)); ?>" 
                                       placeholder="留空自动生成">
                                <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">用于URL，只能包含字母、数字和连字符</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">分类描述</label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="description" name="description" rows="3"><?php echo e(old('description', $newsCategory->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">分类颜色</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="color" name="color" value="<?php echo e(old('color', $newsCategory->color)); ?>">
                                    <input type="text" class="form-control <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="color-text" value="<?php echo e(old('color', $newsCategory->color)); ?>" readonly>
                                </div>
                                <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">用于前台显示时的标识颜色</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">排序</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $newsCategory->sort_order)); ?>" min="0">
                                <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">数字越小排序越靠前</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" <?php echo e(old('is_active', $newsCategory->is_active) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                启用分类
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>更新分类
                        </button>
                        <a href="<?php echo e(route('admin.news-categories.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        <a href="<?php echo e(route('admin.news-categories.show', $newsCategory)); ?>" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>查看详情
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>分类信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>ID：</strong></td>
                        <td><?php echo e($newsCategory->id); ?></td>
                    </tr>
                    <tr>
                        <td><strong>创建时间：</strong></td>
                        <td><?php echo e($newsCategory->created_at->format('Y-m-d H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>更新时间：</strong></td>
                        <td><?php echo e($newsCategory->updated_at->format('Y-m-d H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>新闻数量：</strong></td>
                        <td>
                            <span class="badge bg-primary"><?php echo e($newsCategory->news()->count()); ?></span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-palette me-2"></i>推荐颜色
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <button type="button" class="btn btn-sm border color-preset" 
                            style="background-color: #1e4a8c; width: 40px; height: 30px;" 
                            data-color="#1e4a8c" title="睿测微蓝"></button>
                    <button type="button" class="btn btn-sm border color-preset" 
                            style="background-color: #28a745; width: 40px; height: 30px;" 
                            data-color="#28a745" title="成功绿"></button>
                    <button type="button" class="btn btn-sm border color-preset" 
                            style="background-color: #dc3545; width: 40px; height: 30px;" 
                            data-color="#dc3545" title="警告红"></button>
                    <button type="button" class="btn btn-sm border color-preset" 
                            style="background-color: #ffc107; width: 40px; height: 30px;" 
                            data-color="#ffc107" title="警告黄"></button>
                    <button type="button" class="btn btn-sm border color-preset" 
                            style="background-color: #17a2b8; width: 40px; height: 30px;" 
                            data-color="#17a2b8" title="信息蓝"></button>
                    <button type="button" class="btn btn-sm border color-preset" 
                            style="background-color: #6f42c1; width: 40px; height: 30px;" 
                            data-color="#6f42c1" title="紫色"></button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>提示
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>编辑说明</h6>
                    <ul class="mb-0">
                        <li>修改分类名称会影响前台显示</li>
                        <li>修改别名会影响URL地址</li>
                        <li>禁用分类会隐藏其下所有新闻</li>
                    </ul>
                </div>
                
                <?php if($newsCategory->news()->count() > 0): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>注意</h6>
                        <p class="mb-0">该分类下有 <?php echo e($newsCategory->news()->count()); ?> 条新闻，删除前请先处理这些新闻。</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // 自动生成别名
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '') // 移除特殊字符
            .replace(/\s+/g, '-') // 空格替换为连字符
            .replace(/-+/g, '-') // 多个连字符替换为单个
            .trim('-'); // 移除首尾连字符
        
        if (document.getElementById('slug').value === '' || document.getElementById('slug').value === '<?php echo e($newsCategory->slug); ?>') {
            document.getElementById('slug').value = slug;
        }
    });
    
    // 颜色选择器同步
    document.getElementById('color').addEventListener('input', function() {
        document.getElementById('color-text').value = this.value;
    });
    
    // 预设颜色选择
    document.querySelectorAll('.color-preset').forEach(function(button) {
        button.addEventListener('click', function() {
            const color = this.getAttribute('data-color');
            document.getElementById('color').value = color;
            document.getElementById('color-text').value = color;
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/news-categories/edit.blade.php ENDPATH**/ ?>