@extends('admin.layouts.app')

@section('title', '添加产品')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.products.index') }}">产品管理</a></li>
    <li class="breadcrumb-item active">添加产品</li>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-plus me-2"></i>添加产品
    </h1>
    <p class="text-muted">创建新的产品</p>
</div>

<form action="{{ route('admin.products.store') }}" method="POST" enctype="multipart/form-data" onsubmit="return validateForm()">
    @csrf

    <div class="row">
        <div class="col-lg-8">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">产品别名</label>
                                <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                       id="slug" name="slug" value="{{ old('slug') }}"
                                       placeholder="留空自动生成">
                                @error('slug')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">产品分类 <span class="text-danger">*</span></label>
                                <select class="form-select @error('category_id') is-invalid @enderror"
                                        id="category_id" name="category_id" required>
                                    <option value="">选择分类</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ old('category_id', $selectedCategory) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">产品型号</label>
                                <input type="text" class="form-control @error('model') is-invalid @enderror"
                                       id="model" name="model" value="{{ old('model') }}">
                                @error('model')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="brand" class="form-label">产品品牌</label>
                        <input type="text" class="form-control @error('brand') is-invalid @enderror"
                               id="brand" name="brand" value="{{ old('brand', '睿测微') }}">
                        @error('brand')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">产品描述</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">产品详情</label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content" name="content" rows="15">{{ old('content') }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">支持富文本编辑，可插入图片</div>
                    </div>
                </div>
            </div>

            <!-- 产品图片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>产品图片
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="image" class="form-label">主图</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" accept="image/*" onchange="uploadMainImage(this)">
                        <input type="hidden" name="image" id="image_path" value="">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">支持 JPEG、PNG、JPG、GIF 格式，最大 2MB</div>
                        <div id="image-preview" class="mt-2" style="display: none;">
                            <img id="preview-img" src="" alt="预览图" class="img-thumbnail" style="max-width: 200px;">
                            <div class="mt-1">
                                <small class="text-success">✅ 图片已上传</small>
                            </div>
                        </div>
                        <div id="upload-progress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">上传中...</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="images" class="form-label">产品图片集</label>
                        <input type="file" class="form-control @error('images.*') is-invalid @enderror"
                               id="images" accept="image/*" multiple onchange="uploadMultipleImages(this)">
                        <input type="hidden" name="images" id="images_paths" value="">
                        @error('images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">可选择多张图片</div>
                        <div id="images-preview" class="mt-2" style="display: none;">
                            <div class="row" id="images-container"></div>
                        </div>
                        <div id="images-upload-progress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">上传中...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品规格 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ul me-2"></i>产品规格
                    </h5>
                </div>
                <div class="card-body">
                    <div id="specifications">
                        <div class="row mb-2 spec-row">
                            <div class="col-md-4">
                                <input type="text" class="form-control" name="spec_names[]" placeholder="规格名称">
                            </div>
                            <div class="col-md-6">
                                <input type="text" class="form-control" name="spec_values[]" placeholder="规格值">
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-danger remove-spec">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-primary" id="add-spec">
                        <i class="fas fa-plus me-2"></i>添加规格
                    </button>
                </div>
            </div>

            <!-- SEO设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>SEO设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">SEO标题</label>
                        <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                               id="meta_title" name="meta_title" value="{{ old('meta_title') }}">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">SEO描述</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                  id="meta_description" name="meta_description" rows="3">{{ old('meta_description') }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">SEO关键词</label>
                        <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror"
                               id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords') }}"
                               placeholder="用逗号分隔多个关键词">
                        @error('meta_keywords')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 发布设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>发布设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">排序</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                        @error('sort_order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">数字越小排序越靠前</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_published" name="is_published"
                                   value="1" {{ old('is_published', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                发布产品
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                   value="1" {{ old('is_featured') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_featured">
                                推荐产品
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>提示
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>产品说明</h6>
                        <ul class="mb-0">
                            <li>产品名称和分类是必填项</li>
                            <li>别名用于生成友好的URL</li>
                            <li>主图用于列表页显示</li>
                            <li>图片集用于详情页展示</li>
                            <li>规格参数可动态添加</li>
                        </ul>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>建议</h6>
                        <ul class="mb-0">
                            <li>上传高质量的产品图片</li>
                            <li>填写详细的产品描述</li>
                            <li>设置合适的SEO信息</li>
                            <li>选择正确的产品分类</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存产品
                        </button>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<!-- TinyMCE CSS -->
<style>
    .tox-tinymce {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
    }
    .tox-editor-header {
        border-bottom: 1px solid #ced4da !important;
    }
</style>
@endpush

@push('scripts')
<!-- TinyMCE JS -->
<link rel="stylesheet" href="{{ asset('assets/css/simple-editor.css') }}">
<script src="{{ asset('assets/js/simple-editor.js') }}"></script>
<script>
// 调试信息
console.log('页面加载完成');
console.log('CSRF Token:', '{{ csrf_token() }}');
console.log('上传URL:', '{{ route("admin.upload.image") }}');
console.log('APP_URL:', '{{ config("app.url") }}');

// 初始化简化富文本编辑器
let contentEditor;
document.addEventListener('DOMContentLoaded', function() {
    contentEditor = initSimpleEditor('#content', {
        height: '400px',
        placeholder: '请输入产品详细内容...',
        uploadUrl: '{{ route("admin.upload.image") }}',
        csrfToken: '{{ csrf_token() }}'
    });
});
</script>
<script>
    // 表单验证函数
    function validateForm() {
        let isValid = true;
        let errorMessages = [];

        // 验证产品名称
        const name = document.getElementById('name').value.trim();
        if (!name) {
            errorMessages.push('请输入产品名称');
            document.getElementById('name').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('name').classList.remove('is-invalid');
        }

        // 验证产品分类
        const categoryId = document.getElementById('category_id').value;
        if (!categoryId) {
            errorMessages.push('请选择产品分类');
            document.getElementById('category_id').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('category_id').classList.remove('is-invalid');
        }

        // 内容已通过编辑器自动同步到textarea

        // 检查主图文件大小
        const imageInput = document.getElementById('image');
        if (imageInput.files.length > 0) {
            const file = imageInput.files[0];
            if (file.size > 2048 * 1024) {
                errorMessages.push('主图文件大小不能超过2MB');
                isValid = false;
            }
        }

        // 检查多图文件大小
        const imagesInput = document.getElementById('images');
        if (imagesInput.files.length > 0) {
            for (let i = 0; i < imagesInput.files.length; i++) {
                const file = imagesInput.files[i];
                if (file.size > 2048 * 1024) {
                    errorMessages.push('图片 ' + (i+1) + ' 文件大小不能超过2MB');
                    isValid = false;
                    break;
                }
            }
        }

        // 显示错误信息
        if (!isValid) {
            alert('请完善以下信息：\n' + errorMessages.join('\n'));
            return false;
        }

        console.log('表单验证通过，开始提交');
        return true;
    }

    // TinyMCE已替换为简化富文本编辑器




    // 图片预览功能
    function previewImage(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview-img').src = e.target.result;
                document.getElementById('image-preview').style.display = 'block';
            }
            reader.readAsDataURL(input.files[0]);
        }
    }

    // 自动生成别名
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');

        document.getElementById('slug').value = slug;
    });

    // 添加规格
    document.getElementById('add-spec').addEventListener('click', function() {
        const specificationsDiv = document.getElementById('specifications');
        const newRow = document.createElement('div');
        newRow.className = 'row mb-2 spec-row';
        newRow.innerHTML = `
            <div class="col-md-4">
                <input type="text" class="form-control" name="spec_names[]" placeholder="规格名称">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" name="spec_values[]" placeholder="规格值">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger remove-spec">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        specificationsDiv.appendChild(newRow);
    });

    // 删除规格
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-spec')) {
            const row = e.target.closest('.spec-row');
            if (document.querySelectorAll('.spec-row').length > 1) {
                row.remove();
            }
        }
    });

    // 上传主图
    function uploadMainImage(input) {
        if (!input.files || input.files.length === 0) return;

        const file = input.files[0];
        const formData = new FormData();
        formData.append('image', file);
        formData.append('_token', '{{ csrf_token() }}');

        // 显示上传进度
        document.getElementById('upload-progress').style.display = 'block';
        document.getElementById('image-preview').style.display = 'none';

        fetch('{{ route("admin.upload.image") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            document.getElementById('upload-progress').style.display = 'none';

            if (result.success) {
                // 保存图片路径到隐藏字段
                document.getElementById('image_path').value = result.path;

                // 显示预览
                document.getElementById('preview-img').src = result.url;
                document.getElementById('image-preview').style.display = 'block';
            } else {
                alert('图片上传失败: ' + result.message);
            }
        })
        .catch(error => {
            document.getElementById('upload-progress').style.display = 'none';
            alert('上传错误: ' + error.message);
        });
    }

    // 上传多图
    function uploadMultipleImages(input) {
        if (!input.files || input.files.length === 0) return;

        const files = Array.from(input.files);
        const uploadedPaths = [];
        let uploadedCount = 0;

        // 显示上传进度
        document.getElementById('images-upload-progress').style.display = 'block';
        document.getElementById('images-preview').style.display = 'none';

        const container = document.getElementById('images-container');
        container.innerHTML = '';

        files.forEach((file, index) => {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('_token', '{{ csrf_token() }}');

            fetch('{{ route("admin.upload.image") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                uploadedCount++;

                if (result.success) {
                    uploadedPaths.push(result.path);

                    // 添加预览
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <div class="position-relative">
                            <img src="${result.url}" class="img-thumbnail" style="width: 100%; height: 150px; object-fit: cover;">
                            <small class="text-success d-block text-center mt-1">✅ 图片 ${index + 1}</small>
                        </div>
                    `;
                    container.appendChild(col);
                } else {
                    // 显示错误
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <div class="alert alert-danger p-2">
                            <small>图片 ${index + 1} 上传失败</small>
                        </div>
                    `;
                    container.appendChild(col);
                }

                // 更新进度
                const progress = (uploadedCount / files.length) * 100;
                document.querySelector('#images-upload-progress .progress-bar').style.width = progress + '%';

                // 所有图片上传完成
                if (uploadedCount === files.length) {
                    document.getElementById('images-upload-progress').style.display = 'none';
                    document.getElementById('images-preview').style.display = 'block';

                    // 保存所有图片路径
                    document.getElementById('images_paths').value = JSON.stringify(uploadedPaths);
                }
            })
            .catch(error => {
                uploadedCount++;
                console.error('图片上传错误:', error);

                // 显示错误
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = `
                    <div class="alert alert-danger p-2">
                        <small>图片 ${index + 1} 上传失败</small>
                    </div>
                `;
                container.appendChild(col);

                // 更新进度
                const progress = (uploadedCount / files.length) * 100;
                document.querySelector('#images-upload-progress .progress-bar').style.width = progress + '%';

                if (uploadedCount === files.length) {
                    document.getElementById('images-upload-progress').style.display = 'none';
                    document.getElementById('images-preview').style.display = 'block';
                    document.getElementById('images_paths').value = JSON.stringify(uploadedPaths);
                }
            });
        });
    }
</script>
@endpush
