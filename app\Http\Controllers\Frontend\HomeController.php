<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\News;
use App\Models\Category;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * 显示首页
     */
    public function index()
    {
        // 推荐产品 (首页展示)
        $featuredProducts = Product::with('category')
            ->published()
            ->featured()
            ->ordered()
            ->take(6)
            ->get();

        // 最新新闻
        $latestNews = News::with('category')
            ->published()
            ->latest('published_at')
            ->take(6)
            ->get();

        // 产品分类
        $categories = Category::active()
            ->ordered()
            ->withCount(['products' => function($query) {
                $query->published();
            }])
            ->take(8)
            ->get();

        // 推荐新闻
        $featuredNews = News::with('category')
            ->published()
            ->featured()
            ->latest('published_at')
            ->take(3)
            ->get();

        return view('frontend.home', compact('featuredProducts', 'latestNews', 'categories', 'featuredNews'));
    }
}
