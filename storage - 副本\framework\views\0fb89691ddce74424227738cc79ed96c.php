<?php $__env->startSection('title', '系统设置'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item active">系统设置</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-cog me-2"></i>系统设置
    </h1>
    <p class="text-muted">管理系统配置和参数</p>
</div>

<div class="row">
    <!-- 网站基本信息 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-globe me-2"></i>网站基本信息
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="site_name" class="form-label">网站名称</label>
                        <input type="text" class="form-control" id="site_name" value="睿测微智能科技">
                    </div>
                    <div class="mb-3">
                        <label for="site_description" class="form-label">网站描述</label>
                        <textarea class="form-control" id="site_description" rows="3">专业的芯片智能测试设备提供商</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="site_keywords" class="form-label">关键词</label>
                        <input type="text" class="form-control" id="site_keywords" value="芯片测试,智能设备,睿测微">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>保存设置
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 联系信息 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-phone me-2"></i>联系信息
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="company_phone1" class="form-label">联系电话1</label>
                        <input type="text" class="form-control" id="company_phone1" value="139-1726-8669">
                    </div>
                    <div class="mb-3">
                        <label for="company_phone2" class="form-label">联系电话2</label>
                        <input type="text" class="form-control" id="company_phone2" value="158-2179-6586">
                    </div>
                    <div class="mb-3">
                        <label for="company_address" class="form-label">公司地址</label>
                        <textarea class="form-control" id="company_address" rows="3">上海市杨浦区长白新村街道军工路1076号机械共性研究院401室</textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>保存设置
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 系统维护 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-tools me-2"></i>系统维护
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-sync me-2"></i>清除缓存
                    </button>
                    <button class="btn btn-outline-warning">
                        <i class="fas fa-database me-2"></i>优化数据库
                    </button>
                    <button class="btn btn-outline-info">
                        <i class="fas fa-download me-2"></i>备份数据
                    </button>
                    <button class="btn btn-outline-success">
                        <i class="fas fa-check me-2"></i>系统检测
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>系统信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>系统版本：</strong></td>
                        <td>睿测微管理系统 v1.0</td>
                    </tr>
                    <tr>
                        <td><strong>Laravel版本：</strong></td>
                        <td><?php echo e(app()->version()); ?></td>
                    </tr>
                    <tr>
                        <td><strong>PHP版本：</strong></td>
                        <td><?php echo e(PHP_VERSION); ?></td>
                    </tr>
                    <tr>
                        <td><strong>数据库：</strong></td>
                        <td>MySQL</td>
                    </tr>
                    <tr>
                        <td><strong>服务器时间：</strong></td>
                        <td><?php echo e(now()->format('Y-m-d H:i:s')); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/settings.blade.php ENDPATH**/ ?>