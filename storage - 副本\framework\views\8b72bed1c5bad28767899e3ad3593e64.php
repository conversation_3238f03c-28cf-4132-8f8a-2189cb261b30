<?php $__env->startSection('title', '管理员管理'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">管理员管理</h1>
                    <p class="text-muted mb-0">管理系统用户账号和权限</p>
                </div>
                <a href="<?php echo e(route('admin.admin-users.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加管理员
                </a>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.admin-users.index')); ?>">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">搜索</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo e(request('search')); ?>" placeholder="姓名、邮箱、部门">
                            </div>
                            <div class="col-md-3">
                                <label for="role" class="form-label">角色</label>
                                <select class="form-select" id="role" name="role">
                                    <option value="">全部角色</option>
                                    <option value="super_admin" <?php echo e(request('role') === 'super_admin' ? 'selected' : ''); ?>>超级管理员</option>
                                    <option value="admin" <?php echo e(request('role') === 'admin' ? 'selected' : ''); ?>>管理员</option>
                                    <option value="operator" <?php echo e(request('role') === 'operator' ? 'selected' : ''); ?>>操作员</option>
                                    <option value="user" <?php echo e(request('role') === 'user' ? 'selected' : ''); ?>>普通用户</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">全部状态</option>
                                    <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>启用</option>
                                    <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>禁用</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>用户列表
                        <span class="badge bg-secondary ms-2"><?php echo e($users->total()); ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if($users->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>用户信息</th>
                                        <th>角色</th>
                                        <th>部门</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle me-3">
                                                        <?php echo e(substr($user->name, 0, 1)); ?>

                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?php echo e($user->name); ?></div>
                                                        <small class="text-muted"><?php echo e($user->email); ?></small>
                                                        <?php if($user->phone): ?>
                                                            <br><small class="text-muted"><?php echo e($user->phone); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                    $roleColors = [
                                                        'super_admin' => 'danger',
                                                        'admin' => 'primary',
                                                        'operator' => 'info',
                                                        'user' => 'secondary'
                                                    ];
                                                ?>
                                                <span class="badge bg-<?php echo e($roleColors[$user->role] ?? 'secondary'); ?>">
                                                    <?php echo e($user->getRoleDisplayName()); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php echo e($user->department ?? '-'); ?>

                                            </td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input status-toggle" 
                                                           type="checkbox" 
                                                           <?php echo e($user->is_active ? 'checked' : ''); ?>

                                                           <?php echo e($user->id === auth()->id() ? 'disabled' : ''); ?>

                                                           data-user-id="<?php echo e($user->id); ?>">
                                                    <label class="form-check-label">
                                                        <?php echo e($user->is_active ? '启用' : '禁用'); ?>

                                                    </label>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo e($user->created_at->format('Y-m-d H:i')); ?>

                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.admin-users.show', $user)); ?>"
                                                       class="btn btn-sm btn-outline-info" title="查看详情">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.admin-users.edit', $user)); ?>"
                                                       class="btn btn-sm btn-outline-primary" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.admin-users.change-password', $user)); ?>"
                                                       class="btn btn-sm btn-outline-warning" title="修改密码">
                                                        <i class="fas fa-key"></i>
                                                    </a>
                                                    <?php if($user->id !== Auth::guard('admin')->id()): ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger delete-user"
                                                                data-user-id="<?php echo e($user->id); ?>"
                                                                data-user-name="<?php echo e($user->name); ?>"
                                                                title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无用户数据</h5>
                            <p class="text-muted">点击上方按钮添加第一个用户</p>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if($users->hasPages()): ?>
                    <div class="card-footer">
                        <?php echo e($users->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                <p class="text-danger small">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.status-toggle {
    cursor: pointer;
}

.btn-group .btn {
    margin-right: 2px;
}

.table td {
    vertical-align: middle;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 状态切换
    document.querySelectorAll('.status-toggle').forEach(function(toggle) {
        toggle.addEventListener('change', function() {
            const userId = this.dataset.userId;
            const isActive = this.checked;

            fetch(`<?php echo e(route('admin.admin-users.index')); ?>/${userId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新标签文本
                    const label = this.nextElementSibling;
                    label.textContent = isActive ? '启用' : '禁用';

                    // 显示成功消息
                    showAlert('success', data.message);
                } else {
                    // 恢复开关状态
                    this.checked = !isActive;
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                // 恢复开关状态
                this.checked = !isActive;
                showAlert('error', '操作失败，请重试');
            });
        });
    });

    // 删除用户
    document.querySelectorAll('.delete-user').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const userName = this.dataset.userName;

            document.getElementById('deleteUserName').textContent = userName;
            document.getElementById('deleteForm').action = `<?php echo e(route('admin.admin-users.index')); ?>/${userId}`;

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
});

function showAlert(type, message) {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/users/index.blade.php ENDPATH**/ ?>