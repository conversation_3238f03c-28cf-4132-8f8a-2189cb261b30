<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipment', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('equipment_categories')->comment('设备分类ID');
            $table->string('name')->comment('设备名称');
            $table->string('model')->comment('设备型号');
            $table->string('serial_number')->unique()->comment('设备序列号');
            $table->string('manufacturer')->comment('制造商');
            $table->date('purchase_date')->nullable()->comment('采购日期');
            $table->decimal('purchase_price', 12, 2)->nullable()->comment('采购价格');
            $table->enum('status', ['available', 'in_use', 'maintenance', 'retired'])->default('available')->comment('设备状态');
            $table->string('location')->nullable()->comment('设备位置');
            $table->text('specifications')->nullable()->comment('技术规格');
            $table->text('description')->nullable()->comment('设备描述');
            $table->json('parameters')->nullable()->comment('设备参数配置');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipment');
    }
};
