<?php $__env->startSection('title', '上海睿测微智能科技有限公司 - 芯片智能测试设备专家'); ?>
<?php $__env->startSection('description', '上海睿测微智能科技有限公司专注芯片智能测试设备研发，提供高低温测试系统、芯片性能评估、可靠性测试等专业解决方案'); ?>

<?php $__env->startSection('content'); ?>
<style>
.product-card {
    transition: all 0.3s ease;
    border-radius: 16px !important;
    overflow: hidden;
    background: #ffffff;
    border: 1px solid var(--border-color) !important;
    box-shadow: var(--shadow-medium) !important;
    position: relative;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--light-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy) !important;
    border-color: var(--secondary-blue) !important;
}

.product-card:hover::before {
    opacity: 1;
}

.product-image {
    border-radius: 16px 16px 0 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--light-gray), #e2e8f0);
}

.product-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-image::before {
    opacity: 1;
}

/* 应用领域布局样式 - 围绕中心图片 */
.application-domains-layout {
    min-height: 400px;
    position: relative;
    padding: 1rem 0;
    max-width: 900px;
    margin: 0 auto;
}

.domain-position {
    position: absolute;
    width: 280px;
    z-index: 2;
}

.domain-top-left {
    top: 0;
    left: 0;
}

.domain-top-right {
    top: 0;
    right: 0;
}

.domain-bottom-left {
    bottom: 0;
    left: 0;
}

.domain-bottom-right {
    bottom: 0;
    right: 0;
}

.domain-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    padding: 1rem;
    position: relative;
    overflow: hidden;
}

.domain-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.domain-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.domain-card:hover::before {
    opacity: 1;
}

.domain-icon {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    z-index: 3;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.domain-icon i {
    font-size: 1rem;
    color: #64748b;
    transition: all 0.3s ease;
}

.domain-card:hover .domain-icon {
    background: rgba(255, 255, 255, 0.95);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.domain-card:hover .domain-icon i {
    color: #3b82f6;
    transform: scale(1.05);
}

.domain-card h5 {
    position: relative;
    z-index: 3;
    font-size: 1rem;
    color: #1e293b;
    font-weight: 600;
}

.domain-card p {
    position: relative;
    z-index: 3;
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.5;
}

/* 中间图形元素 */
.central-graphic {
    z-index: 10;
}

.central-image {
    transition: all 0.3s ease;
}

.central-image img {
    max-width: 220px;
    height: auto;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
    filter: drop-shadow(0 3px 10px rgba(0, 0, 0, 0.1));
}

.central-image:hover img {
    transform: scale(1.02);
    filter: drop-shadow(0 6px 20px rgba(0, 0, 0, 0.15));
}

/* 核心技术样式 */
.core-metric {
    padding: 1rem;
}

.metric-value {
    color: #3b82f6;
}

.metric-label {
    color: #1e293b;
}

.metric-desc {
    color: #64748b;
    line-height: 1.5;
}

/* 技术流程图样式 */
.tech-flow-container {
    padding: 1rem 0;
}

.flow-line-container {
    height: 4px;
}

.flow-line {
    border-radius: 2px;
}

.step-circle {
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.step-circle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.tech-step-card {
    transition: all 0.3s ease;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.tech-step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
    border-color: #3b82f6 !important;
}

.tech-step-wrapper {
    position: relative;
}

/* 详细功能特性样式 */
.detailed-features {
    margin-top: 2rem;
}

.feature-section {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.feature-section:hover {
    border-left-color: #3b82f6;
    transform: translateX(5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.feature-icon i {
    font-size: 1.2rem;
}

.sub-icon i {
    font-size: 1rem;
}

.feature-group {
    position: relative;
}

.feature-list {
    border-left: 2px solid #e2e8f0;
    padding-left: 1rem;
}

.feature-item {
    position: relative;
}

.bullet-point {
    flex-shrink: 0;
}

.feature-group .fw-bold {
    color: #1e293b;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .application-domains-layout {
        min-height: auto;
        position: static;
        padding: 1rem 0;
    }

    .domain-position {
        position: static;
        width: 100%;
        margin-bottom: 2rem;
    }

    .domain-card {
        text-align: left !important;
    }

    .domain-card .d-flex {
        justify-content: flex-start !important;
    }

    .domain-card h5 {
        order: 2;
        margin-left: 12px !important;
        margin-right: 0 !important;
    }

    .domain-card .domain-icon {
        order: 1;
    }

    .central-graphic {
        position: static;
        transform: none;
        text-align: center;
        margin: 2rem 0;
    }

    .flow-line-container {
        display: none !important;
    }

    .tech-flow-container {
        padding: 1rem 0;
    }

    .tech-step-card {
        min-height: auto;
    }
}

@media (min-width: 993px) {
    .domain-position {
        max-width: 320px;
    }
}

/* 公司大记事时间轴样式 */
.company-timeline {
    position: relative;
    padding: 40px 0;
}

.timeline-line {
    z-index: 1;
    background: #ddd !important;
}

.timeline-event {
    position: relative;
    z-index: 2;
}

.timeline-node {
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-node:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3) !important;
}

.timeline-content {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    background: #fafafa;
}

.timeline-event:hover .timeline-content {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    border-color: #2196f3;
    background: #fff;
}

/* 确保时间轴节点在正确位置 */
@media (min-width: 768px) {
    .company-timeline .timeline-line {
        top: 120px !important;
    }
}

/* 紫色文字样式 */
.text-purple {
    color: #8b5cf6 !important;
}

/* 内容区域样式 */
.content-section ul li {
    transition: all 0.2s ease;
    padding: 8px 0;
    border-radius: 4px;
}

.content-section ul li:hover {
    background-color: #f8fafc;
    padding-left: 10px;
}

/* 统一设计系统 */
:root {
    --primary-blue: #1e40af;
    --secondary-blue: #3b82f6;
    --light-blue: #60a5fa;
    --dark-slate: #1e293b;
    --medium-slate: #334155;
    --light-gray: #f8fafc;
    --border-color: rgba(59, 130, 246, 0.1);
    --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Hero Section 样式 */
.hero-section {
    background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(30, 64, 175, 0.03) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.03) 0%, transparent 40%);
    pointer-events: none;
}

/* 恢复原来的圆形卡片样式 */
.content-card {
    transition: all 0.3s ease;
    border-radius: 20px;
    overflow: hidden;
}

.content-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2) !important;
}

/* 统一的内容区域样式 */
.content-section {
    background: #ffffff;
    position: relative;
}

.content-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 49%, rgba(30, 64, 175, 0.01) 50%, transparent 51%),
        linear-gradient(-45deg, transparent 49%, rgba(59, 130, 246, 0.01) 50%, transparent 51%);
    pointer-events: none;
}

/* 统一的卡片样式 */
.unified-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.unified-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--light-blue));
}

.unified-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--secondary-blue);
}


</style>
<!-- 企业视频展示 -->
<section class="py-2 bg-light">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="content-image position-relative" style="height: 500px; background: url('<?php echo e(asset('images/index/1_.png')); ?>') center/cover; cursor: pointer;" onclick="playVideo()">
                        <!-- 播放按钮覆盖层 -->
                        <div class="video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                            <div class="play-button">
                                <i class="fas fa-play-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 视频模态框 -->
<div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <video id="heroVideo" class="w-100" controls>
                    <source src="<?php echo e(asset('videos/index.mp4')); ?>" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            </div>
        </div>
    </div>
</div>

<!-- 公司介绍 -->
<section class="content-section py-2">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="unified-card">
                    <div class="card-body p-4">
                        <div class="text-left mb-3">
                            <h2 class="h1 fw-bold text-dark mb-3">关于睿测微</h2>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <p class="fs-5 text-dark lh-base mb-4">
                                    上海睿测微智能科技有限公司专注于高端芯片高低温测试设备的研发与销售，致力于为国内外半导体企业提供智能化、自动化的芯片测试解决方案。
                                    公司依托上海理工大学的科研力量与国内知名存储芯片企业长鑫存储的产业资源，在2021年由上海理工大学联合国内头部存储芯片企业长鑫存储共同
                                    启动，推动测试设备领域的技术革新与产品升级。
                                </p>
                                <p class="fs-5 text-dark lh-base mb-3">
                                    目前，我们的设备已取得专利10余项，在申请专利30余项，多项核心技术处于行业领先地位，并获得人民网、光明日报等权威媒体广泛报道，树立了
                                    良好的专业形象与社会影响力。
                                </p>
                            </div>
                        </div>

                        <!-- 数据统计 -->
                        <div class="row g-4 text-center mt-4">
                            <div class="col-md-3">
                                <div class="p-4">
                                    <h2 class="display-4 fw-bold text-dark mb-2">10+</h2>
                                    <h5 class="fw-bold text-dark mb-2">已获专利</h5>
                                    <p class="text-muted mb-0">核心技术获得国家专利保护</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-4">
                                    <h2 class="display-4 fw-bold text-dark mb-2">30+</h2>
                                    <h5 class="fw-bold text-dark mb-2">申请专利</h5>
                                    <p class="text-muted mb-0">持续创新推动技术突破</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-4">
                                    <h2 class="display-4 fw-bold text-dark mb-2">12+</h2>
                                    <h5 class="fw-bold text-dark mb-2">媒体报道</h5>
                                    <p class="text-muted mb-0">国内主流媒体报道</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-4">
                                    <h2 class="display-4 fw-bold text-dark mb-2">4年</h2>
                                    <h5 class="fw-bold text-dark mb-2">研发历程</h5>
                                    <p class="text-muted mb-0">专注芯片测试设备创新</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 产品中心 -->
<section class="content-section py-2 bg-light">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="unified-card">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h2 class="h1 fw-bold mb-3" style="color: var(--dark-slate);">产品中心</h2>
                            <div class="mx-auto" style="width: 60px; height: 3px; background: linear-gradient(90deg, var(--primary-blue), var(--light-blue));"></div>
                        </div>

                        <!-- 产品展示网格 -->
                        <div class="row g-3">
                            <?php if($featuredProducts->count() > 0): ?>
                                <?php $__currentLoopData = $featuredProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                                        <div class="product-card card h-100 border-0 shadow">
                                            <div class="product-image" style="height: 200px; position: relative; overflow: hidden;">
                                                <?php if($product->image): ?>
                                                    <img src="<?php echo e(config('app.url')); ?>/storage/<?php echo e($product->image); ?>"
                                                         alt="<?php echo e($product->name); ?>"
                                                         class="w-100 h-100"
                                                         style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="d-flex align-items-center justify-content-center h-100"
                                                         style="background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);">
                                                        <i class="fas fa-microchip text-white" style="font-size: 3rem;"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="card-body p-4">
                                                <h5 class="fw-bold mb-2"><?php echo e($product->name); ?></h5>
                                                <p class="text-muted small mb-3">
                                                    <?php echo e($product->excerpt ?: '专业的芯片测试设备解决方案'); ?>

                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <a href="<?php echo e(route('products.show', $product->slug)); ?>"
                                                       class="btn btn-outline-primary btn-sm">查看详情</a>
                                                    <?php if($product->category): ?>
                                                        <small class="text-muted"><?php echo e($product->category->name); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <!-- 如果没有推荐产品，显示默认产品 -->
                                <div class="col-lg-4 col-md-6" data-aos="fade-up">
                                    <div class="product-card card h-100 border-0 shadow">
                                        <div class="product-image" style="height: 200px; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); position: relative;">
                                            <div class="d-flex align-items-center justify-content-center h-100">
                                                <i class="fas fa-microchip text-white" style="font-size: 3rem;"></i>
                                            </div>
                                        </div>
                                        <div class="card-body p-4">
                                            <h5 class="fw-bold mb-2">STD-03系列</h5>
                                            <p class="text-muted small mb-3">高精度智能温控系统，支持-80°C至600°C极端温度测试</p>
                                            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-outline-primary btn-sm">查看详情</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                                    <div class="product-card card h-100 border-0 shadow">
                                        <div class="product-image" style="height: 200px; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); position: relative;">
                                            <div class="d-flex align-items-center justify-content-center h-100">
                                                <i class="fas fa-cogs text-white" style="font-size: 3rem;"></i>
                                            </div>
                                        </div>
                                        <div class="card-body p-4">
                                            <h5 class="fw-bold mb-2">STD-02系列</h5>
                                            <p class="text-muted small mb-3">多通道并行测试系统，高效率批量芯片测试解决方案</p>
                                            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-outline-primary btn-sm">查看详情</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                                    <div class="product-card card h-100 border-0 shadow">
                                        <div class="product-image" style="height: 200px; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); position: relative;">
                                            <div class="d-flex align-items-center justify-content-center h-100">
                                                <i class="fas fa-thermometer-half text-white" style="font-size: 3rem;"></i>
                                            </div>
                                        </div>
                                        <div class="card-body p-4">
                                            <h5 class="fw-bold mb-2">STD-TN系列</h5>
                                            <p class="text-muted small mb-3">专为高速信号完整性测试设计，确保测试结果高精确性</p>
                                            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-outline-primary btn-sm">查看详情</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>



                        <!-- 查看更多按钮 -->
                        <div class="text-center mt-4">
                            <a href="<?php echo e(route('products.index')); ?>" class="btn btn-primary btn-lg px-5 py-3">
                                <i class="fas fa-th-large me-2"></i>查看全部产品
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 睿测微 - 核心技术 -->
<section class="py-2">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-4">
                        <!-- 标题部分 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h2 class="h2 fw-bold text-dark mb-0">睿测微 - 核心技术</h2>
                            <a href="#whitepaper-section" class="btn btn-outline-primary btn-sm rounded-pill px-3" onclick="document.querySelector('#whitepaper-section').scrollIntoView({behavior: 'smooth'});">
                                <i class="fas fa-download me-1"></i>下载白皮书
                            </a>
                        </div>

                        <!-- 三大核心指标 -->
                        <div class="row g-3 mb-3">
                            <!-- 温控精度 -->
                            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
                                <div class="core-metric text-center">
                                    <div class="metric-value text-primary fw-bold mb-2" style="font-size: 2.5rem;">±0.05℃</div>
                                    <div class="metric-label fw-bold mb-3" style="font-size: 1.1rem;">温控精度</div>
                                    <p class="metric-desc text-muted small">业界领先的温度控制精确度，确保测试结果高度一致性</p>
                                </div>
                            </div>

                            <!-- 最大功率支持 -->
                            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
                                <div class="core-metric text-center">
                                    <div class="metric-value text-primary fw-bold mb-2" style="font-size: 2.5rem;">1500W</div>
                                    <div class="metric-label fw-bold mb-3" style="font-size: 1.1rem;">最大功率支持</div>
                                    <p class="metric-desc text-muted small">即使在极低温环境下仍能安全测试高功率芯片</p>
                                </div>
                            </div>

                            <!-- 温度范围 -->
                            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
                                <div class="core-metric text-center">
                                    <div class="metric-value text-primary fw-bold mb-2" style="font-size: 2.5rem;">-80~600℃</div>
                                    <div class="metric-label fw-bold mb-3" style="font-size: 1.1rem;">温度范围</div>
                                    <p class="metric-desc text-muted small">超宽测试范围覆盖从极低温到极高温的各类应用场景</p>
                                </div>
                            </div>
                        </div>

                        <!-- 产品描述 -->
                        <div class="product-description mb-3" data-aos="fade-up" data-aos-delay="400">
                            <p class="text-muted lh-base">
                                睿测微高低温芯片测试系统支持接触式高温600℃至低温-80℃的极限测试环境，温控精度达±0.05℃，确保测试数据的高重复性与可靠性。通过专利温控算法，系统能够维持稳定测试环境，消除温度波动对芯片性能评估的影响。
                            </p>
                        </div>

                        <!-- 技术流程图 -->
                        <div class="tech-flow-container position-relative" data-aos="fade-up" data-aos-delay="500">
                            <!-- 连接线背景 -->
                            <div class="flow-line-container position-absolute w-100" style="top: 30px; z-index: 1;">
                                <div class="row">
                                    <div class="col-4 text-center">
                                        <div class="flow-line bg-primary" style="height: 4px; margin: 0 20%;"></div>
                                    </div>
                                    <div class="col-4 text-center">
                                        <div class="flow-line bg-primary" style="height: 4px; margin: 0 20%;"></div>
                                    </div>
                                    <div class="col-4 text-center">
                                        <div class="flow-line bg-primary" style="height: 4px; margin: 0 20%;"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row position-relative" style="z-index: 2;">
                                <!-- 步骤1 -->
                                <div class="col-lg-4 mb-3">
                                    <div class="tech-step-wrapper text-center">
                                        <!-- 圆形编号 -->
                                        <div class="step-circle bg-primary text-white rounded-circle d-flex align-items-center justify-content-center fw-bold mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.3rem;">1</div>
                                        <!-- 卡片内容 -->
                                        <div class="tech-step-card p-4 bg-white rounded-3 shadow-sm border" style="border-color: #e2e8f0 !important;">
                                            <h6 class="fw-bold mb-3 text-dark">自研智能Socket技术</h6>
                                            <p class="text-muted small mb-0 lh-base">
                                                实时监控芯片电气接触状态，确保测试过程中的连接可靠性。内置多点压力传感器，检测微小接触阻抗变化，为测试数据提供质量保障。
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤2 -->
                                <div class="col-lg-4 mb-3">
                                    <div class="tech-step-wrapper text-center">
                                        <!-- 圆形编号 -->
                                        <div class="step-circle bg-primary text-white rounded-circle d-flex align-items-center justify-content-center fw-bold mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.3rem;">2</div>
                                        <!-- 卡片内容 -->
                                        <div class="tech-step-card p-4 bg-white rounded-3 shadow-sm border" style="border-color: #e2e8f0 !important;">
                                            <h6 class="fw-bold mb-3 text-dark">大地+壳体共接地架构</h6>
                                            <p class="text-muted small mb-0 lh-base">
                                                解决温度冲击中流动冷媒和悬空冷板在高速信号测试中的天线效应，消除干扰、回路不闭合、信号畸变问题。
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤3 -->
                                <div class="col-lg-4 mb-3">
                                    <div class="tech-step-wrapper text-center">
                                        <!-- 圆形编号 -->
                                        <div class="step-circle bg-primary text-white rounded-circle d-flex align-items-center justify-content-center fw-bold mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.3rem;">3</div>
                                        <!-- 卡片内容 -->
                                        <div class="tech-step-card p-4 bg-white rounded-3 shadow-sm border" style="border-color: #e2e8f0 !important;">
                                            <h6 class="fw-bold mb-3 text-dark">高速信号完整性与电磁兼容</h6>
                                            <p class="text-muted small mb-0 lh-base">
                                                睿测微独家采用「大地+壳体共接地」技术架构，彻底解决温度冲击中流动冷媒和悬空冷板在高速信号测试中的干扰问题。多层金属结构电磁兼容屏蔽系统有效隔离EMC和RFI噪声，屏蔽效率达-60dB以上，满足国际主流汽车、工业及医疗电子EMC标准要求。
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细功能特性 -->
                        <div class="detailed-features mt-3" data-aos="fade-up" data-aos-delay="600">
                            <div class="row g-3">
                                <!-- 左侧：智能计划管理与人机互动操作 -->
                                <div class="col-lg-6">
                                    <div class="feature-section p-4 bg-white rounded-3 shadow-sm border h-100" style="border-color: #e2e8f0 !important;">
                                        <!-- 主标题 -->
                                        <div class="d-flex align-items-center mb-4">
                                            <div class="feature-icon me-3">
                                                <i class="fas fa-caret-down text-primary"></i>
                                            </div>
                                            <h5 class="fw-bold text-dark mb-0">智能计划管理与人机互动操作</h5>
                                        </div>

                                        <!-- 测试计划管理 -->
                                        <div class="feature-group mb-4">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="sub-icon me-3">
                                                    <i class="fas fa-caret-down text-secondary"></i>
                                                </div>
                                                <h6 class="fw-bold text-dark mb-0">测试计划管理</h6>
                                            </div>
                                            <div class="feature-list ms-4">
                                                <div class="feature-item d-flex align-items-start mb-2">
                                                    <div class="bullet-point me-2 mt-1">
                                                        <i class="fas fa-circle text-primary" style="font-size: 0.4rem;"></i>
                                                    </div>
                                                    <p class="text-muted small mb-0 lh-base">
                                                        计划执行过程与结果完整记录于SD卡，便于回溯。支持参数批量导入与模板复用，提高工作效率
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 自动Debug功能 -->
                                        <div class="feature-group mb-4">
                                            <div class="feature-list ms-4">
                                                <p class="fw-bold text-dark mb-2 small">自动Debug功能</p>
                                                <div class="feature-item d-flex align-items-start mb-2">
                                                    <div class="bullet-point me-2 mt-1">
                                                        <i class="fas fa-circle text-primary" style="font-size: 0.4rem;"></i>
                                                    </div>
                                                    <p class="text-muted small mb-0 lh-base">全过程结果自动分析与故障诊断</p>
                                                </div>
                                                <div class="feature-item d-flex align-items-start mb-2">
                                                    <div class="bullet-point me-2 mt-1">
                                                        <i class="fas fa-circle text-primary" style="font-size: 0.4rem;"></i>
                                                    </div>
                                                    <p class="text-muted small mb-0 lh-base">智能建议优化方案，缩短问题解决时间</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 人机交互界面 -->
                                        <div class="feature-group">
                                            <p class="fw-bold text-dark mb-2 small">人机交互界面</p>
                                            <div class="feature-list ms-4">
                                                <div class="feature-item d-flex align-items-start mb-2">
                                                    <div class="bullet-point me-2 mt-1">
                                                        <i class="fas fa-circle text-primary" style="font-size: 0.4rem;"></i>
                                                    </div>
                                                    <p class="text-muted small mb-0 lh-base">配备7/10寸高清触摸屏，提供简便直观</p>
                                                </div>
                                                <div class="feature-item d-flex align-items-start mb-2">
                                                    <div class="bullet-point me-2 mt-1">
                                                        <i class="fas fa-circle text-primary" style="font-size: 0.4rem;"></i>
                                                    </div>
                                                    <p class="text-muted small mb-0 lh-base">支持上位机软件远程操作（RJ-</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：芯片高低温设备多模式温度环境选择 -->
                                <div class="col-lg-6">
                                    <div class="feature-section p-4 bg-white rounded-3 shadow-sm border h-100" style="border-color: #e2e8f0 !important;">
                                        <!-- 主标题 -->
                                        <div class="d-flex align-items-center mb-4">
                                            <div class="feature-icon me-3">
                                                <i class="fas fa-caret-down text-primary"></i>
                                            </div>
                                            <h5 class="fw-bold text-dark mb-0">芯片高低温设备多模式温度环境选择</h5>
                                        </div>

                                        <!-- 接触式温度环境 -->
                                        <div class="feature-group mb-4">
                                            <p class="fw-bold text-dark mb-3 small">接触式温度环境</p>
                                            <div class="feature-item mb-3">
                                                <p class="text-muted small mb-0 lh-base">
                                                    通过热头直接触芯片表面进行温度控制，实现快速温变精确温度，适用于需要高精度温度控制的研发测试场景。变速率最高可达50°C/min，满足温度冲击测试需求。
                                                </p>
                                            </div>
                                        </div>

                                        <!-- 温箱式温度环境 -->
                                        <div class="feature-group mb-4">
                                            <p class="fw-bold text-dark mb-3 small">温箱式温度环境</p>
                                            <div class="feature-item mb-3">
                                                <p class="text-muted small mb-0 lh-base">
                                                    模拟封闭式温箱提供均匀稳定的温度测试环境，温度均匀性控制在±0.5°C以内，确保批次/效性。
                                                </p>
                                            </div>
                                        </div>

                                        <!-- 温度循环模式 -->
                                        <div class="feature-group">
                                            <p class="fw-bold text-dark mb-3 small">温度循环模式</p>
                                            <div class="feature-item">
                                                <p class="text-muted small mb-0 lh-base">
                                                    提供高温值·常温值·低温值循环冲击，循环参数可自设定，满足不同封装与开发阶段的多样化测试需求，支持自动切换，提升测试灵活性。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 广泛的应用领域 -->
<section class="py-2 bg-light">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-4">
                        <!-- 标题部分 -->
                        <div class="text-center mb-3">
                            <h2 class="h2 fw-bold text-dark mb-0">广泛的应用领域</h2>
                        </div>

                        <!-- 应用领域布局 - 围绕中心图片 -->
                        <div class="application-domains-layout position-relative">
                            <!-- 航天航空与国防 - 左上 -->
                            <div class="domain-position domain-top-left" data-aos="fade-up" data-aos-delay="100">
                                <div class="domain-card text-left">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="domain-icon me-3">
                                            <i class="fas fa-rocket"></i>
                                        </div>
                                        <h5 class="fw-bold mb-0 text-dark">航天航空与国防</h5>
                                    </div>
                                    <p class="text-muted mb-0 lh-base">
                                        应用于卫星、导弹、飞机等领域，测试芯片在极端环境下的可靠性，确保关键设备在恶劣条件下的稳定运行。
                                    </p>
                                </div>
                            </div>

                            <!-- 汽车工业 - 右上 -->
                            <div class="domain-position domain-top-right" data-aos="fade-up" data-aos-delay="200">
                                <div class="domain-card text-right">
                                    <div class="d-flex align-items-center justify-content-end mb-3">
                                        <h5 class="fw-bold mb-0 text-dark me-3">汽车工业</h5>
                                        <div class="domain-icon">
                                            <i class="fas fa-car"></i>
                                        </div>
                                    </div>
                                    <p class="text-muted mb-0 lh-base">
                                        应用于汽车芯片测试，测试芯片在极端低温和高温环境下的自适应性和可靠性，保障汽车电子系统安全。
                                    </p>
                                </div>
                            </div>

                            <!-- 高性能计算 - 左下 -->
                            <div class="domain-position domain-bottom-left" data-aos="fade-up" data-aos-delay="300">
                                <div class="domain-card text-left">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="domain-icon me-3">
                                            <i class="fas fa-microchip"></i>
                                        </div>
                                        <h5 class="fw-bold mb-0 text-dark">高性能计算</h5>
                                    </div>
                                    <p class="text-muted mb-0 lh-base">
                                        为图像处理 (GPU)、加速处理单元 (APU)、高性能计算 (HPC) 芯片提供全面测试，确保高负载下的稳定性。
                                    </p>
                                </div>
                            </div>

                            <!-- 电子通信领域 - 右下 -->
                            <div class="domain-position domain-bottom-right" data-aos="fade-up" data-aos-delay="400">
                                <div class="domain-card text-right">
                                    <div class="d-flex align-items-center justify-content-end mb-3">
                                        <h5 class="fw-bold mb-0 text-dark me-3">电子通信领域</h5>
                                        <div class="domain-icon">
                                            <i class="fas fa-broadcast-tower"></i>
                                        </div>
                                    </div>
                                    <p class="text-muted mb-0 lh-base">
                                        应用于电子通信、计算机、通信设备等领域，测试芯片在不同场景下的性能表现，确保电子产品的稳定运行。
                                    </p>
                                </div>
                            </div>

                            <!-- 中间图形元素 -->
                            <div class="central-graphic position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10;">
                                <div class="central-image">
                                    <img src="<?php echo e(asset('images/index/11-1.png')); ?>" alt="应用领域连接图" class="img-fluid">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 睿测微大记事 -->
<section class="py-2">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-4">
                        <!-- 标题部分 -->
                        <div class="text-left mb-3">
                            <h2 class="h1 fw-bold text-dark mb-2">睿测微大记事</h2>
                            <p class="text-muted fs-6 mb-3">了解睿测微最新动态、产品更新及行业资讯，把握芯片测试领域的前沿趋势。</p>
                        </div>

                        <!-- 时间轴部分 -->
                        <div class="company-timeline mb-3">
                            <!-- 时间轴线和节点 -->
                            <div class="position-relative">
                                <!-- 水平时间轴线 -->
                                <div class="timeline-line position-absolute" style="top: 120px; left: 10%; right: 10%; height: 2px; background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #e2e8f0 100%);"></div>

                                <!-- 上排事件 -->
                                <div class="row position-relative mb-4">
                                    <!-- 2021年08月 -->
                                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                                        <div class="timeline-event text-center">
                                            <div class="timeline-content bg-white p-3 rounded-3 shadow-sm mb-3" style="min-height: 120px;">
                                                <h6 class="fw-bold text-dark mb-2">2021年08月</h6>
                                                <p class="text-muted small mb-0 lh-base">
                                                    上海睿测微智能科技有限公司正式成立，专注于芯片智能测试温控系统的研发与销售
                                                </p>
                                            </div>
                                            <div class="timeline-node mx-auto d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 50%; color: #1976d2; font-weight: bold; font-size: 0.9rem;">
                                                1
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 2023年10月 -->
                                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                                        <div class="timeline-event text-center">
                                            <div class="timeline-content bg-white p-3 rounded-3 shadow-sm mb-3" style="min-height: 120px;">
                                                <h6 class="fw-bold text-dark mb-2">2023年10月</h6>
                                                <p class="text-muted small mb-0 lh-base">
                                                    与复旦大学微电子学院深度合作，共同开发高智能高精度温控芯片测试系统，开创国产芯片环境测试新纪元
                                                </p>
                                            </div>
                                            <div class="timeline-node mx-auto d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 50%; color: #1976d2; font-weight: bold; font-size: 0.9rem;">
                                                3
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 2024年12月 -->
                                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                                        <div class="timeline-event text-center">
                                            <div class="timeline-content bg-white p-3 rounded-3 shadow-sm mb-3" style="min-height: 120px;">
                                                <h6 class="fw-bold text-dark mb-2">2024年12月</h6>
                                                <p class="text-muted small mb-0 lh-base">
                                                    自主研发推出国内首支支持-80°C至+600°C的芯片智能三温测试系统
                                                </p>
                                            </div>
                                            <div class="timeline-node mx-auto d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 50%; color: #1976d2; font-weight: bold; font-size: 0.9rem;">
                                                5
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 下排事件 -->
                                <div class="row position-relative" style="margin-top: 20px;">
                                    <div class="col-md-2"></div>
                                    <!-- 2022年02月 -->
                                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
                                        <div class="timeline-event text-center">
                                            <div class="timeline-node mx-auto d-flex align-items-center justify-content-center mb-3" style="width: 35px; height: 35px; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 50%; color: #1976d2; font-weight: bold; font-size: 0.9rem;">
                                                2
                                            </div>
                                            <div class="timeline-content bg-white p-3 rounded-3 shadow-sm" style="min-height: 120px;">
                                                <h6 class="fw-bold text-dark mb-2">2022年02月</h6>
                                                <p class="text-muted small mb-0 lh-base">
                                                    首批5套开行智能测试系统在长三角地区成功应用，系统具备稳定的温控能力与智能数据反馈
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 2024年9月 -->
                                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="500">
                                        <div class="timeline-event text-center">
                                            <div class="timeline-node mx-auto d-flex align-items-center justify-content-center mb-3" style="width: 35px; height: 35px; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 50%; color: #1976d2; font-weight: bold; font-size: 0.9rem;">
                                                4
                                            </div>
                                            <div class="timeline-content bg-white p-3 rounded-3 shadow-sm" style="min-height: 120px;">
                                                <h6 class="fw-bold text-dark mb-2">2024年9月</h6>
                                                <p class="text-muted small mb-0 lh-base">
                                                    成功参展第二十四届中国国际工业博览会，全面展示睿测微最新芯片测试解决方案，获得多家媒体与客户广泛关注
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部内容区域 -->
                        <div class="row" data-aos="fade-up" data-aos-delay="600">
                            <!-- 技术白皮书 -->
                            <div class="col-md-6 mb-4" id="whitepaper-section">
                                <div class="content-section">
                                    <h4 class="fw-bold text-dark mb-3">
                                        <i class="fas fa-file-alt text-primary me-2"></i>技术白皮书
                                    </h4>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                                            <a href="<?php echo e(asset('download/睿测微芯片高低温测试系统 - 核心技术.pdf')); ?>"
                                               class="text-muted small text-decoration-none"
                                               download="睿测微芯片高低温测试系统 - 核心技术.pdf"
                                               target="_blank">
                                               《睿测微芯片高低温测试系统 - 核心技术》
                                            </a>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                                            <a href="<?php echo e(asset('download/睿测微智能-专业芯片高低温测试解决方案.pdf')); ?>"
                                               class="text-muted small text-decoration-none"
                                               download="睿测微智能-专业芯片高低温测试解决方案.pdf"
                                               target="_blank">
                                               《睿测微智能 - 专业芯片高低温测试解决方案》
                                            </a>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                                            <a href="<?php echo e(asset('download/睿测微小实验室芯片三温智能气候环境测试系统.pdf')); ?>"
                                               class="text-muted small text-decoration-none"
                                               download="睿测微小实验室芯片三温智能气候环境测试系统.pdf"
                                               target="_blank">
                                               《睿测微小实验室芯片三温智能气候环境测试系统》
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 媒体报道 -->
                            <div class="col-md-6 mb-4">
                                <div class="content-section">
                                    <h4 class="fw-bold text-dark mb-3">
                                        <i class="fas fa-newspaper text-success me-2"></i>媒体报道
                                    </h4>
                                    <ul class="list-unstyled">
                                        <?php if($featuredNews->count() > 0): ?>
                                            <?php $__currentLoopData = $featuredNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li class="mb-2">
                                                    <i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>
                                                    <a href="<?php echo e(route('news.show', $news->slug)); ?>" class="text-muted small text-decoration-none">
                                                        <?php echo e($news->title); ?>

                                                    </a>
                                                </li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <li class="mb-2">
                                                <i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>
                                                <span class="text-muted small">暂无媒体报道</span>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .video-overlay {
        background: rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
    }

    .content-image:hover .video-overlay {
        background: rgba(0, 0, 0, 0.5);
    }

    .play-button {
        font-size: 5rem;
        color: rgba(255, 255, 255, 0.95);
        transition: all 0.3s ease;
        text-shadow: 0 4px 15px rgba(0, 0, 0, 0.6);
        filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.3));
    }

    .content-image:hover .play-button {
        transform: scale(1.15);
        color: #60a5fa;
        text-shadow: 0 6px 20px rgba(0, 0, 0, 0.8);
        filter: drop-shadow(0 0 30px rgba(96, 165, 250, 0.6));
    }

    .content-card {
        transition: all 0.3s ease;
        border-radius: 20px;
        overflow: hidden;
    }

    .content-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2) !important;
    }

    .content-image {
        position: relative;
        overflow: hidden;
    }

    .content-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        transition: all 0.3s ease;
    }

    .content-card:hover .content-overlay {
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    }

    /* 视频模态框样式 */
    .modal-content {
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }

    .modal-body video {
        border-radius: 0 0 15px 15px;
    }

    /* 移动端按钮样式 */
    @media (max-width: 768px) {
        .btn-play {
            width: 80px;
            height: 80px;
            font-size: 1.8rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function playVideo() {
    // 显示视频模态框
    var videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
    videoModal.show();

    // 播放视频
    var video = document.getElementById('heroVideo');
    video.play();
}

// 当模态框关闭时暂停视频
document.getElementById('videoModal').addEventListener('hidden.bs.modal', function () {
    var video = document.getElementById('heroVideo');
    video.pause();
    video.currentTime = 0; // 重置到开始位置
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/frontend/home.blade.php ENDPATH**/ ?>