@extends('admin.layouts.app')

@section('title', '编辑用户')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">编辑用户</h1>
                    <p class="text-muted mb-0">修改用户 <strong>{{ $user->name }}</strong> 的信息</p>
                </div>
                <div class="btn-group">
                    <a href="{{ route('admin.admin-users.change-password', $user) }}" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>修改密码
                    </a>
                    <a href="{{ route('admin.admin-users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回列表
                    </a>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-edit me-2"></i>用户信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.admin-users.update', $user) }}">
                                @csrf
                                @method('PUT')
                                
                                <div class="row">
                                    <!-- 基本信息 -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="role" class="form-label">角色 <span class="text-danger">*</span></label>
                                            <select class="form-select @error('role') is-invalid @enderror" 
                                                    id="role" name="role" required>
                                                <option value="">请选择角色</option>
                                                <option value="super_admin" {{ old('role', $user->role) === 'super_admin' ? 'selected' : '' }}>超级管理员</option>
                                                <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>管理员</option>
                                                <option value="operator" {{ old('role', $user->role) === 'operator' ? 'selected' : '' }}>操作员</option>
                                                <option value="user" {{ old('role', $user->role) === 'user' ? 'selected' : '' }}>普通用户</option>
                                            </select>
                                            @error('role')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="department" class="form-label">部门</label>
                                            <input type="text" class="form-control @error('department') is-invalid @enderror" 
                                                   id="department" name="department" value="{{ old('department', $user->department) }}" 
                                                   placeholder="如：技术部、市场部等">
                                            @error('department')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">手机号</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone', $user->phone) }}" 
                                                   placeholder="手机号码">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">账号状态</label>
                                            <select class="form-select @error('status') is-invalid @enderror"
                                                    id="status" name="status" required
                                                    {{ $user->id === Auth::guard('admin')->id() ? 'disabled' : '' }}>
                                                <option value="active" {{ old('status', $user->status) === 'active' ? 'selected' : '' }}>启用</option>
                                                <option value="inactive" {{ old('status', $user->status) === 'inactive' ? 'selected' : '' }}>禁用</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            @if($user->id === Auth::guard('admin')->id())
                                                <small class="form-text text-muted">不能禁用自己的账号</small>
                                            @else
                                                <small class="form-text text-muted">禁用的账号无法登录系统</small>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- 账号信息 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="alert alert-light">
                                            <h6 class="alert-heading">
                                                <i class="fas fa-info-circle me-2"></i>账号信息
                                            </h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <small class="text-muted">创建时间：</small>
                                                    <strong>{{ $user->created_at->format('Y-m-d H:i:s') }}</strong>
                                                </div>
                                                <div class="col-md-6">
                                                    <small class="text-muted">最后更新：</small>
                                                    <strong>{{ $user->updated_at->format('Y-m-d H:i:s') }}</strong>
                                                </div>
                                                @if($user->email_verified_at)
                                                    <div class="col-md-6 mt-2">
                                                        <small class="text-muted">邮箱验证：</small>
                                                        <span class="badge bg-success">已验证</span>
                                                    </div>
                                                @endif
                                                @if($user->last_login_at)
                                                    <div class="col-md-6 mt-2">
                                                        <small class="text-muted">最后登录：</small>
                                                        <strong>{{ $user->last_login_at->format('Y-m-d H:i:s') }}</strong>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 角色权限说明 -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-shield-alt me-2"></i>角色权限说明
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li><strong>超级管理员</strong>：拥有所有权限，可以管理其他用户</li>
                                        <li><strong>管理员</strong>：可以管理内容，但不能管理用户</li>
                                        <li><strong>操作员</strong>：可以编辑内容，权限有限</li>
                                        <li><strong>普通用户</strong>：只能查看内容</li>
                                    </ul>
                                </div>

                                @if($user->id === Auth::guard('admin')->id())
                                    <div class="alert alert-warning">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-exclamation-triangle me-2"></i>注意
                                        </h6>
                                        <p class="mb-0 small">
                                            您正在编辑自己的账号信息。修改角色可能会影响您的系统权限。
                                        </p>
                                    </div>
                                @endif

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.admin-users.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>取消
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>保存修改
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 角色选择说明
    const roleSelect = document.getElementById('role');
    const currentUserId = {{ Auth::guard('admin')->id() }};
    const editingUserId = {{ $user->id }};
    
    roleSelect.addEventListener('change', function() {
        const role = this.value;
        const descriptions = {
            'super_admin': '拥有系统最高权限，可以管理所有用户和内容',
            'admin': '可以管理内容和基本设置，但不能管理用户',
            'operator': '可以编辑和发布内容，权限相对有限',
            'user': '只能查看内容，无编辑权限'
        };
        
        // 移除之前的说明
        const existingDesc = this.parentNode.querySelector('.role-description');
        if (existingDesc) {
            existingDesc.remove();
        }
        
        if (role && descriptions[role]) {
            const descDiv = document.createElement('div');
            descDiv.className = 'role-description small text-muted mt-1';
            descDiv.textContent = descriptions[role];
            this.parentNode.appendChild(descDiv);
        }
    });
    
    // 表单提交确认
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const isOwnAccount = currentUserId === editingUserId;
        const newRole = roleSelect.value;
        const originalRole = '{{ $user->role }}';
        
        if (isOwnAccount && newRole !== originalRole) {
            const confirmed = confirm('您正在修改自己的角色权限，这可能会影响您对系统的访问权限。\n\n确定要继续吗？');
            if (!confirmed) {
                e.preventDefault();
            }
        }
    });
});
</script>
@endpush
