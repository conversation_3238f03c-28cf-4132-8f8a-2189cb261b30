<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\TestRecord;
use App\Models\MaintenanceRecord;
use App\Models\EquipmentCategory;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * 显示仪表板
     */
    public function index()
    {
        // 统计数据
        $totalEquipment = Equipment::count();
        $availableEquipment = Equipment::where('status', 'available')->count();
        $inUseEquipment = Equipment::where('status', 'in_use')->count();
        $maintenanceEquipment = Equipment::where('status', 'maintenance')->count();

        $totalTests = TestRecord::count();
        $passedTests = TestRecord::where('result', 'pass')->count();
        $failedTests = TestRecord::where('result', 'fail')->count();

        $totalMaintenance = MaintenanceRecord::count();
        $completedMaintenance = MaintenanceRecord::where('status', 'completed')->count();

        // 最近的测试记录
        $recentTests = TestRecord::with(['equipment', 'user'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // 设备分类统计
        $categoryStats = EquipmentCategory::withCount('equipment')->get();

        return view('dashboard', compact(
            'totalEquipment',
            'availableEquipment',
            'inUseEquipment',
            'maintenanceEquipment',
            'totalTests',
            'passedTests',
            'failedTests',
            'totalMaintenance',
            'completedMaintenance',
            'recentTests',
            'categoryStats'
        ));
    }
}
