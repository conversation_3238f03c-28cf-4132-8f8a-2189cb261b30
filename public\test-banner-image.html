<!DOCTYPE html>
<html>
<head>
    <title>Banner图片测试</title>
</head>
<body>
    <h1>Banner图片测试</h1>
    
    <h2>测试不同的图片路径</h2>
    
    <p>1. about.png (banner目录):</p>
    <img src="/images/banner/about.png" style="max-width: 500px; border: 1px solid green;" alt="banner/about.png">
    
    <p>2. about.svg (banner目录):</p>
    <img src="/images/banner/about.svg" style="max-width: 500px; border: 1px solid blue;" alt="banner/about.svg">
    
    <p>3. about.png (about目录):</p>
    <img src="/images/about/about.png" style="max-width: 500px; border: 1px solid red;" alt="about/about.png">
    
    <h2>直接链接测试</h2>
    <p><a href="/images/banner/about.png" target="_blank">点击查看 banner/about.png</a></p>
    <p><a href="/images/banner/about.svg" target="_blank">点击查看 banner/about.svg</a></p>
    <p><a href="/images/about/about.png" target="_blank">点击查看 about/about.png</a></p>
    
    <h2>模拟关于我们页面的banner效果</h2>
    <div style="height: 500px; border-radius: 20px; overflow: hidden; border: 2px solid #ccc;">
        <img src="/images/banner/about.png" 
             alt="关于我们" 
             style="width: 100%; height: 100%; object-fit: cover;">
    </div>
    
    <script>
        // 检查图片加载状态
        document.querySelectorAll('img').forEach(function(img, index) {
            img.onload = function() {
                console.log('图片 ' + (index + 1) + ' 加载成功: ' + this.src);
                this.style.border = '2px solid green';
            };
            img.onerror = function() {
                console.log('图片 ' + (index + 1) + ' 加载失败: ' + this.src);
                this.style.border = '2px solid red';
                this.alt = '加载失败: ' + this.src;
            };
        });
    </script>
</body>
</html>
