<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaintenanceRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'equipment_id',
        'user_id',
        'type',
        'maintenance_date',
        'description',
        'parts_replaced',
        'cost',
        'status',
        'notes',
        'next_maintenance_date',
    ];

    protected $casts = [
        'maintenance_date' => 'datetime',
        'next_maintenance_date' => 'datetime',
        'cost' => 'decimal:2',
    ];

    /**
     * 获取维护设备
     */
    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    /**
     * 获取维护人员
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取已完成的维护记录
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * 获取预防性维护记录
     */
    public function scopePreventive($query)
    {
        return $query->where('type', 'preventive');
    }
}
