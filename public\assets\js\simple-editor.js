/**
 * 简化的富文本编辑器
 * 基于原生JavaScript实现，无需外部依赖
 */
class SimpleEditor {
    constructor(selector, options = {}) {
        this.element = typeof selector === 'string' ? document.querySelector(selector) : selector;
        this.options = {
            height: options.height || '300px',
            placeholder: options.placeholder || '请输入内容...',
            uploadUrl: options.uploadUrl || '/admin/upload/image',
            csrfToken: options.csrfToken || '',
            ...options
        };

        this.init();
    }

    init() {
        if (!this.element) return;

        // 创建编辑器容器
        this.createEditor();

        // 绑定事件
        this.bindEvents();

        // 设置初始内容
        if (this.element.value) {
            this.setContent(this.element.value);
        }
    }

    createEditor() {
        // 隐藏原始textarea
        this.element.style.display = 'none';

        // 创建编辑器容器
        this.container = document.createElement('div');
        this.container.className = 'simple-editor';
        this.container.style.border = '1px solid #ddd';
        this.container.style.borderRadius = '4px';
        this.container.style.overflow = 'hidden';

        // 创建工具栏
        this.toolbar = document.createElement('div');
        this.toolbar.className = 'simple-editor-toolbar';
        this.toolbar.style.cssText = `
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            padding: 8px;
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        `;

        // 创建编辑区域
        this.editor = document.createElement('div');
        this.editor.className = 'simple-editor-content';
        this.editor.contentEditable = true;
        this.editor.style.cssText = `
            min-height: ${this.options.height};
            padding: 12px;
            outline: none;
            line-height: 1.6;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        `;
        this.editor.setAttribute('data-placeholder', this.options.placeholder);

        // 添加工具栏按钮
        this.createToolbarButtons();

        // 组装编辑器
        this.container.appendChild(this.toolbar);
        this.container.appendChild(this.editor);

        // 插入到DOM
        this.element.parentNode.insertBefore(this.container, this.element.nextSibling);

        // 添加样式
        this.addStyles();
    }

    createToolbarButtons() {
        const buttons = [
            { command: 'bold', icon: 'B', title: '粗体' },
            { command: 'italic', icon: 'I', title: '斜体' },
            { command: 'underline', icon: 'U', title: '下划线' },
            { command: 'separator' },
            { command: 'formatBlock', value: 'h1', icon: 'H1', title: '标题1' },
            { command: 'formatBlock', value: 'h2', icon: 'H2', title: '标题2' },
            { command: 'formatBlock', value: 'p', icon: 'P', title: '段落' },
            { command: 'separator' },
            { command: 'insertUnorderedList', icon: '•', title: '无序列表' },
            { command: 'insertOrderedList', icon: '1.', title: '有序列表' },
            { command: 'separator' },
            { command: 'justifyLeft', icon: '←', title: '左对齐' },
            { command: 'justifyCenter', icon: '↔', title: '居中' },
            { command: 'justifyRight', icon: '→', title: '右对齐' },
            { command: 'separator' },
            { command: 'insertImage', icon: '📷', title: '插入图片' },
            { command: 'insertMultipleImages', icon: '🖼', title: '插入多张图片' },
            { command: 'insertPdf', icon: '📄', title: '插入PDF' },
            { command: 'createLink', icon: '🔗', title: '插入链接' },
            { command: 'separator' },
            { command: 'removeFormat', icon: '✗', title: '清除格式' }
        ];

        buttons.forEach(btn => {
            if (btn.command === 'separator') {
                const separator = document.createElement('div');
                separator.style.cssText = 'width: 1px; height: 20px; background: #ddd; margin: 0 4px;';
                this.toolbar.appendChild(separator);
            } else {
                const button = document.createElement('button');
                button.type = 'button';
                button.innerHTML = btn.icon;
                button.title = btn.title;
                button.style.cssText = `
                    border: none;
                    background: none;
                    padding: 4px 8px;
                    cursor: pointer;
                    border-radius: 3px;
                    font-weight: bold;
                    min-width: 28px;
                    height: 28px;
                `;

                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.execCommand(btn.command, btn.value);
                });

                button.addEventListener('mouseenter', () => {
                    button.style.background = '#e9ecef';
                });

                button.addEventListener('mouseleave', () => {
                    button.style.background = 'none';
                });

                this.toolbar.appendChild(button);
            }
        });
    }

    addStyles() {
        if (document.getElementById('simple-editor-styles')) return;

        const style = document.createElement('style');
        style.id = 'simple-editor-styles';
        style.textContent = `
            .simple-editor-content:empty:before {
                content: attr(data-placeholder);
                color: #999;
                pointer-events: none;
            }
            .simple-editor-content h1 { font-size: 2em; margin: 0.67em 0; }
            .simple-editor-content h2 { font-size: 1.5em; margin: 0.75em 0; }
            .simple-editor-content h3 { font-size: 1.17em; margin: 0.83em 0; }
            .simple-editor-content p { margin: 1em 0; }
            .simple-editor-content ul, .simple-editor-content ol { margin: 1em 0; padding-left: 2em; }
            .simple-editor-content img { max-width: 100%; height: auto; }
        `;
        document.head.appendChild(style);
    }

    bindEvents() {
        // 同步内容到原始textarea
        this.editor.addEventListener('input', () => {
            this.element.value = this.getContent();
        });

        // 处理粘贴
        this.editor.addEventListener('paste', (e) => {
            e.preventDefault();
            const text = e.clipboardData.getData('text/plain');
            document.execCommand('insertText', false, text);
        });
    }

    execCommand(command, value = null) {
        this.editor.focus();

        if (command === 'insertImage') {
            this.insertImage();
        } else if (command === 'insertMultipleImages') {
            this.insertMultipleImages();
        } else if (command === 'insertPdf') {
            this.insertPdf();
        } else if (command === 'createLink') {
            this.createLink();
        } else {
            document.execCommand(command, false, value);
        }

        // 同步内容
        this.element.value = this.getContent();
    }

    insertImage() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';

        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            // 显示图片大小选择对话框
            this.showImageSizeDialog(file);
        });

        input.click();
    }

    showImageSizeDialog(file) {
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const dialogContent = document.createElement('div');
        dialogContent.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        dialogContent.innerHTML = `
            <h4 style="margin: 0 0 15px 0; color: #333;">选择图片显示大小</h4>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 10px; font-weight: bold;">图片大小：</label>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <button type="button" class="size-btn" data-size="small" style="
                        padding: 10px;
                        border: 2px solid #ddd;
                        border-radius: 6px;
                        background: white;
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">小图</div>
                        <div style="font-size: 12px; color: #666;">最大宽度 200px</div>
                    </button>
                    <button type="button" class="size-btn" data-size="medium" style="
                        padding: 10px;
                        border: 2px solid #007bff;
                        border-radius: 6px;
                        background: #f8f9ff;
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">中图</div>
                        <div style="font-size: 12px; color: #666;">最大宽度 400px</div>
                    </button>
                    <button type="button" class="size-btn" data-size="large" style="
                        padding: 10px;
                        border: 2px solid #ddd;
                        border-radius: 6px;
                        background: white;
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">大图</div>
                        <div style="font-size: 12px; color: #666;">最大宽度 600px</div>
                    </button>
                    <button type="button" class="size-btn" data-size="full" style="
                        padding: 10px;
                        border: 2px solid #ddd;
                        border-radius: 6px;
                        background: white;
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">原图</div>
                        <div style="font-size: 12px; color: #666;">100% 宽度</div>
                    </button>
                </div>
            </div>
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" id="cancel-btn" style="
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background: white;
                    cursor: pointer;
                ">取消</button>
                <button type="button" id="upload-btn" style="
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #007bff;
                    color: white;
                    cursor: pointer;
                " disabled>上传图片</button>
            </div>
        `;

        dialog.appendChild(dialogContent);
        document.body.appendChild(dialog);

        let selectedSize = 'medium'; // 默认选择中图

        // 处理大小选择
        const sizeButtons = dialogContent.querySelectorAll('.size-btn');
        const uploadBtn = dialogContent.querySelector('#upload-btn');

        sizeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 重置所有按钮样式
                sizeButtons.forEach(b => {
                    b.style.border = '2px solid #ddd';
                    b.style.background = 'white';
                });

                // 设置选中样式
                btn.style.border = '2px solid #007bff';
                btn.style.background = '#f8f9ff';

                selectedSize = btn.dataset.size;
                uploadBtn.disabled = false;
                uploadBtn.style.background = '#007bff';
            });
        });

        // 取消按钮
        dialogContent.querySelector('#cancel-btn').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        // 上传按钮
        uploadBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            this.uploadImageWithSize(file, selectedSize);
        });

        // 点击背景关闭
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
            }
        });
    }

    uploadImageWithSize(file, size) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('_token', this.options.csrfToken);

        // 显示上传中状态
        const placeholder = document.createElement('span');
        placeholder.textContent = '图片上传中...';
        placeholder.style.color = '#999';
        placeholder.className = 'upload-placeholder';
        this.insertElement(placeholder);

        fetch(this.options.uploadUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            placeholder.remove();
            if (result.success) {
                const img = document.createElement('img');
                img.src = result.url;

                // 根据选择的大小设置样式
                const sizeStyles = this.getImageSizeStyles(size);
                img.style.cssText = sizeStyles;

                // 添加可调整大小的功能
                this.makeImageResizable(img);

                this.insertElement(img);
            } else {
                alert('图片上传失败: ' + (result.message || '未知错误'));
            }
        })
        .catch(error => {
            placeholder.remove();
            alert('图片上传失败: ' + error.message);
        });
    }

    getImageSizeStyles(size) {
        const baseStyles = `
            height: auto;
            border-radius: 4px;
            margin: 0.5em 0;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        `;

        switch (size) {
            case 'small':
                return baseStyles + 'max-width: 200px;';
            case 'medium':
                return baseStyles + 'max-width: 400px;';
            case 'large':
                return baseStyles + 'max-width: 600px;';
            case 'full':
                return baseStyles + 'max-width: 100%; width: 100%;';
            default:
                return baseStyles + 'max-width: 400px;';
        }
    }

    makeImageResizable(img) {
        img.addEventListener('click', () => {
            this.showImageResizeDialog(img);
        });

        img.addEventListener('mouseenter', () => {
            img.style.transform = 'scale(1.02)';
            img.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });

        img.addEventListener('mouseleave', () => {
            img.style.transform = 'scale(1)';
            img.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
    }

    showImageResizeDialog(img) {
        // 创建调整大小对话框
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const dialogContent = document.createElement('div');
        dialogContent.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        // 获取当前图片的大小类型
        const currentMaxWidth = img.style.maxWidth;
        let currentSize = 'medium';
        if (currentMaxWidth === '200px') currentSize = 'small';
        else if (currentMaxWidth === '400px') currentSize = 'medium';
        else if (currentMaxWidth === '600px') currentSize = 'large';
        else if (currentMaxWidth === '100%') currentSize = 'full';

        dialogContent.innerHTML = `
            <h4 style="margin: 0 0 15px 0; color: #333;">调整图片大小</h4>
            <div style="margin-bottom: 15px; text-align: center;">
                <img src="${img.src}" style="max-width: 200px; max-height: 150px; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" alt="预览">
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择新的大小：</label>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <button type="button" class="resize-btn" data-size="small" style="
                        padding: 10px;
                        border: 2px solid ${currentSize === 'small' ? '#007bff' : '#ddd'};
                        border-radius: 6px;
                        background: ${currentSize === 'small' ? '#f8f9ff' : 'white'};
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">小图</div>
                        <div style="font-size: 12px; color: #666;">最大宽度 200px</div>
                    </button>
                    <button type="button" class="resize-btn" data-size="medium" style="
                        padding: 10px;
                        border: 2px solid ${currentSize === 'medium' ? '#007bff' : '#ddd'};
                        border-radius: 6px;
                        background: ${currentSize === 'medium' ? '#f8f9ff' : 'white'};
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">中图</div>
                        <div style="font-size: 12px; color: #666;">最大宽度 400px</div>
                    </button>
                    <button type="button" class="resize-btn" data-size="large" style="
                        padding: 10px;
                        border: 2px solid ${currentSize === 'large' ? '#007bff' : '#ddd'};
                        border-radius: 6px;
                        background: ${currentSize === 'large' ? '#f8f9ff' : 'white'};
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">大图</div>
                        <div style="font-size: 12px; color: #666;">最大宽度 600px</div>
                    </button>
                    <button type="button" class="resize-btn" data-size="full" style="
                        padding: 10px;
                        border: 2px solid ${currentSize === 'full' ? '#007bff' : '#ddd'};
                        border-radius: 6px;
                        background: ${currentSize === 'full' ? '#f8f9ff' : 'white'};
                        cursor: pointer;
                        transition: all 0.2s;
                    ">
                        <div style="font-weight: bold;">原图</div>
                        <div style="font-size: 12px; color: #666;">100% 宽度</div>
                    </button>
                </div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">对齐方式：</label>
                <div style="display: flex; gap: 10px;">
                    <button type="button" class="align-btn" data-align="left" style="
                        padding: 8px 12px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: white;
                        cursor: pointer;
                    ">左对齐</button>
                    <button type="button" class="align-btn" data-align="center" style="
                        padding: 8px 12px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: white;
                        cursor: pointer;
                    ">居中</button>
                    <button type="button" class="align-btn" data-align="right" style="
                        padding: 8px 12px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: white;
                        cursor: pointer;
                    ">右对齐</button>
                </div>
            </div>
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" id="delete-img-btn" style="
                    padding: 8px 16px;
                    border: 1px solid #dc3545;
                    border-radius: 4px;
                    background: white;
                    color: #dc3545;
                    cursor: pointer;
                ">删除图片</button>
                <button type="button" id="cancel-resize-btn" style="
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background: white;
                    cursor: pointer;
                ">取消</button>
                <button type="button" id="apply-resize-btn" style="
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #007bff;
                    color: white;
                    cursor: pointer;
                ">应用</button>
            </div>
        `;

        dialog.appendChild(dialogContent);
        document.body.appendChild(dialog);

        let selectedSize = currentSize;
        let selectedAlign = 'left';

        // 处理大小选择
        const resizeButtons = dialogContent.querySelectorAll('.resize-btn');
        resizeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                resizeButtons.forEach(b => {
                    b.style.border = '2px solid #ddd';
                    b.style.background = 'white';
                });
                btn.style.border = '2px solid #007bff';
                btn.style.background = '#f8f9ff';
                selectedSize = btn.dataset.size;
            });
        });

        // 处理对齐选择
        const alignButtons = dialogContent.querySelectorAll('.align-btn');
        alignButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                alignButtons.forEach(b => {
                    b.style.background = 'white';
                    b.style.color = 'black';
                });
                btn.style.background = '#007bff';
                btn.style.color = 'white';
                selectedAlign = btn.dataset.align;
            });
        });

        // 删除图片
        dialogContent.querySelector('#delete-img-btn').addEventListener('click', () => {
            if (confirm('确定要删除这张图片吗？')) {
                img.remove();
                document.body.removeChild(dialog);
                // 同步内容
                this.element.value = this.getContent();
            }
        });

        // 取消按钮
        dialogContent.querySelector('#cancel-resize-btn').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        // 应用按钮
        dialogContent.querySelector('#apply-resize-btn').addEventListener('click', () => {
            // 应用新的大小样式
            const newStyles = this.getImageSizeStyles(selectedSize);
            img.style.cssText = newStyles;

            // 应用对齐
            if (selectedAlign === 'center') {
                img.style.display = 'block';
                img.style.marginLeft = 'auto';
                img.style.marginRight = 'auto';
            } else if (selectedAlign === 'right') {
                img.style.float = 'right';
                img.style.marginLeft = '10px';
            } else {
                img.style.float = 'left';
                img.style.marginRight = '10px';
            }

            // 重新添加交互功能
            this.makeImageResizable(img);

            document.body.removeChild(dialog);

            // 同步内容
            this.element.value = this.getContent();
        });

        // 点击背景关闭
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
            }
        });
    }

    createLink() {
        const url = prompt('请输入链接地址:');
        if (url) {
            document.execCommand('createLink', false, url);
        }
    }

    insertMultipleImages() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.multiple = true;

        input.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            if (files.length === 0) return;

            // 创建容器显示上传进度
            const progressContainer = document.createElement('div');
            progressContainer.style.cssText = `
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
                background: #f8f9fa;
            `;
            progressContainer.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">正在上传 ${files.length} 张图片...</div>
                <div class="progress-bar" style="width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden;">
                    <div class="progress-fill" style="height: 100%; background: #007bff; width: 0%; transition: width 0.3s;"></div>
                </div>
                <div class="progress-text" style="font-size: 12px; color: #666; margin-top: 5px;">0 / ${files.length}</div>
            `;
            this.insertElement(progressContainer);

            const progressFill = progressContainer.querySelector('.progress-fill');
            const progressText = progressContainer.querySelector('.progress-text');
            let completedCount = 0;
            const imageContainer = document.createElement('div');
            imageContainer.style.cssText = 'display: flex; flex-wrap: wrap; gap: 10px; margin: 10px 0;';

            files.forEach((file, index) => {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('_token', this.options.csrfToken);

                fetch(this.options.uploadUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(result => {
                    completedCount++;
                    const progress = (completedCount / files.length) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `${completedCount} / ${files.length}`;

                    if (result.success) {
                        const img = document.createElement('img');
                        img.src = result.url;

                        // 使用小图作为多图的默认大小
                        const styles = this.getImageSizeStyles('small');
                        img.style.cssText = styles;

                        // 添加可调整大小的功能
                        this.makeImageResizable(img);

                        imageContainer.appendChild(img);
                    }

                    // 所有图片上传完成
                    if (completedCount === files.length) {
                        progressContainer.remove();
                        if (imageContainer.children.length > 0) {
                            this.insertElement(imageContainer);
                        }
                    }
                })
                .catch(error => {
                    completedCount++;
                    const progress = (completedCount / files.length) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `${completedCount} / ${files.length} (部分失败)`;

                    if (completedCount === files.length) {
                        setTimeout(() => {
                            progressContainer.remove();
                            if (imageContainer.children.length > 0) {
                                this.insertElement(imageContainer);
                            }
                        }, 1000);
                    }
                });
            });
        });

        input.click();
    }

    insertPdf() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.pdf,application/pdf';

        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            // 验证文件类型
            if (file.type !== 'application/pdf') {
                alert('请选择PDF文件');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('PDF文件大小不能超过10MB');
                return;
            }

            const formData = new FormData();
            formData.append('pdf', file);
            formData.append('_token', this.options.csrfToken);

            // 显示上传中状态
            const placeholder = document.createElement('div');
            placeholder.style.cssText = `
                border: 1px dashed #007bff;
                border-radius: 4px;
                padding: 20px;
                text-align: center;
                margin: 10px 0;
                background: #f8f9ff;
            `;
            placeholder.innerHTML = `
                <div style="color: #007bff; font-size: 24px; margin-bottom: 10px;">📄</div>
                <div style="font-weight: bold;">正在上传PDF文件...</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">${file.name}</div>
            `;
            this.insertElement(placeholder);

            // 上传到PDF专用接口
            const pdfUploadUrl = this.options.uploadUrl.replace('/image', '/pdf');

            fetch(pdfUploadUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                placeholder.remove();
                if (result.success) {
                    const pdfLink = document.createElement('div');
                    pdfLink.style.cssText = `
                        border: 1px solid #28a745;
                        border-radius: 4px;
                        padding: 15px;
                        margin: 10px 0;
                        background: #f8fff9;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    `;
                    pdfLink.innerHTML = `
                        <div style="color: #28a745; font-size: 24px;">📄</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold; color: #28a745;">
                                <a href="${result.url}" target="_blank" style="color: #28a745; text-decoration: none;">
                                    ${result.original_name || file.name}
                                </a>
                            </div>
                            <div style="font-size: 12px; color: #666;">
                                PDF文件 • ${(result.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                        </div>
                        <div>
                            <a href="${result.url}" target="_blank" style="
                                background: #28a745;
                                color: white;
                                padding: 5px 10px;
                                border-radius: 3px;
                                text-decoration: none;
                                font-size: 12px;
                            ">查看</a>
                        </div>
                    `;
                    this.insertElement(pdfLink);
                } else {
                    alert('PDF上传失败: ' + (result.message || '未知错误'));
                }
            })
            .catch(error => {
                placeholder.remove();
                alert('PDF上传失败: ' + error.message);
            });
        });

        input.click();
    }

    insertElement(element) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            range.deleteContents();
            range.insertNode(element);
            range.setStartAfter(element);
            range.setEndAfter(element);
            selection.removeAllRanges();
            selection.addRange(range);
        } else {
            this.editor.appendChild(element);
        }

        // 同步内容
        this.element.value = this.getContent();
    }

    getContent() {
        return this.editor.innerHTML;
    }

    setContent(content) {
        this.editor.innerHTML = content;
        this.element.value = content;
    }

    destroy() {
        if (this.container) {
            this.container.remove();
        }
        this.element.style.display = '';
    }
}

// 全局初始化函数
window.initSimpleEditor = function(selector, options = {}) {
    return new SimpleEditor(selector, options);
};
