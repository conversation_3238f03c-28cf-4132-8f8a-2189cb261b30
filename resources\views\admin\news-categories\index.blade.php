@extends('admin.layouts.app')

@section('title', '新闻分类管理')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item active">新闻分类</li>
@endsection

@section('content')
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-folder me-2"></i>新闻分类管理
        </h1>
        <p class="text-muted">管理新闻分类信息</p>
    </div>
    <a href="{{ route('admin.news-categories.create') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>添加分类
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>分类列表
        </h5>
    </div>
    <div class="card-body">
        @if($categories->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>分类名称</th>
                            <th>别名</th>
                            <th>颜色</th>
                            <th>新闻数量</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($categories as $category)
                            <tr>
                                <td>{{ $category->id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: {{ $category->color }};"></div>
                                        <div>
                                            <strong>{{ $category->name }}</strong>
                                            @if($category->description)
                                                <br><small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td><code>{{ $category->slug }}</code></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 30px; height: 20px; background-color: {{ $category->color }};"></div>
                                        <code>{{ $category->color }}</code>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $category->news_count }}</span>
                                </td>
                                <td>{{ $category->sort_order }}</td>
                                <td>
                                    @if($category->is_active)
                                        <span class="badge bg-success">启用</span>
                                    @else
                                        <span class="badge bg-secondary">禁用</span>
                                    @endif
                                </td>
                                <td>{{ $category->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.news-categories.show', $category) }}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.news-categories.edit', $category) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.news-categories.destroy', $category) }}" 
                                              method="POST" 
                                              class="d-inline"
                                              onsubmit="return confirm('确定要删除这个分类吗？')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="d-flex justify-content-center">
                {{ $categories->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-folder fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无分类</h4>
                <p class="text-muted">点击上方按钮添加第一个新闻分类</p>
                <a href="{{ route('admin.news-categories.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加分类
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
