<?php
// 修复数据库中的图片URL
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>修复图片URL问题</h1>";

try {
    echo "<h2>当前配置检查</h2>";
    echo "<p>APP_URL: " . config('app.url') . "</p>";
    echo "<p>Storage URL: " . \Storage::url('test.jpg') . "</p>";
    
    echo "<h2>产品图片数据检查</h2>";
    $products = \App\Models\Product::whereNotNull('image')->get();
    
    echo "<table border='1' cellpadding='10' cellspacing='0'>";
    echo "<tr><th>ID</th><th>名称</th><th>原始图片路径</th><th>是否包含URL</th><th>修复后路径</th><th>操作</th></tr>";
    
    $needsFix = false;
    foreach ($products as $product) {
        $originalImage = $product->image;
        $containsUrl = (strpos($originalImage, 'http://') === 0 || strpos($originalImage, 'https://') === 0);
        
        // 如果包含完整URL，提取相对路径
        $fixedImage = $originalImage;
        if ($containsUrl) {
            $needsFix = true;
            // 提取storage/之后的部分
            if (preg_match('/\/storage\/(.+)$/', $originalImage, $matches)) {
                $fixedImage = $matches[1];
            }
        }
        
        $containsUrlText = $containsUrl ? '是' : '否';
        $actionText = $containsUrl ? '需要修复' : '正常';
        
        echo "<tr>";
        echo "<td>{$product->id}</td>";
        echo "<td>{$product->name}</td>";
        echo "<td>" . htmlspecialchars($originalImage) . "</td>";
        echo "<td>{$containsUrlText}</td>";
        echo "<td>" . htmlspecialchars($fixedImage) . "</td>";
        echo "<td>{$actionText}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($needsFix) {
        echo "<h2>执行修复</h2>";
        echo "<form method='post'>";
        echo "<p>发现有产品的图片路径包含完整URL，这会导致端口问题。</p>";
        echo "<button type='submit' name='fix' value='1' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>立即修复</button>";
        echo "</form>";
        
        // 处理修复请求
        if (isset($_POST['fix'])) {
            echo "<h3>修复进度</h3>";
            foreach ($products as $product) {
                $originalImage = $product->image;
                $containsUrl = (strpos($originalImage, 'http://') === 0 || strpos($originalImage, 'https://') === 0);
                
                if ($containsUrl) {
                    // 提取相对路径
                    if (preg_match('/\/storage\/(.+)$/', $originalImage, $matches)) {
                        $newImagePath = $matches[1];
                        $product->update(['image' => $newImagePath]);
                        echo "<p>✅ 修复产品: {$product->name}</p>";
                        echo "<p>   原路径: " . htmlspecialchars($originalImage) . "</p>";
                        echo "<p>   新路径: " . htmlspecialchars($newImagePath) . "</p>";
                    }
                }
            }
            echo "<p><strong>修复完成！请刷新产品页面查看效果。</strong></p>";
            echo "<p><a href='http://localhost/products' target='_blank'>点击查看产品页面</a></p>";
        }
    } else {
        echo "<p style='color: green;'>✅ 所有产品的图片路径都是正确的相对路径。</p>";
        
        echo "<h2>测试图片URL生成</h2>";
        $testProduct = $products->first();
        if ($testProduct) {
            echo "<p>测试产品: {$testProduct->name}</p>";
            echo "<p>图片路径: {$testProduct->image}</p>";
            echo "<p>生成的URL: {$testProduct->image_url}</p>";
            echo "<p>预期URL: " . config('app.url') . "/storage/{$testProduct->image}</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
