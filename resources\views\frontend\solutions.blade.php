@extends('frontend.layouts.app')

@section('title', '解决方案 - 上海睿测微智能')

@section('content')
<style>
/* 解决方案Banner样式 - 左右分栏布局 */
.solutions-hero-banner {
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
}

.solutions-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 10;
}

.solutions-text-section {
    flex: 1;
    padding: 3rem;
    color: #1e293b;
    position: relative;
    z-index: 3;
}

.solutions-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    background: url('{{ asset('images/banner/jjfa.png') }}') right center/contain no-repeat;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

/* 图片区域的装饰效果已移至::after伪元素中 */

.company-name {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e40af;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.company-tagline {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0369a1;
    margin-bottom: 1.5rem;
    border-left: 4px solid #0369a1;
    padding-left: 1rem;
}

.company-description {
    font-size: 1.1rem;
    color: #475569;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #0369a1, #1e40af);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(3, 105, 161, 0.3);
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.4);
    color: white;
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid #0369a1;
    color: #0369a1;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: #0369a1;
    color: white;
    transform: translateY(-2px);
}

/* 装饰元素 */
.solutions-hero-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .solutions-hero-banner {
        height: auto;
        min-height: 600px;
    }

    .solutions-banner-content {
        flex-direction: column;
    }

    .solutions-text-section {
        flex: none;
        padding: 2rem 1.5rem;
        text-align: center;
        order: 2;
    }

    .solutions-image-section {
        flex: none;
        height: 250px;
        order: 1;
        background-size: contain;
    }

    .company-name {
        font-size: 2rem;
    }

    .company-tagline {
        font-size: 1.2rem;
    }

    .company-description {
        font-size: 1rem;
    }

    .action-buttons {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .company-name {
        font-size: 1.8rem;
    }

    .company-tagline {
        font-size: 1.1rem;
    }

    .btn-primary-custom,
    .btn-outline-custom {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>

<!-- 解决方案Banner -->
<section class="py-4">
    <div class="container">
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="solutions-hero-banner">
                        <div class="solutions-banner-content">
                            <!-- 文字内容区域 -->
                            <div class="solutions-text-section">
                                <h1 class="company-name">
                                    上海睿测微智能
                                </h1>
                                <h2 class="company-tagline">
                                    专业芯片测试解决方案提供商
                                </h2>
                                <p class="company-description">
                                    为尖端技术行业提供高精度、高可靠性的专业测试系统，助力工程师攻克极限环境下的芯片性能挑战
                                </p>
                                <div class="action-buttons">
                                    <a href="{{ route('products.index') }}" class="btn-primary-custom">
                                        <i class="fas fa-eye"></i>
                                        查看产品演示
                                    </a>
                                    <a href="#" class="btn-outline-custom">
                                        <i class="fas fa-download"></i>
                                        下载技术白皮书
                                    </a>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="solutions-image-section">
                                <!-- jjfa.png 图片通过CSS背景显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 工业级高低温测试方案 -->
<section class="py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="pe-lg-5">
                    <h2 class="display-6 fw-bold text-dark mb-4">工业级高低温测试方案</h2>

                    <h3 class="h4 fw-semibold text-dark mb-4">突破极限温度范围的芯片测试系统</h3>

                    <p class="text-dark mb-4 fs-5 lh-lg">
                        我们的工业级测试系统专为苛刻环境设计，支持<strong>-80°C至600°C</strong>的全温度范围循环测试，远超行业标准要求。该系统采用双重闭环控制技术，温度波动控制在<strong>±0.1°C</strong>以内，确保测试数据的高精度和可重复性。
                    </p>

                    <p class="text-dark mb-5 fs-5 lh-lg">
                        系统配备先进的实时监控平台，可记录温度变化曲线与芯片性能参数的关联性，生成详细测试报告，帮助工程师精确定位潜在的温度敏感问题，提升产品在极端工业环境下的可靠性。
                    </p>


                </div>
            </div>

            <div class="col-lg-6">
                <div class="text-center">
                    <div class="position-relative d-inline-block">
                        <img src="{{ asset('images/jjfa/2_1.png') }}"
                             alt="睿测微工业级高低温测试系统"
                             class="img-fluid rounded-4 shadow-lg"
                             style="max-height: 550px; object-fit: contain; transform: scale(1.1);">

                        <!-- 装饰元素 -->
                        <div class="position-absolute top-0 start-0 w-100 h-100 rounded-4"
                             style="background: linear-gradient(45deg, rgba(59, 130, 246, 0.1) 0%, transparent 50%); pointer-events: none;"></div>
                    </div>

                    <p class="text-dark mt-4 fs-6">
                        <i class="fas fa-cog text-primary me-2"></i>
                        睿测微工业级高低温测试系统配备精密温控单元和数据采集系统
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 航天级严苛环境测试系统 -->
<section class="py-4">
    <div class="container">
        <h2 class="h3 fw-bold text-dark mb-5">航天级严苛环境测试系统</h2>

        <div class="row g-4">
            <!-- 大空环境模拟 -->
            <div class="col-lg-4">
                <div class="position-relative">
                    <!-- 连接线 -->
                    <div class="position-absolute top-0 start-0 w-100" style="height: 4px; background: #3b82f6; z-index: 1;"></div>

                    <!-- 圆形数字 -->
                    <div class="position-absolute top-0 start-50 translate-middle bg-primary rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 40px; height: 40px; z-index: 2;">
                        <span class="text-white fw-bold">1</span>
                    </div>

                    <!-- 内容卡片 -->
                    <div class="bg-white border rounded-3 p-4 mt-3">
                        <h5 class="fw-bold text-dark mb-3">大空环境模拟</h5>
                        <p class="text-muted mb-0 small lh-lg">
                            采用多层隔离舱设计，可同时模拟-196°C至+350°C的极端温度变化、真空环境（10⁻⁶Pa）和辐射条件，为航天级芯片提供全方位的环境测试平台。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 军工级精度保证 -->
            <div class="col-lg-4">
                <div class="position-relative">
                    <!-- 连接线 -->
                    <div class="position-absolute top-0 start-0 w-100" style="height: 4px; background: #3b82f6; z-index: 1;"></div>

                    <!-- 圆形数字 -->
                    <div class="position-absolute top-0 start-50 translate-middle bg-primary rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 40px; height: 40px; z-index: 2;">
                        <span class="text-white fw-bold">2</span>
                    </div>

                    <!-- 内容卡片 -->
                    <div class="bg-white border rounded-3 p-4 mt-3">
                        <h5 class="fw-bold text-dark mb-3">军工级精度保证</h5>
                        <p class="text-muted mb-0 small lh-lg">
                            系统核心部件采用航空级材料制造，测量精度达到±0.05°C，并于抗振功率，确保在极端条件下数据采集的稳定性和可靠性，满足航天任务于元器件的严苛质量标准。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 全参数实时监测 -->
            <div class="col-lg-4">
                <div class="position-relative">
                    <!-- 连接线 -->
                    <div class="position-absolute top-0 start-0 w-100" style="height: 4px; background: #3b82f6; z-index: 1;"></div>

                    <!-- 圆形数字 -->
                    <div class="position-absolute top-0 start-50 translate-middle bg-primary rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 40px; height: 40px; z-index: 2;">
                        <span class="text-white fw-bold">3</span>
                    </div>

                    <!-- 内容卡片 -->
                    <div class="bg-white border rounded-3 p-4 mt-3">
                        <h5 class="fw-bold text-dark mb-3">全参数实时监测</h5>
                        <p class="text-muted mb-0 small lh-lg">
                            配备16通道高精度数据采集系统，可同时监测芯片温度、电压、电流、功耗等多项参数，实时记录芯片在模拟大空环境中的性能表现和潜在异常。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 汽车电子芯片温度应力评估 -->
<section class="py-4">
    <div class="container">
        <h2 class="h3 fw-bold text-dark mb-5">汽车电子芯片温度应力评估</h2>

        <div class="row g-4">
            <!-- 左侧图片 -->
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="{{ asset('images/jjfa/4_1.png') }}?v={{ time() }}"
                         alt="汽车电子芯片温度应力评估系统"
                         class="img-fluid rounded-3"
                         style="max-width: 100%; height: auto;">
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="col-lg-6">
                <div class="ps-lg-4">
                    <h4 class="fw-bold text-dark mb-4">符合AEC-Q100标准的专业测试方案</h4>

                    <p class="text-muted mb-3 lh-lg">
                        睿测微汽车电子芯片测试系统严格遵循AEC-Q100标准设计，支持Grade 0（-40°C至+170°C）至Grade 4（0°C至+70°C）全等级测试要求，覆盖温度循环、温度冲击、湿热测试等多种测试类型。
                    </p>

                    <p class="text-muted mb-0 lh-lg">
                        系统采用创新的热管理技术，温度转换速率可达60°C/分钟，大幅缩短测试周期。内置多重安全保护机制，包括过热断电、异常报警和紧急停机功能，确保测试过程安全可控，满足汽车电子日益严格的可靠性和安全性要求。
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- AI芯片测试全流程方案 -->
<section class="py-4">
    <div class="container">
        <h2 class="h3 fw-bold text-dark mb-5">AI芯片测试全流程方案</h2>

        <!-- AI芯片测试流程图 -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-10">
                <div class="text-center">
                    <img src="{{ asset('images/jjfa/5_.png') }}?v={{ time() }}"
                         alt="AI芯片测试全流程方案"
                         class="img-fluid"
                         style="max-width: 100%; height: auto;">
                </div>
            </div>
        </div>

        <!-- 描述文字 -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <p class="text-muted mb-3 lh-lg">
                    面对AI芯片高功耗、高集成度的特点，我们开发了专门的全流程测试解决方案。系统支持300W以上高功耗条件下的精准温控，温度均匀性控制在±0.05°C以内，确保大型AI芯片各功能单元的测试一致性。
                </p>

                <p class="text-muted mb-0 lh-lg">
                    配备的智能算法可自动识别性能瓶颈，优化测试流程，测试效率提升达40%以上。同时，系统支持海量测试数据的实时分析，帮助工程师快速定位问题，为AI芯片的量产提供坚实保障。
                </p>
            </div>
        </div>
    </div>
</section>

<!-- 定制化测试解决方案 -->
<section class="py-4">
    <div class="container">
        <!-- 标题 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="h3 fw-bold text-dark mb-0">定制化测试解决方案</h2>
            </div>
        </div>

        <!-- 三个服务卡片 -->
        <div class="row g-4 mb-5">
            <!-- 方案定制 -->
            <div class="col-lg-4 col-md-6">
                <div class="h-100">
                    <div class="d-flex align-items-start mb-3">
                        <div class="me-3">
                            <i class="fas fa-edit text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <div>
                            <h4 class="fw-bold text-dark mb-3">方案定制</h4>
                            <p class="text-muted lh-lg mb-0">
                                基于十年芯片测试经验，我们的工程师团队可根据客户特定需求，定制开发专属测试方案，解决标准设备无法满足的特殊测试挑战。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备维护 -->
            <div class="col-lg-4 col-md-6">
                <div class="h-100">
                    <div class="d-flex align-items-start mb-3">
                        <div class="me-3">
                            <i class="fas fa-cog text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <div>
                            <h4 class="fw-bold text-dark mb-3">设备维护</h4>
                            <p class="text-muted lh-lg mb-0">
                                提供全天候技术支持和定期维护服务，确保测试设备长期稳定运行。关键部件采用模块化设计，维护简便，最大限度减少停机时间。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="col-lg-4 col-md-6">
                <div class="h-100">
                    <div class="d-flex align-items-start mb-3">
                        <div class="me-3">
                            <i class="fas fa-chart-bar text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <div>
                            <h4 class="fw-bold text-dark mb-3">数据分析</h4>
                            <p class="text-muted lh-lg mb-0">
                                配套专业数据分析软件，支持海量测试数据的深度挖掘和可视化展示，帮助工程师发现隐藏的性能问题和优化方向。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部描述文字 -->
        <div class="row mb-4">
            <div class="col-12">
                <p class="text-muted lh-lg mb-0">
                    睿测微智能秉承"精准测试，智造未来"的理念，致力于为客户提供最专业、最可靠的芯片测试解决方案。无论您面临何种测试挑战，我们都能为您量身定制最适合的测试方案，助力您的产品在极端环境中保持卓越性能。
                </p>
            </div>
        </div>

        <!-- 联系按钮 -->
        <div class="row">
            <div class="col-12">
                <a href="{{ route('contact') }}" class="btn btn-primary px-4 py-2 fw-bold">
                    联系我们获取定制方案
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
