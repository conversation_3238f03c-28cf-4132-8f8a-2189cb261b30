<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('页面标题');
            $table->string('slug')->unique()->comment('页面别名');
            $table->text('content')->nullable()->comment('页面内容');
            $table->string('meta_title')->nullable()->comment('SEO标题');
            $table->text('meta_description')->nullable()->comment('SEO描述');
            $table->string('meta_keywords')->nullable()->comment('SEO关键词');
            $table->boolean('is_published')->default(true)->comment('是否发布');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
