<?php
echo "<h1>路由测试页面</h1>";
echo "<p>当前URL: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>脚本名称: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>查询字符串: " . ($_SERVER['QUERY_STRING'] ?? '无') . "</p>";

echo "<h2>测试链接</h2>";
echo "<ul>";
echo "<li><a href='/'>首页</a></li>";
echo "<li><a href='/products'>产品页面</a></li>";
echo "<li><a href='/about'>关于我们</a></li>";
echo "<li><a href='/solutions'>解决方案</a></li>";
echo "</ul>";

echo "<h2>Laravel路由测试</h2>";
echo "<p>如果上面的链接都能正常访问，说明URL重写配置正确</p>";
?>
