<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * 显示登录页面
     */
    public function showLogin()
    {
        // 如果已经登录，重定向到后台首页
        if (Auth::guard('admin')->check()) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.login');
    }

    /**
     * 处理登录请求
     */
    public function login(Request $request)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ], [
            'email.required' => '邮箱地址不能为空',
            'email.email' => '请输入有效的邮箱地址',
            'password.required' => '密码不能为空',
            'password.min' => '密码至少需要6位',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // 尝试登录
        $credentials = $request->only('email', 'password');
        $remember = $request->has('remember');

        if (Auth::guard('admin')->attempt($credentials, $remember)) {
            $admin = Auth::guard('admin')->user();

            // 检查账户状态
            if (!$admin->isActive()) {
                Auth::guard('admin')->logout();
                return back()->with('error', '账户已被禁用，请联系管理员');
            }

            // 登录成功，重定向到后台首页
            return redirect()->intended(route('admin.dashboard'))
                           ->with('success', '登录成功，欢迎回来！');
        }

        // 登录失败
        return back()->with('error', '邮箱或密码错误')->withInput();
    }

    /**
     * 退出登录
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login')->with('success', '已安全退出');
    }
}
