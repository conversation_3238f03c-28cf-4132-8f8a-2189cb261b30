@extends('admin.layouts.app')

@section('title', '添加新闻分类')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.news-categories.index') }}">新闻分类</a></li>
    <li class="breadcrumb-item active">添加分类</li>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-plus me-2"></i>添加新闻分类
    </h1>
    <p class="text-muted">创建新的新闻分类</p>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.news-categories.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">别名</label>
                                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                       id="slug" name="slug" value="{{ old('slug') }}" 
                                       placeholder="留空自动生成">
                                @error('slug')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">用于URL，只能包含字母、数字和连字符</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">分类描述</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">分类颜色</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', '#1e4a8c') }}">
                                    <input type="text" class="form-control @error('color') is-invalid @enderror" 
                                           id="color-text" value="{{ old('color', '#1e4a8c') }}" readonly>
                                </div>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">用于前台显示时的标识颜色</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">排序</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">数字越小排序越靠前</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                启用分类
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存分类
                        </button>
                        <a href="{{ route('admin.news-categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>提示
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>分类说明</h6>
                    <ul class="mb-0">
                        <li>分类名称是必填项</li>
                        <li>别名用于生成友好的URL</li>
                        <li>颜色用于前台显示时的标识</li>
                        <li>排序数字越小越靠前显示</li>
                        <li>禁用的分类不会在前台显示</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-palette me-2"></i>推荐颜色</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-sm border color-preset" 
                                style="background-color: #1e4a8c; width: 40px; height: 30px;" 
                                data-color="#1e4a8c" title="睿测微蓝"></button>
                        <button type="button" class="btn btn-sm border color-preset" 
                                style="background-color: #28a745; width: 40px; height: 30px;" 
                                data-color="#28a745" title="成功绿"></button>
                        <button type="button" class="btn btn-sm border color-preset" 
                                style="background-color: #dc3545; width: 40px; height: 30px;" 
                                data-color="#dc3545" title="警告红"></button>
                        <button type="button" class="btn btn-sm border color-preset" 
                                style="background-color: #ffc107; width: 40px; height: 30px;" 
                                data-color="#ffc107" title="警告黄"></button>
                        <button type="button" class="btn btn-sm border color-preset" 
                                style="background-color: #17a2b8; width: 40px; height: 30px;" 
                                data-color="#17a2b8" title="信息蓝"></button>
                        <button type="button" class="btn btn-sm border color-preset" 
                                style="background-color: #6f42c1; width: 40px; height: 30px;" 
                                data-color="#6f42c1" title="紫色"></button>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>注意事项</h6>
                    <ul class="mb-0">
                        <li>删除分类前请先移除其下的新闻</li>
                        <li>建议使用有意义的分类名称</li>
                        <li>颜色选择要考虑视觉效果</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // 自动生成别名
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '') // 移除特殊字符
            .replace(/\s+/g, '-') // 空格替换为连字符
            .replace(/-+/g, '-') // 多个连字符替换为单个
            .trim('-'); // 移除首尾连字符
        
        document.getElementById('slug').value = slug;
    });
    
    // 颜色选择器同步
    document.getElementById('color').addEventListener('input', function() {
        document.getElementById('color-text').value = this.value;
    });
    
    // 预设颜色选择
    document.querySelectorAll('.color-preset').forEach(function(button) {
        button.addEventListener('click', function() {
            const color = this.getAttribute('data-color');
            document.getElementById('color').value = color;
            document.getElementById('color-text').value = color;
        });
    });
</script>
@endpush
