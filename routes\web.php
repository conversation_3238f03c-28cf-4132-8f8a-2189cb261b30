<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// 前台路由
Route::get('/', [App\Http\Controllers\Frontend\HomeController::class, 'index'])->name('home');
Route::get('/products', [App\Http\Controllers\Frontend\ProductController::class, 'index'])->name('products.index');
Route::get('/products/{slug}', [App\Http\Controllers\Frontend\ProductController::class, 'show'])->name('products.show');
Route::get('/news', [App\Http\Controllers\Frontend\NewsController::class, 'index'])->name('news.index');
Route::get('/news/{slug}', [App\Http\Controllers\Frontend\NewsController::class, 'show'])->name('news.show');
Route::get('/about', [App\Http\Controllers\Frontend\PageController::class, 'about'])->name('about');
Route::get('/applications', [App\Http\Controllers\Frontend\PageController::class, 'applications'])->name('applications');
Route::get('/solutions', [App\Http\Controllers\Frontend\PageController::class, 'solutions'])->name('solutions');
Route::get('/services', [App\Http\Controllers\Frontend\PageController::class, 'services'])->name('services');
Route::get('/contact', [App\Http\Controllers\Frontend\PageController::class, 'contact'])->name('contact');

// 测试路由
Route::get('/test-assets', function () {
    return view('test-assets');
})->name('test.assets');

Auth::routes();

// 后台管理员认证路由
Route::prefix('admin')->name('admin.')->group(function () {
    // 登录相关路由（不需要认证）
    Route::get('login', [App\Http\Controllers\Admin\AuthController::class, 'showLogin'])->name('login');
    Route::post('login', [App\Http\Controllers\Admin\AuthController::class, 'login'])->name('login.submit');
    Route::post('logout', [App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('logout');

    // 需要管理员认证的路由
    Route::middleware(['admin.auth'])->group(function () {
        // 后台首页
        Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
        Route::get('dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard.index');

        // 分类管理
        Route::resource('categories', App\Http\Controllers\Admin\CategoryController::class);

        // 产品管理
        Route::resource('products', App\Http\Controllers\Admin\ProductController::class);

        // 新闻分类管理
        Route::resource('news-categories', App\Http\Controllers\Admin\NewsCategoryController::class);

        // 新闻管理
        Route::resource('news', App\Http\Controllers\Admin\NewsController::class);

        // 文件上传
        Route::post('upload/image', [App\Http\Controllers\Admin\UploadController::class, 'uploadImage'])->name('upload.image');
        Route::post('upload/pdf', [App\Http\Controllers\Admin\UploadController::class, 'uploadPdf'])->name('upload.pdf');

        // 管理员用户管理（仅超级管理员）
        Route::resource('admin-users', App\Http\Controllers\Admin\AdminUserController::class);
        Route::get('admin-users/{admin_user}/change-password', [App\Http\Controllers\Admin\AdminUserController::class, 'showChangePasswordForm'])->name('admin-users.change-password');
        Route::put('admin-users/{admin_user}/change-password', [App\Http\Controllers\Admin\AdminUserController::class, 'changePassword']);
        Route::post('admin-users/{admin_user}/toggle-status', [App\Http\Controllers\Admin\AdminUserController::class, 'toggleStatus']);

        // 修改自己的密码（所有管理员都可以）
        Route::get('profile/change-password', [App\Http\Controllers\Admin\AdminUserController::class, 'showChangePasswordForm'])->name('profile.change-password');
        Route::put('profile/change-password', [App\Http\Controllers\Admin\AdminUserController::class, 'changePassword']);

        // 测试上传页面
        Route::get('test-upload', function() {
            return view('admin.test-upload');
        })->name('test.upload');

        // 图片上传测试页面
        Route::get('upload-test', function() {
            return view('admin.upload-test');
        })->name('upload.test');

        // 管理员管理
        Route::get('admins', [App\Http\Controllers\Admin\DashboardController::class, 'admins'])->name('admins');

        // 系统设置
        Route::get('settings', [App\Http\Controllers\Admin\DashboardController::class, 'settings'])->name('settings');
    });
});

// 包含测试路由
require __DIR__.'/test-upload.php';

// 原有用户认证路由（暂时保留，如需要可以启用）
// Route::middleware(['auth'])->group(function () {
//     Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
//     // 原有设备管理路由
//     Route::resource('equipment', App\Http\Controllers\EquipmentController::class);
//     Route::resource('test-records', App\Http\Controllers\TestRecordController::class);
//     Route::resource('equipment-categories', App\Http\Controllers\EquipmentCategoryController::class);
//     Route::resource('maintenance-records', App\Http\Controllers\MaintenanceRecordController::class);
// });

// 测试路由 (开发环境使用)
if (app()->environment('local')) {
    Route::get('/test-url', function () {
        return view('test-url');
    })->name('test.url');

    Route::get('/test-products', function () {
        $featuredProducts = App\Models\Product::with('category')
            ->published()
            ->featured()
            ->ordered()
            ->take(6)
            ->get();

        return response()->json([
            'featured_products_count' => $featuredProducts->count(),
            'featured_products' => $featuredProducts->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'is_featured' => $product->is_featured,
                    'is_published' => $product->is_published,
                    'image' => $product->image,
                    'image_url' => $product->image_url,
                    'excerpt' => $product->excerpt,
                    'category' => $product->category ? $product->category->name : null,
                ];
            })
        ]);
    })->name('test.products');
}
