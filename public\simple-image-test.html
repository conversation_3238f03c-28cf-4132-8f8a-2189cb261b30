<!DOCTYPE html>
<html>
<head>
    <title>图片测试</title>
</head>
<body>
    <h1>图片路径测试</h1>
    
    <h2>测试不同的URL格式</h2>
    
    <p>1. 相对路径 (应该工作):</p>
    <img src="/storage/uploads/editor/1753962532_cnEQXuR2.png" style="max-width: 300px; border: 1px solid green;" alt="相对路径">
    
    <p>2. 完整URL (localhost):</p>
    <img src="http://localhost/storage/uploads/editor/1753962532_cnEQXuR2.png" style="max-width: 300px; border: 1px solid blue;" alt="完整URL">
    
    <p>3. 错误的端口 (8080):</p>
    <img src="http://localhost:8080/storage/uploads/editor/1753962532_cnEQXuR2.png" style="max-width: 300px; border: 1px solid red;" alt="错误端口">
    
    <h2>直接链接测试</h2>
    <p><a href="/storage/uploads/editor/1753962532_cnEQXuR2.png" target="_blank">点击查看图片 (相对路径)</a></p>
    <p><a href="http://localhost/storage/uploads/editor/1753962532_cnEQXuR2.png" target="_blank">点击查看图片 (完整URL)</a></p>
    
    <script>
        // 检查图片加载状态
        document.querySelectorAll('img').forEach(function(img, index) {
            img.onload = function() {
                console.log('图片 ' + (index + 1) + ' 加载成功');
                this.style.border = '2px solid green';
            };
            img.onerror = function() {
                console.log('图片 ' + (index + 1) + ' 加载失败: ' + this.src);
                this.style.border = '2px solid red';
                this.alt = '加载失败: ' + this.src;
            };
        });
    </script>
</body>
</html>
