<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * 产品列表页面
     */
    public function index(Request $request)
    {
        $query = Product::with('category')->published();

        // 分类筛选
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%");
            });
        }

        // 使用ordered scope（推荐产品优先）
        $products = $query->ordered()->paginate(12);

        $categories = Category::active()->ordered()->withCount(['products' => function($query) {
            $query->published();
        }])->get();

        return view('frontend.products.index', compact('products', 'categories'));
    }

    /**
     * 产品详情页面
     */
    public function show($slug)
    {
        $product = Product::with('category')->where('slug', $slug)->published()->firstOrFail();

        // 相关产品（同分类的其他产品）
        $relatedProducts = Product::with('category')
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->published()
            ->ordered()
            ->take(4)
            ->get();

        return view('frontend.products.show', compact('product', 'relatedProducts'));
    }
}
