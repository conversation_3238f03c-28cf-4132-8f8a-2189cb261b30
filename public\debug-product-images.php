<?php
// 调试产品图片显示问题
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>产品图片显示调试</h1>";

try {
    echo "<h2>数据库产品数据</h2>";
    $products = \App\Models\Product::take(10)->get();
    
    echo "<table border='1' cellpadding='10' cellspacing='0'>";
    echo "<tr><th>ID</th><th>名称</th><th>image字段</th><th>is_published</th><th>is_featured</th><th>image_url结果</th><th>文件存在</th></tr>";
    
    foreach ($products as $product) {
        $imageField = $product->image;
        $isPublished = $product->is_published ? '是' : '否';
        $isFeatured = $product->is_featured ? '是' : '否';
        $imageUrl = $product->image_url;
        
        // 检查文件是否存在
        $fileExists = '否';
        if ($imageField) {
            $fullPath = storage_path('app/public/' . $imageField);
            $fileExists = file_exists($fullPath) ? '是' : '否';
        }
        
        echo "<tr>";
        echo "<td>{$product->id}</td>";
        echo "<td>{$product->name}</td>";
        echo "<td>" . ($imageField ?: '<span style="color:red;">空</span>') . "</td>";
        echo "<td>{$isPublished}</td>";
        echo "<td>{$isFeatured}</td>";
        echo "<td>" . htmlspecialchars($imageUrl) . "</td>";
        echo "<td>{$fileExists}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>首页显示的产品</h2>";
    $featuredProducts = \App\Models\Product::with('category')
        ->published()
        ->featured()
        ->ordered()
        ->take(6)
        ->get();
    
    echo "<p>推荐产品数量: " . $featuredProducts->count() . "</p>";
    
    if ($featuredProducts->count() > 0) {
        echo "<table border='1' cellpadding='10' cellspacing='0'>";
        echo "<tr><th>产品名称</th><th>image字段</th><th>image_url</th><th>预览</th></tr>";
        
        foreach ($featuredProducts as $product) {
            echo "<tr>";
            echo "<td>{$product->name}</td>";
            echo "<td>" . ($product->image ?: '<span style="color:red;">空</span>') . "</td>";
            echo "<td>" . htmlspecialchars($product->image_url) . "</td>";
            echo "<td>";
            if ($product->image) {
                echo "<img src='{$product->image_url}' style='max-width: 100px;' onerror='this.alt=\"加载失败\"'>";
            } else {
                echo "无图片";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>没有找到推荐产品！这就是为什么显示默认图片的原因。</p>";
        
        echo "<h3>解决方案：设置推荐产品</h3>";
        $allProducts = \App\Models\Product::published()->take(3)->get();
        if ($allProducts->count() > 0) {
            echo "<form method='post'>";
            echo "<p>将前3个已发布的产品设置为推荐产品：</p>";
            foreach ($allProducts as $product) {
                echo "<p>- {$product->name}</p>";
            }
            echo "<button type='submit' name='set_featured' value='1' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>设置为推荐产品</button>";
            echo "</form>";
            
            if (isset($_POST['set_featured'])) {
                foreach ($allProducts as $product) {
                    $product->update(['is_featured' => true]);
                }
                echo "<p style='color: green;'>✅ 已设置推荐产品，请刷新首页查看效果！</p>";
            }
        }
    }
    
    echo "<h2>产品页面显示的产品</h2>";
    $allProducts = \App\Models\Product::published()->take(5)->get();
    echo "<p>已发布产品数量: " . $allProducts->count() . "</p>";
    
    if ($allProducts->count() > 0) {
        echo "<table border='1' cellpadding='10' cellspacing='0'>";
        echo "<tr><th>产品名称</th><th>image字段</th><th>image_url</th><th>预览</th></tr>";
        
        foreach ($allProducts as $product) {
            echo "<tr>";
            echo "<td>{$product->name}</td>";
            echo "<td>" . ($product->image ?: '<span style="color:red;">空</span>') . "</td>";
            echo "<td>" . htmlspecialchars($product->image_url) . "</td>";
            echo "<td>";
            if ($product->image) {
                echo "<img src='{$product->image_url}' style='max-width: 100px;' onerror='this.alt=\"加载失败\"'>";
            } else {
                echo "无图片";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
