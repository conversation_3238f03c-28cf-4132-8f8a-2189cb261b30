@extends('admin.layouts.app')

@section('title', '新闻分类详情')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.news-categories.index') }}">新闻分类</a></li>
    <li class="breadcrumb-item active">{{ $newsCategory->name }}</li>
@endsection

@section('content')
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-folder me-2"></i>{{ $newsCategory->name }}
        </h1>
        <p class="text-muted">新闻分类详情信息</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ route('admin.news-categories.edit', $newsCategory) }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>编辑分类
        </a>
        <a href="{{ route('admin.news-categories.index') }}" class="btn btn-secondary">
            <i class="fas fa-list me-2"></i>返回列表
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- 基本信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td width="120"><strong>分类名称：</strong></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="rounded me-2" 
                                     style="width: 20px; height: 20px; background-color: {{ $newsCategory->color }};"></div>
                                {{ $newsCategory->name }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>别名：</strong></td>
                        <td><code>{{ $newsCategory->slug }}</code></td>
                    </tr>
                    <tr>
                        <td><strong>分类颜色：</strong></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="rounded me-2" 
                                     style="width: 30px; height: 20px; background-color: {{ $newsCategory->color }};"></div>
                                <code>{{ $newsCategory->color }}</code>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>排序：</strong></td>
                        <td>{{ $newsCategory->sort_order }}</td>
                    </tr>
                    <tr>
                        <td><strong>状态：</strong></td>
                        <td>
                            @if($newsCategory->is_active)
                                <span class="badge bg-success">启用</span>
                            @else
                                <span class="badge bg-secondary">禁用</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>创建时间：</strong></td>
                        <td>{{ $newsCategory->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <td><strong>更新时间：</strong></td>
                        <td>{{ $newsCategory->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
                
                @if($newsCategory->description)
                    <div class="mt-4">
                        <h6><strong>分类描述：</strong></h6>
                        <div class="bg-light p-3 rounded">
                            {{ $newsCategory->description }}
                        </div>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- 分类新闻 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-newspaper me-2"></i>分类新闻 ({{ $newsCategory->news()->count() }})
                </h5>
                @if($newsCategory->news()->count() > 0)
                    <a href="{{ route('admin.news.index') }}?category={{ $newsCategory->id }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                @endif
            </div>
            <div class="card-body">
                @if($newsCategory->news->count() > 0)
                    <div class="row">
                        @foreach($newsCategory->news as $news)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            @if($news->image)
                                                <img src="{{ Storage::url($news->image) }}" 
                                                     alt="{{ $news->title }}" 
                                                     class="rounded me-3" 
                                                     style="width: 60px; height: 60px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 60px; height: 60px;">
                                                    <i class="fas fa-newspaper text-muted"></i>
                                                </div>
                                            @endif
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ Str::limit($news->title, 30) }}</h6>
                                                @if($news->summary)
                                                    <small class="text-muted">{{ Str::limit($news->summary, 50) }}</small><br>
                                                @endif
                                                <small class="text-muted">
                                                    @if($news->published_at)
                                                        {{ $news->published_at->format('Y-m-d') }}
                                                    @else
                                                        未发布
                                                    @endif
                                                </small>
                                                <div class="mt-1">
                                                    @if($news->is_published)
                                                        <span class="badge bg-success">已发布</span>
                                                    @else
                                                        <span class="badge bg-secondary">草稿</span>
                                                    @endif
                                                    @if($news->is_featured)
                                                        <span class="badge bg-warning">推荐</span>
                                                    @endif
                                                    @switch($news->type)
                                                        @case('company')
                                                            <span class="badge bg-primary">公司新闻</span>
                                                            @break
                                                        @case('industry')
                                                            <span class="badge bg-success">行业资讯</span>
                                                            @break
                                                        @case('technology')
                                                            <span class="badge bg-info">技术动态</span>
                                                            @break
                                                    @endswitch
                                                </div>
                                            </div>
                                            <a href="{{ route('admin.news.show', $news) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无新闻</h5>
                        <p class="text-muted">该分类下还没有新闻</p>
                        <a href="{{ route('admin.news.create') }}?category={{ $newsCategory->id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加新闻
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 统计信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>统计信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 class="text-primary mb-1">{{ $newsCategory->news()->count() }}</h3>
                            <small class="text-muted">新闻数量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success mb-1">{{ $newsCategory->news()->sum('views') }}</h3>
                        <small class="text-muted">总浏览量</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1">{{ $newsCategory->news()->where('is_published', true)->count() }}</h4>
                            <small class="text-muted">已发布</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1">{{ $newsCategory->news()->where('is_featured', true)->count() }}</h4>
                        <small class="text-muted">推荐新闻</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.news-categories.edit', $newsCategory) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>编辑分类
                    </a>
                    <a href="{{ route('admin.news.create') }}?category={{ $newsCategory->id }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>添加新闻
                    </a>
                    @if($newsCategory->news()->count() == 0)
                        <form action="{{ route('admin.news-categories.destroy', $newsCategory) }}" 
                              method="POST" 
                              onsubmit="return confirm('确定要删除这个分类吗？此操作不可恢复！')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>删除分类
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- 新闻类型统计 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart me-2"></i>新闻类型统计
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>公司新闻</span>
                        <span class="badge bg-primary">{{ $newsCategory->news()->where('type', 'company')->count() }}</span>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>行业资讯</span>
                        <span class="badge bg-success">{{ $newsCategory->news()->where('type', 'industry')->count() }}</span>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>技术动态</span>
                        <span class="badge bg-info">{{ $newsCategory->news()->where('type', 'technology')->count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
