<?php $__env->startSection('title', '编辑产品'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.products.index')); ?>">产品管理</a></li>
    <li class="breadcrumb-item active">编辑产品</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-edit me-2"></i>编辑产品
    </h1>
    <p class="text-muted">编辑产品：<?php echo e($product->name); ?></p>
</div>

<form action="<?php echo e(route('admin.products.update', $product)); ?>" method="POST" enctype="multipart/form-data" onsubmit="return validateForm()">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>

    <div class="row">
        <div class="col-lg-8">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="name" name="name" value="<?php echo e(old('name', $product->name)); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">产品别名</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="slug" name="slug" value="<?php echo e(old('slug', $product->slug)); ?>"
                                       placeholder="留空自动生成">
                                <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">产品分类 <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="category_id" name="category_id" required>
                                    <option value="">选择分类</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"
                                                <?php echo e(old('category_id', $product->category_id) == $category->id ? 'selected' : ''); ?>>
                                            <?php echo e($category->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">产品型号</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="model" name="model" value="<?php echo e(old('model', $product->model)); ?>">
                                <?php $__errorArgs = ['model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="brand" class="form-label">产品品牌</label>
                        <input type="text" class="form-control <?php $__errorArgs = ['brand'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="brand" name="brand" value="<?php echo e(old('brand', $product->brand)); ?>">
                        <?php $__errorArgs = ['brand'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">产品描述</label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="description" name="description" rows="3"><?php echo e(old('description', $product->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">产品详情</label>
                        <textarea class="form-control <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="content" name="content" rows="15"><?php echo e(old('content', $product->content)); ?></textarea>
                        <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">支持富文本编辑，可插入图片</div>
                    </div>
                </div>
            </div>

            <!-- 产品图片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>产品图片
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="image" class="form-label">主图</label>
                        <?php if($product->image): ?>
                            <div class="mb-2">
                                <img src="<?php echo e(Storage::url($product->image)); ?>" alt="<?php echo e($product->name); ?>"
                                     class="img-thumbnail" style="max-width: 200px;">
                                <div class="form-text">当前主图</div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="image" accept="image/*" onchange="uploadMainImage(this)">
                        <input type="hidden" name="image" id="image_path" value="<?php echo e($product->image); ?>">
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">支持 JPEG、PNG、JPG、GIF 格式，最大 2MB</div>
                        <div id="image-preview" class="mt-2" style="display: none;">
                            <img id="preview-img" src="" alt="预览图" class="img-thumbnail" style="max-width: 200px;">
                            <div class="mt-1">
                                <small class="text-success">✅ 图片已上传</small>
                            </div>
                        </div>
                        <div id="upload-progress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">上传中...</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="images" class="form-label">产品图片集</label>
                        <?php if($product->images && count($product->images) > 0): ?>
                            <div class="mb-2">
                                <div class="row">
                                    <?php $__currentLoopData = $product->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-3 mb-2">
                                            <img src="<?php echo e(Storage::url($image)); ?>" alt="<?php echo e($product->name); ?>"
                                                 class="img-thumbnail w-100">
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="form-text">当前图片集</div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="images" accept="image/*" multiple onchange="uploadMultipleImages(this)">
                        <input type="hidden" name="images" id="images_paths" value="<?php echo e($product->images ? json_encode($product->images) : ''); ?>">
                        <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">可选择多张图片，选择新图片将替换当前图片集</div>
                        <div id="images-preview" class="mt-2" style="display: none;">
                            <div class="row" id="images-container"></div>
                        </div>
                        <div id="images-upload-progress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">上传中...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品规格 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ul me-2"></i>产品规格
                    </h5>
                </div>
                <div class="card-body">
                    <div id="specifications">
                        <?php if($product->specifications && count($product->specifications) > 0): ?>
                            <?php $__currentLoopData = $product->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $name => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="row mb-2 spec-row">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="spec_names[]"
                                               placeholder="规格名称" value="<?php echo e($name); ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="spec_values[]"
                                               placeholder="规格值" value="<?php echo e($value); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger remove-spec">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="row mb-2 spec-row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="spec_names[]" placeholder="规格名称">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control" name="spec_values[]" placeholder="规格值">
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-outline-danger remove-spec">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <button type="button" class="btn btn-outline-primary" id="add-spec">
                        <i class="fas fa-plus me-2"></i>添加规格
                    </button>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 发布设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>发布设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">排序</label>
                        <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $product->sort_order)); ?>" min="0">
                        <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">数字越小排序越靠前</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_published" name="is_published"
                                   value="1" <?php echo e(old('is_published', $product->is_published) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_published">
                                发布产品
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                   value="1" <?php echo e(old('is_featured', $product->is_featured) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_featured">
                                推荐产品
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>产品信息
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>ID：</strong></td>
                            <td><?php echo e($product->id); ?></td>
                        </tr>
                        <tr>
                            <td><strong>创建时间：</strong></td>
                            <td><?php echo e($product->created_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                        <tr>
                            <td><strong>更新时间：</strong></td>
                            <td><?php echo e($product->updated_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                        <tr>
                            <td><strong>状态：</strong></td>
                            <td>
                                <?php if($product->is_published): ?>
                                    <span class="badge bg-success">已发布</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">草稿</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>更新产品
                        </button>
                        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        <a href="<?php echo e(route('admin.products.show', $product)); ?>" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>查看详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<!-- TinyMCE CSS -->
<style>
    .tox-tinymce {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
    }
    .tox-editor-header {
        border-bottom: 1px solid #ced4da !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- TinyMCE JS -->
<link rel="stylesheet" href="<?php echo e(asset('assets/css/simple-editor.css')); ?>">
<script src="<?php echo e(asset('assets/js/simple-editor.js')); ?>"></script>
<script>
    // 初始化简化富文本编辑器
    let contentEditor;
    document.addEventListener('DOMContentLoaded', function() {
        contentEditor = initSimpleEditor('#content', {
            height: '400px',
            placeholder: '请输入产品详细内容...',
            uploadUrl: '<?php echo e(route("admin.upload.image")); ?>',
            csrfToken: '<?php echo e(csrf_token()); ?>'
        });

        // 显示现有主图预览
        <?php if($product->image): ?>
            const existingImageUrl = '<?php echo e(Storage::url($product->image)); ?>';
            document.getElementById('preview-img').src = existingImageUrl;
            document.getElementById('image-preview').style.display = 'block';
        <?php endif; ?>

        // 显示现有多图预览
        <?php if($product->images && count($product->images) > 0): ?>
            const existingImages = <?php echo json_encode($product->images, 15, 512) ?>;
            const container = document.getElementById('images-container');
            container.innerHTML = '';

            existingImages.forEach((imagePath, index) => {
                const imageUrl = '<?php echo e(asset("storage")); ?>/' + imagePath;
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = `
                    <img src="${imageUrl}" alt="图片 ${index + 1}" class="img-thumbnail w-100">
                    <div class="mt-1">
                        <small class="text-info">✅ 现有图片</small>
                    </div>
                `;
                container.appendChild(col);
            });

            document.getElementById('images-preview').style.display = 'block';
        <?php endif; ?>
    });

    // 表单验证函数
    function validateForm() {
        let isValid = true;
        let errorMessages = [];

        // 验证产品名称
        const name = document.getElementById('name').value.trim();
        if (!name) {
            errorMessages.push('请输入产品名称');
            document.getElementById('name').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('name').classList.remove('is-invalid');
        }

        // 验证产品分类
        const categoryId = document.getElementById('category_id').value;
        if (!categoryId) {
            errorMessages.push('请选择产品分类');
            document.getElementById('category_id').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('category_id').classList.remove('is-invalid');
        }

        // 内容已通过编辑器自动同步到textarea

        // 显示错误信息
        if (!isValid) {
            alert('请完善以下信息：\n' + errorMessages.join('\n'));
            return false;
        }

        return true;
    }

    // TinyMCE已替换为简化富文本编辑器

    // 上传主图
    function uploadMainImage(input) {
        if (!input.files || input.files.length === 0) return;

        const file = input.files[0];
        const formData = new FormData();
        formData.append('image', file);
        formData.append('_token', '<?php echo e(csrf_token()); ?>');

        // 显示上传进度
        document.getElementById('upload-progress').style.display = 'block';
        document.getElementById('image-preview').style.display = 'none';

        fetch('<?php echo e(route("admin.upload.image")); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            document.getElementById('upload-progress').style.display = 'none';

            if (result.success) {
                // 保存图片路径到隐藏字段
                document.getElementById('image_path').value = result.path;

                // 显示预览
                document.getElementById('preview-img').src = result.url;
                document.getElementById('image-preview').style.display = 'block';
            } else {
                alert('图片上传失败: ' + result.message);
            }
        })
        .catch(error => {
            document.getElementById('upload-progress').style.display = 'none';
            alert('上传错误: ' + error.message);
        });
    }

    // 自动生成别名
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');

        if (document.getElementById('slug').value === '' || document.getElementById('slug').value === '<?php echo e($product->slug); ?>') {
            document.getElementById('slug').value = slug;
        }
    });

    // 添加规格
    document.getElementById('add-spec').addEventListener('click', function() {
        const specificationsDiv = document.getElementById('specifications');
        const newRow = document.createElement('div');
        newRow.className = 'row mb-2 spec-row';
        newRow.innerHTML = `
            <div class="col-md-4">
                <input type="text" class="form-control" name="spec_names[]" placeholder="规格名称">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" name="spec_values[]" placeholder="规格值">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger remove-spec">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        specificationsDiv.appendChild(newRow);
    });

    // 删除规格
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-spec')) {
            const row = e.target.closest('.spec-row');
            if (document.querySelectorAll('.spec-row').length > 1) {
                row.remove();
            }
        }
    });

    // 上传多图
    function uploadMultipleImages(input) {
        if (!input.files || input.files.length === 0) return;

        const files = Array.from(input.files);
        const uploadedPaths = [];
        let uploadedCount = 0;

        // 显示上传进度
        document.getElementById('images-upload-progress').style.display = 'block';
        document.getElementById('images-preview').style.display = 'none';

        const container = document.getElementById('images-container');
        container.innerHTML = '';

        files.forEach((file, index) => {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('_token', '<?php echo e(csrf_token()); ?>');

            fetch('<?php echo e(route("admin.upload.image")); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                uploadedCount++;

                if (result.success) {
                    uploadedPaths.push(result.path);

                    // 显示预览
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <img src="${result.url}" alt="图片 ${index + 1}" class="img-thumbnail w-100">
                        <div class="mt-1">
                            <small class="text-success">✅ 已上传</small>
                        </div>
                    `;
                    container.appendChild(col);
                } else {
                    // 显示错误
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <div class="alert alert-danger p-2">
                            <small>图片 ${index + 1} 上传失败</small>
                        </div>
                    `;
                    container.appendChild(col);
                }

                // 更新进度
                const progress = (uploadedCount / files.length) * 100;
                document.querySelector('#images-upload-progress .progress-bar').style.width = progress + '%';

                if (uploadedCount === files.length) {
                    document.getElementById('images-upload-progress').style.display = 'none';
                    document.getElementById('images-preview').style.display = 'block';
                    document.getElementById('images_paths').value = JSON.stringify(uploadedPaths);
                }
            })
            .catch(error => {
                uploadedCount++;
                console.error('图片上传错误:', error);

                // 显示错误
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = `
                    <div class="alert alert-danger p-2">
                        <small>图片 ${index + 1} 上传失败</small>
                    </div>
                `;
                container.appendChild(col);

                // 更新进度
                const progress = (uploadedCount / files.length) * 100;
                document.querySelector('#images-upload-progress .progress-bar').style.width = progress + '%';

                if (uploadedCount === files.length) {
                    document.getElementById('images-upload-progress').style.display = 'none';
                    document.getElementById('images-preview').style.display = 'block';
                    document.getElementById('images_paths').value = JSON.stringify(uploadedPaths);
                }
            });
        });
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/products/edit.blade.php ENDPATH**/ ?>