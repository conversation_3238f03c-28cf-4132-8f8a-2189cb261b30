<!DOCTYPE html>
<html>
<head>
    <title>测试产品图片上传</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>测试产品图片上传</h2>
        
        <div class="card">
            <div class="card-body">
                <form id="testForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="image" class="form-label">选择产品图片</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                    </div>
                    <button type="button" class="btn btn-primary" onclick="testUpload()">测试上传</button>
                </form>
                
                <div id="result" class="mt-3" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        function testUpload() {
            const input = document.getElementById('image');
            const file = input.files[0];
            
            if (!file) {
                alert('请选择一个图片文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('image', file);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info">上传中...</div>';
            
            fetch('/test-product-upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>上传成功！</h5>
                            <p><strong>路径:</strong> ${result.path}</p>
                            <p><strong>URL:</strong> ${result.url}</p>
                            <p><strong>文件信息:</strong> ${result.file_info.name} (${result.file_info.size} bytes)</p>
                            <img src="${result.url}" class="img-thumbnail mt-2" style="max-width: 300px;">
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>上传失败</h5>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>上传错误</h5>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }
    </script>
</body>
</html>
