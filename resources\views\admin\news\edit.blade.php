@extends('admin.layouts.app')

@section('title', '编辑新闻')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.news.index') }}">新闻管理</a></li>
    <li class="breadcrumb-item active">编辑新闻</li>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-edit me-2"></i>编辑新闻
    </h1>
    <p class="text-muted">编辑新闻：{{ $news->title }}</p>
</div>

<form action="{{ route('admin.news.update', $news) }}" method="POST" enctype="multipart/form-data">
    @csrf
    @method('PUT')

    <div class="row">
        <div class="col-lg-8">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">新闻标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror"
                                       id="title" name="title" value="{{ old('title', $news->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="slug" class="form-label">新闻别名</label>
                                <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                       id="slug" name="slug" value="{{ old('slug', $news->slug) }}"
                                       placeholder="留空自动生成">
                                @error('slug')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">


                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">新闻分类 <span class="text-danger">*</span></label>
                                <select class="form-select @error('category_id') is-invalid @enderror"
                                        id="category_id" name="category_id" required>
                                    <option value="">选择分类</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id', $news->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="author" class="form-label">作者</label>
                                <input type="text" class="form-control @error('author') is-invalid @enderror"
                                       id="author" name="author" value="{{ old('author', $news->author) }}">
                                @error('author')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="source" class="form-label">来源</label>
                                <input type="text" class="form-control @error('source') is-invalid @enderror"
                                       id="source" name="source" value="{{ old('source', $news->source) }}">
                                @error('source')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="summary" class="form-label">新闻摘要</label>
                        <textarea class="form-control @error('summary') is-invalid @enderror"
                                  id="summary" name="summary" rows="3">{{ old('summary', $news->summary) }}</textarea>
                        @error('summary')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">新闻内容 <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content" name="content" rows="15">{{ old('content', $news->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">支持富文本编辑，可插入图片</div>
                    </div>
                </div>
            </div>

            <!-- 新闻图片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-image me-2"></i>新闻图片
                    </h5>
                </div>
                <div class="card-body">
                    @if($news->image)
                        <div class="mb-2">
                            <img src="{{ Storage::url($news->image) }}" alt="{{ $news->title }}"
                                 class="img-thumbnail" style="max-width: 300px;">
                            <div class="form-text">当前图片</div>
                        </div>
                    @endif
                    <input type="file" class="form-control @error('image') is-invalid @enderror"
                           id="image" accept="image/*" onchange="uploadNewsImage(this)">
                    <input type="hidden" name="image" id="image_path" value="{{ $news->image }}">
                    @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">支持 JPEG、PNG、JPG、GIF 格式，最大 2MB。选择新图片将替换当前图片。</div>
                    <div id="image-preview" class="mt-2" style="display: none;">
                        <img id="preview-img" src="" alt="预览图" class="img-thumbnail" style="max-width: 200px;">
                        <div class="mt-1">
                            <small class="text-success">✅ 图片已上传</small>
                        </div>
                    </div>
                    <div id="upload-progress" class="mt-2" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">上传中...</small>
                    </div>
                </div>
            </div>

            <!-- SEO设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>SEO设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">SEO标题</label>
                        <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                               id="meta_title" name="meta_title" value="{{ old('meta_title', $news->meta_title) }}">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">SEO描述</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                  id="meta_description" name="meta_description" rows="3">{{ old('meta_description', $news->meta_description) }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">SEO关键词</label>
                        <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror"
                               id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords', $news->meta_keywords) }}"
                               placeholder="用逗号分隔多个关键词">
                        @error('meta_keywords')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 发布设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>发布设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="published_at" class="form-label">发布时间</label>
                        <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror"
                               id="published_at" name="published_at"
                               value="{{ old('published_at', $news->published_at ? $news->published_at->format('Y-m-d\TH:i') : '') }}">
                        @error('published_at')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">留空则使用当前时间</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_published" name="is_published"
                                   value="1" {{ old('is_published', $news->is_published) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                发布新闻
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                   value="1" {{ old('is_featured', $news->is_featured) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_featured">
                                推荐新闻
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新闻信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>新闻信息
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>ID：</strong></td>
                            <td>{{ $news->id }}</td>
                        </tr>
                        <tr>
                            <td><strong>浏览量：</strong></td>
                            <td><span class="badge bg-secondary">{{ $news->views }}</span></td>
                        </tr>
                        <tr>
                            <td><strong>创建时间：</strong></td>
                            <td>{{ $news->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>更新时间：</strong></td>
                            <td>{{ $news->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>状态：</strong></td>
                            <td>
                                @if($news->is_published)
                                    <span class="badge bg-success">已发布</span>
                                @else
                                    <span class="badge bg-secondary">草稿</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" onclick="return validateForm()">
                            <i class="fas fa-save me-2"></i>更新新闻
                        </button>
                        <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>取消
                        </a>
                        <a href="{{ route('admin.news.show', $news) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>查看详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<!-- 简化编辑器样式已在CSS文件中定义 -->
@endpush

@push('scripts')
<!-- TinyMCE JS -->
<link rel="stylesheet" href="{{ asset('assets/css/simple-editor.css') }}">
<script src="{{ asset('assets/js/simple-editor.js') }}"></script>
<script>
    // 初始化简化富文本编辑器
    let contentEditor;
    document.addEventListener('DOMContentLoaded', function() {
        contentEditor = initSimpleEditor('#content', {
            height: '400px',
            placeholder: '请输入新闻详细内容...',
            uploadUrl: '{{ route("admin.upload.image") }}',
            csrfToken: '{{ csrf_token() }}'
        });

        // 显示现有图片预览
        @if($news->image)
            const existingImageUrl = '{{ Storage::url($news->image) }}';
            document.getElementById('preview-img').src = existingImageUrl;
            document.getElementById('image-preview').style.display = 'block';
        @endif
    });

    // 表单验证函数
    function validateForm() {
        let isValid = true;
        let errorMessages = [];

        // 验证新闻标题
        const title = document.getElementById('title').value.trim();
        if (!title) {
            errorMessages.push('请输入新闻标题');
            document.getElementById('title').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('title').classList.remove('is-invalid');
        }

        // 验证新闻分类
        const category = document.getElementById('category_id').value;
        if (!category) {
            errorMessages.push('请选择新闻分类');
            document.getElementById('category_id').classList.add('is-invalid');
            isValid = false;
        } else {
            document.getElementById('category_id').classList.remove('is-invalid');
        }



        // 验证新闻内容
        const content = document.getElementById('content').value.trim();
        if (!content || content === '<p></p>' || content === '<p><br></p>') {
            errorMessages.push('请输入新闻内容');
            isValid = false;
        }

        // 显示错误信息
        if (!isValid) {
            alert('请完善以下信息：\n' + errorMessages.join('\n'));
            return false;
        }

        return true;
    }

    // TinyMCE已替换为简化富文本编辑器

    // 自动生成别名
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');

        if (document.getElementById('slug').value === '' || document.getElementById('slug').value === '{{ $news->slug }}') {
            document.getElementById('slug').value = slug;
        }
    });

    // 上传新闻图片
    function uploadNewsImage(input) {
        if (!input.files || input.files.length === 0) return;

        const file = input.files[0];
        const formData = new FormData();
        formData.append('image', file);
        formData.append('_token', '{{ csrf_token() }}');

        // 显示上传进度
        document.getElementById('upload-progress').style.display = 'block';
        document.getElementById('image-preview').style.display = 'none';

        fetch('{{ route("admin.upload.image") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            document.getElementById('upload-progress').style.display = 'none';

            if (result.success) {
                // 保存图片路径到隐藏字段
                document.getElementById('image_path').value = result.path;

                // 显示预览
                document.getElementById('preview-img').src = result.url;
                document.getElementById('image-preview').style.display = 'block';
            } else {
                alert('图片上传失败: ' + result.message);
            }
        })
        .catch(error => {
            document.getElementById('upload-progress').style.display = 'none';
            alert('上传错误: ' + error.message);
        });
    }
</script>
@endpush
