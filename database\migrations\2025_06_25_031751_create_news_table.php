<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('新闻标题');
            $table->string('slug')->unique()->comment('新闻别名');
            $table->text('summary')->nullable()->comment('新闻摘要');
            $table->text('content')->comment('新闻内容');
            $table->string('image')->nullable()->comment('新闻图片');
            $table->string('author')->nullable()->comment('作者');
            $table->string('source')->nullable()->comment('来源');
            $table->enum('type', ['company', 'industry', 'technology'])->default('company')->comment('新闻类型');
            $table->integer('views')->default(0)->comment('浏览次数');
            $table->boolean('is_featured')->default(false)->comment('是否推荐');
            $table->boolean('is_published')->default(true)->comment('是否发布');
            $table->datetime('published_at')->nullable()->comment('发布时间');
            $table->string('meta_title')->nullable()->comment('SEO标题');
            $table->text('meta_description')->nullable()->comment('SEO描述');
            $table->string('meta_keywords')->nullable()->comment('SEO关键词');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news');
    }
};
