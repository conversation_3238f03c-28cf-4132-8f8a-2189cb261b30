<?php
// 修复推荐产品设置
require_once '../vendor/autoload.php';

// 模拟Laravel环境
$app = require_once '../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h1>修复推荐产品设置</h1>";

try {
    // 获取所有已发布的产品
    $products = \App\Models\Product::published()->get();
    
    echo "<h2>当前产品状态</h2>";
    echo "<table border='1' cellpadding='10' cellspacing='0'>";
    echo "<tr><th>ID</th><th>名称</th><th>已发布</th><th>推荐状态</th><th>有图片</th></tr>";
    
    foreach ($products as $product) {
        $hasImage = $product->image ? '是' : '否';
        $isFeatured = $product->is_featured ? '是' : '否';
        $isPublished = $product->is_published ? '是' : '否';
        
        echo "<tr>";
        echo "<td>{$product->id}</td>";
        echo "<td>{$product->name}</td>";
        echo "<td>{$isPublished}</td>";
        echo "<td>{$isFeatured}</td>";
        echo "<td>{$hasImage}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 自动设置前3个产品为推荐产品
    $productsToFeature = \App\Models\Product::published()->take(3)->get();
    
    if ($productsToFeature->count() > 0) {
        echo "<h2>设置推荐产品</h2>";
        echo "<p>正在将前3个产品设置为推荐产品...</p>";
        
        foreach ($productsToFeature as $product) {
            $product->update(['is_featured' => true]);
            echo "<p>✅ {$product->name} 已设置为推荐产品</p>";
        }
        
        echo "<p><strong>完成！请刷新首页查看效果。</strong></p>";
        echo "<p><a href='http://localhost:8080/' target='_blank'>点击查看首页</a></p>";
    } else {
        echo "<p style='color: red;'>没有找到已发布的产品。请先在后台添加产品。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
