<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f9ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e0f2fe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bae6fd;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="url(#bgGradient)"/>
  
  <!-- 建筑物轮廓 -->
  <g fill="url(#buildingGradient)" opacity="0.8">
    <!-- 主建筑 -->
    <rect x="200" y="200" width="400" height="300" rx="10"/>
    <!-- 左侧建筑 -->
    <rect x="100" y="250" width="120" height="250" rx="5"/>
    <!-- 右侧建筑 -->
    <rect x="580" y="180" width="100" height="320" rx="5"/>
  </g>
  
  <!-- 窗户 -->
  <g fill="#ffffff" opacity="0.6">
    <!-- 主建筑窗户 -->
    <rect x="230" y="230" width="30" height="40" rx="3"/>
    <rect x="280" y="230" width="30" height="40" rx="3"/>
    <rect x="330" y="230" width="30" height="40" rx="3"/>
    <rect x="380" y="230" width="30" height="40" rx="3"/>
    <rect x="430" y="230" width="30" height="40" rx="3"/>
    <rect x="480" y="230" width="30" height="40" rx="3"/>
    <rect x="530" y="230" width="30" height="40" rx="3"/>
    
    <rect x="230" y="300" width="30" height="40" rx="3"/>
    <rect x="280" y="300" width="30" height="40" rx="3"/>
    <rect x="330" y="300" width="30" height="40" rx="3"/>
    <rect x="380" y="300" width="30" height="40" rx="3"/>
    <rect x="430" y="300" width="30" height="40" rx="3"/>
    <rect x="480" y="300" width="30" height="40" rx="3"/>
    <rect x="530" y="300" width="30" height="40" rx="3"/>
    
    <rect x="230" y="370" width="30" height="40" rx="3"/>
    <rect x="280" y="370" width="30" height="40" rx="3"/>
    <rect x="330" y="370" width="30" height="40" rx="3"/>
    <rect x="380" y="370" width="30" height="40" rx="3"/>
    <rect x="430" y="370" width="30" height="40" rx="3"/>
    <rect x="480" y="370" width="30" height="40" rx="3"/>
    <rect x="530" y="370" width="30" height="40" rx="3"/>
    
    <!-- 左侧建筑窗户 -->
    <rect x="120" y="280" width="20" height="25" rx="2"/>
    <rect x="150" y="280" width="20" height="25" rx="2"/>
    <rect x="180" y="280" width="20" height="25" rx="2"/>
    
    <rect x="120" y="320" width="20" height="25" rx="2"/>
    <rect x="150" y="320" width="20" height="25" rx="2"/>
    <rect x="180" y="320" width="20" height="25" rx="2"/>
    
    <!-- 右侧建筑窗户 -->
    <rect x="600" y="210" width="20" height="25" rx="2"/>
    <rect x="630" y="210" width="20" height="25" rx="2"/>
    <rect x="600" y="250" width="20" height="25" rx="2"/>
    <rect x="630" y="250" width="20" height="25" rx="2"/>
  </g>
  
  <!-- 装饰元素 -->
  <g fill="#3b82f6" opacity="0.3">
    <circle cx="150" cy="150" r="30"/>
    <circle cx="650" cy="120" r="20"/>
    <circle cx="700" cy="400" r="25"/>
  </g>
  
  <!-- 文字 -->
  <text x="400" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1e40af" opacity="0.7">
    关于我们 - 睿测微智能科技
  </text>
</svg>
