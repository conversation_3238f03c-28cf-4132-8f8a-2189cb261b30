@extends('frontend.layouts.app')

@section('title', '产品中心 - 睿测微智能科技')
@section('description', '睿测微智能科技产品中心，提供专业的智能测试设备、半导体测试解决方案，高低温测试系统等产品')
@section('keywords', '智能测试设备,半导体测试,高低温测试系统,芯片检测,产品中心')

@section('content')
<style>
/* 产品中心Banner样式 - 左右分栏布局 */
.products-hero-banner {
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
}

.products-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
}

.products-text-section {
    flex: 1;
    padding: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 2;
}

.products-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    background: url('{{ asset('images/banner/product.png') }}') right center/contain no-repeat;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.company-name {
    font-size: 2.8rem;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.company-tagline {
    font-size: 1.4rem;
    color: #3b82f6;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.company-description {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid #3b82f6;
    color: #3b82f6;
    padding: 10px 28px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
}

.hero-content {
    z-index: 10;
}

.hero-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
    animation: pulse 2s ease-in-out infinite;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
    background: linear-gradient(45deg, #ffffff, #e0f2fe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    opacity: 0.95;
    font-weight: 300;
}

.hero-stats {
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 0 1.5rem;
    position: relative;
}

.stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    margin-top: 0.5rem;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(-10px) rotate(240deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* 装饰元素 */
.products-hero-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .products-hero-banner {
        height: auto;
        min-height: 600px;
    }

    .products-banner-content {
        flex-direction: column;
    }

    .products-text-section {
        flex: none;
        padding: 40px 30px;
        text-align: center;
    }

    .products-image-section {
        flex: none;
        height: 300px;
        background-size: contain;
        background-position: center;
    }

    .company-name {
        font-size: 2.2rem;
    }

    .company-tagline {
        font-size: 1.2rem;
    }

    .cta-buttons {
        justify-content: center;
    }
}
</style>
<!-- 产品中心Banner -->
<section class="py-2 bg-light">
    <div class="container">
        <div class="row mb-2" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="products-hero-banner">
                        <div class="products-banner-content">
                            <!-- 文字内容区域 -->
                            <div class="products-text-section">
                                <h1 class="company-name">
                                    上海睿测微智能
                                </h1>
                                <p class="company-tagline">
                                    专业的智能测试设备与解决方案
                                </p>
                                <p class="company-description">
                                    致力于为半导体、电子制造等行业提供高精度、高可靠性的测试设备，
                                    包括高低温测试系统、芯片检测设备、智能测试平台等产品，
                                    助力客户提升产品质量和生产效率。
                                </p>
                                <div class="cta-buttons">
                                    <a href="#products" class="btn-primary-custom">
                                        <i class="fas fa-search me-2"></i>浏览产品
                                    </a>
                                    <a href="{{ route('contact') }}" class="btn-outline-custom">
                                        <i class="fas fa-phone me-2"></i>联系我们
                                    </a>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="products-image-section">
                                <!-- 背景图片通过CSS设置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- 搜索和筛选 -->
<section id="products" class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <form method="GET" class="d-flex gap-3">
                    <div class="flex-grow-1">
                        <input type="text" name="search" class="form-control"
                               placeholder="搜索产品名称、型号..."
                               value="{{ request('search') }}">
                    </div>
                    <select name="category" class="form-select" style="width: auto;">
                        <option value="">所有分类</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}"
                                    {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }} ({{ $category->products_count }})
                            </option>
                        @endforeach
                    </select>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>搜索
                    </button>
                </form>
            </div>
            <div class="col-lg-4 text-end">
                <span class="text-muted">共找到 {{ $products->total() }} 个产品</span>
            </div>
        </div>
    </div>
</section>



<!-- 产品列表 -->
<section class="py-5">
    <div class="container">
        @if($products->count() > 0)
            <div class="row">
                @foreach($products as $product)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 50 }}">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative">
                                @if($product->image)
                                    <img src="{{ config('app.url') }}/storage/{{ $product->image }}"
                                         class="card-img-top" alt="{{ $product->name }}"
                                         style="height: 250px; object-fit: cover;">
                                @else
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                         style="height: 250px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                @endif

                                <!-- 推荐产品标识 -->
                                @if($product->is_featured)
                                    <div class="position-absolute top-0 end-0 m-3">
                                        <span class="badge bg-warning text-dark px-3 py-2 rounded-pill shadow-sm">
                                            <i class="fas fa-star me-1"></i>推荐
                                        </span>
                                    </div>
                                @endif
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="card-title mb-0">{{ $product->name }}</h5>
                                    @if($product->category)
                                        <span class="badge" style="background-color: {{ $product->category->color }};">
                                            {{ $product->category->name }}
                                        </span>
                                    @endif
                                </div>
                                @if($product->model)
                                    <p class="text-muted small mb-2">型号：{{ $product->model }}</p>
                                @endif
                                @if($product->brand)
                                    <p class="text-muted small mb-2">品牌：{{ $product->brand }}</p>
                                @endif
                                <p class="card-text text-muted">{{ Str::limit($product->description, 100) }}</p>
                            </div>
                            <div class="card-footer bg-transparent border-0 pt-0">
                                <a href="{{ route('products.show', $product->slug) }}"
                                   class="btn btn-outline-primary w-100">
                                    <i class="fas fa-eye me-2"></i>查看详情
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center mt-5">
                {{ $products->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">暂无产品</h4>
                <p class="text-muted">
                    @if(request()->hasAny(['search', 'category']))
                        没有找到符合条件的产品，请尝试其他搜索条件
                    @else
                        暂时还没有产品信息
                    @endif
                </p>
                @if(request()->hasAny(['search', 'category']))
                    <a href="{{ route('products.index') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>查看所有产品
                    </a>
                @endif
            </div>
        @endif
    </div>
</section>
@endsection

@push('styles')
<style>
    /* 产品展示区域 */
    .product-showcase {
        position: relative;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .center-product {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
        animation: productPulse 3s ease-in-out infinite;
    }

    @keyframes productPulse {
        0%, 100% { transform: translate(-50%, -50%) scale(1); }
        50% { transform: translate(-50%, -50%) scale(1.1); }
    }

    /* 产品特性指示器 */
    .feature-item {
        position: absolute;
        display: flex;
        align-items: center;
        gap: 15px;
        animation: featureFloat 4s ease-in-out infinite;
    }

    .feature-item:nth-child(1) { animation-delay: 0s; }
    .feature-item:nth-child(2) { animation-delay: 1s; }
    .feature-item:nth-child(3) { animation-delay: 2s; }
    .feature-item:nth-child(4) { animation-delay: 3s; }

    @keyframes featureFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .feature-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        border: 3px solid rgba(255, 255, 255, 0.2);
    }

    .feature-label {
        color: white;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .feature-label strong {
        font-size: 1rem;
        display: block;
        margin-bottom: 2px;
    }

    .feature-label small {
        font-size: 0.8rem;
        opacity: 0.9;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .product-showcase {
            height: 400px;
        }

        .feature-item {
            gap: 10px;
        }

        .feature-circle {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .feature-label strong {
            font-size: 0.9rem;
        }

        .feature-label small {
            font-size: 0.7rem;
        }

        .center-product i {
            font-size: 4rem !important;
        }
    }
</style>
@endpush

@push('styles')
<style>
    /* 产品中心Banner样式 */
    .products-hero-banner {
        height: 600px;
        display: flex;
        align-items: center;
        position: relative;
    }

    .products-bg-image {
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)),
                    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><linearGradient id="labGrad" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:%23f5f5f5;stop-opacity:1" /><stop offset="100%" style="stop-color:%23e8eaf6;stop-opacity:1" /></linearGradient></defs><rect width="1200" height="800" fill="url(%23labGrad)"/><g fill="%23424242" opacity="0.8"><rect x="0" y="600" width="1200" height="200"/><rect x="100" y="500" width="200" height="100" rx="10"/><rect x="350" y="480" width="180" height="120" rx="15"/><rect x="580" y="460" width="220" height="140" rx="12"/><rect x="850" y="490" width="160" height="110" rx="8"/></g><g fill="%23616161" opacity="0.6"><rect x="120" y="520" width="160" height="60" rx="5"/><rect x="370" y="500" width="140" height="80" rx="8"/><rect x="600" y="480" width="180" height="100" rx="10"/><rect x="870" y="510" width="120" height="70" rx="6"/></g><g fill="%2300bcd4" opacity="0.7"><circle cx="200" cy="550" r="15"/><circle cx="450" cy="540" r="18"/><circle cx="690" cy="530" r="20"/><circle cx="930" cy="545" r="16"/><rect x="190" y="540" width="20" height="4"/><rect x="440" y="530" width="20" height="4"/><rect x="680" y="520" width="20" height="4"/><rect x="920" y="535" width="20" height="4"/></g><g fill="%23ff9800" opacity="0.5"><rect x="50" y="400" width="80" height="100"/><rect x="300" y="380" width="60" height="120"/><rect x="550" y="360" width="90" height="140"/><rect x="800" y="390" width="70" height="110"/><rect x="1050" y="410" width="75" height="90"/></g><g fill="%234caf50" opacity="0.6"><circle cx="80" cy="200" r="25"/><circle cx="320" cy="180" r="30"/><circle cx="580" cy="160" r="35"/><circle cx="820" cy="190" r="28"/><circle cx="1080" cy="210" r="22"/></g><g stroke="%23333333" stroke-width="2" fill="none" opacity="0.4"><path d="M150,300 Q300,250 450,300 T750,300"/><path d="M200,350 L400,350 L400,250 L600,250"/><path d="M500,200 L700,200 L700,100 L900,100"/></g></svg>');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .company-badge {
        animation: badgeFloat 3s ease-in-out infinite;
    }

    @keyframes badgeFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .products-hero-banner {
            height: 400px;
        }

        .products-bg-image {
            background-attachment: scroll;
        }

        .hero-content h1 {
            font-size: 2.5rem !important;
        }

        .hero-content .lead {
            font-size: 1.1rem !important;
        }
    }

    .product-card {
        transition: all 0.3s ease;
    }
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
    }
    .page-header {
        background: var(--gradient-primary);
    }
</style>
@endpush
