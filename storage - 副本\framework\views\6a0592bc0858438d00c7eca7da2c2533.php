<?php $__env->startSection('title', '产品管理'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">控制台</a></li>
    <li class="breadcrumb-item active">产品管理</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="page-header d-flex justify-content-between align-items-center">
    <div>
        <h1 class="page-title">
            <i class="fas fa-cube me-2"></i>产品管理
        </h1>
        <p class="text-muted">管理网站产品信息</p>
    </div>
    <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>添加产品
    </a>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.products.index')); ?>">
            <div class="row g-3">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="search"
                           placeholder="搜索产品名称、型号、品牌..."
                           value="<?php echo e(request('search')); ?>">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category">
                        <option value="">所有分类</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>"
                                    <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="status">
                        <option value="">所有状态</option>
                        <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>已发布</option>
                        <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>草稿</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>产品列表
        </h5>
    </div>
    <div class="card-body">
        <?php if($products->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>产品信息</th>
                            <th>分类</th>
                            <th>型号/品牌</th>
                            <th>状态</th>
                            <th>排序</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($product->id); ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($product->image): ?>
                                            <img src="<?php echo e(Storage::url($product->image)); ?>"
                                                 alt="<?php echo e($product->name); ?>"
                                                 class="rounded me-3"
                                                 style="width: 60px; height: 60px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo e($product->name); ?></strong>
                                            <?php if($product->description): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($product->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if($product->category): ?>
                                        <span class="badge bg-info"><?php echo e($product->category->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->model): ?>
                                        <div><strong><?php echo e($product->model); ?></strong></div>
                                    <?php endif; ?>
                                    <?php if($product->brand): ?>
                                        <small class="text-muted"><?php echo e($product->brand); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <?php if($product->is_published): ?>
                                            <span class="badge bg-success">已发布</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">草稿</span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if($product->is_featured): ?>
                                        <div class="mt-1">
                                            <span class="badge bg-warning">推荐</span>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($product->sort_order); ?></td>
                                <td><?php echo e($product->created_at->format('Y-m-d')); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.products.show', $product)); ?>"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.products.edit', $product)); ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.products.destroy', $product)); ?>"
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('确定要删除这个产品吗？')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center">
                <?php echo e($products->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-cube fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无产品</h4>
                <p class="text-muted">点击上方按钮添加第一个产品</p>
                <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加产品
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/admin/products/index.blade.php ENDPATH**/ ?>