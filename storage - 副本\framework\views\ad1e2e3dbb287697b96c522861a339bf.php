<?php $__env->startSection('title', '新闻资讯 - 睿测微智能科技'); ?>
<?php $__env->startSection('description', '睿测微智能科技新闻资讯中心，了解最新的行业动态、技术发展和公司新闻'); ?>
<?php $__env->startSection('keywords', '新闻资讯,行业动态,技术发展,公司新闻,睿测微'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.news-card {
    transition: all 0.3s ease;
    border-radius: 15px !important;
    overflow: hidden;
}

.news-card:hover {
    transform: translateY(-5px);
}

.hover-shadow:hover {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.news-card .card-img-top {
    transition: transform 0.3s ease;
}

.news-card:hover .card-img-top {
    transform: scale(1.05);
}

.news-card .stretched-link::after {
    z-index: 1;
}

.news-card .badge {
    backdrop-filter: blur(10px);
    background-color: rgba(var(--bs-primary-rgb), 0.9) !important;
}

.search-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.search-form .form-control, .search-form .form-select {
    border-radius: 10px;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-form .btn {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.hover-bg-light:hover {
    background-color: rgba(0,0,0,0.02) !important;
    transition: background-color 0.2s ease;
}

.card {
    border-radius: 15px !important;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.rounded-4 {
    border-radius: 15px !important;
}

.rounded-top-4 {
    border-radius: 15px 15px 0 0 !important;
}

/* 新闻资讯Banner样式 - 左右分栏布局 */
.news-hero-banner {
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 70%, #7dd3fc 100%);
}

.news-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 10;
}

.news-text-section {
    flex: 1;
    padding: 60px;
    color: #1e293b;
    position: relative;
    z-index: 3;
}

.news-image-section {
    flex: 1;
    position: relative;
    height: 100%;
    background: url('<?php echo e(asset('images/banner/product.png')); ?>') right center/contain no-repeat;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.company-name {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e40af;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.company-tagline {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0369a1;
    margin-bottom: 1.5rem;
    border-left: 4px solid #0369a1;
    padding-left: 1rem;
}

.company-description {
    font-size: 1.1rem;
    color: #475569;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #0369a1, #1e40af);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(3, 105, 161, 0.3);
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.4);
    color: white;
}

.btn-outline-custom {
    background: transparent;
    border: 2px solid #0369a1;
    color: #0369a1;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background: #0369a1;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(3, 105, 161, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .news-hero-banner {
        height: auto;
        min-height: 600px;
    }

    .news-banner-content {
        flex-direction: column;
    }

    .news-text-section {
        flex: none;
        padding: 40px 30px;
        text-align: center;
    }

    .news-image-section {
        flex: none;
        height: 300px;
        background-size: contain;
        background-position: center;
    }

    .company-name {
        font-size: 2.2rem;
    }

    .company-tagline {
        font-size: 1.2rem;
    }

    .cta-buttons {
        justify-content: center;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- 新闻资讯Banner -->
<section class="py-4">
    <div class="container">
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-12">
                <div class="content-card card border-0 shadow-lg overflow-hidden">
                    <div class="news-hero-banner">
                        <div class="news-banner-content">
                            <!-- 左侧文字内容区域 -->
                            <div class="news-text-section">
                                <h1 class="company-name">
                                    新闻资讯
                                </h1>
                                <p class="company-tagline">
                                    关注芯片测试行业发展趋势
                                </p>
                                <p class="company-description">
                                    及时了解半导体测试技术的最新发展动态，掌握行业前沿资讯，
                                    为您提供专业的技术洞察和市场分析，助力企业把握发展机遇。
                                </p>
                                <div class="cta-buttons">
                                    <a href="#news-list" class="btn-primary-custom">
                                        <i class="fas fa-newspaper me-2"></i>浏览资讯
                                    </a>
                                    <a href="<?php echo e(route('contact')); ?>" class="btn-outline-custom">
                                        <i class="fas fa-envelope me-2"></i>订阅更新
                                    </a>
                                </div>
                            </div>

                            <!-- 右侧图片区域 -->
                            <div class="news-image-section">
                                <!-- 背景图片通过CSS设置 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <div class="row">
        <!-- 新闻列表 -->
        <div class="col-lg-8">
            <?php if($news->count() > 0): ?>
                <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <article class="card news-card mb-4 border-0 shadow-sm hover-shadow" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                        <div class="row g-0">
                            <?php if($newsItem->image && file_exists(public_path('storage/' . $newsItem->image))): ?>
                                <div class="col-md-4">
                                    <div class="position-relative overflow-hidden h-100">
                                        <img src="<?php echo e($newsItem->image_url); ?>"
                                             class="img-fluid h-100 w-100"
                                             alt="<?php echo e($newsItem->title); ?>"
                                             style="object-fit: cover; min-height: 200px;">
                                    </div>
                                </div>
                                <div class="col-md-8">
                            <?php else: ?>
                                <div class="col-12">
                            <?php endif; ?>
                                    <div class="card-body h-100 d-flex flex-column">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="d-flex gap-2 flex-wrap">
                                                <?php switch($newsItem->type):
                                                    case ('company'): ?>
                                                        <span class="badge bg-primary px-3 py-2">公司新闻</span>
                                                        <?php break; ?>
                                                    <?php case ('industry'): ?>
                                                        <span class="badge bg-success px-3 py-2">行业资讯</span>
                                                        <?php break; ?>
                                                    <?php case ('technology'): ?>
                                                        <span class="badge bg-info px-3 py-2">技术动态</span>
                                                        <?php break; ?>
                                                <?php endswitch; ?>
                                                <?php if($newsItem->category): ?>
                                                    <span class="badge px-3 py-2"
                                                          style="background-color: <?php echo e($newsItem->category->color); ?>; color: white;">
                                                        <i class="fas fa-folder me-1"></i><?php echo e($newsItem->category->name); ?>

                                                    </span>
                                                <?php endif; ?>
                                                <?php if($newsItem->is_featured): ?>
                                                    <span class="badge bg-warning text-dark px-3 py-2">推荐</span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i><?php echo e($newsItem->views); ?>

                                            </small>
                                        </div>

                                        <h5 class="card-title fw-bold mb-3">
                                            <a href="<?php echo e(route('news.show', $newsItem->slug)); ?>"
                                               class="text-decoration-none text-dark">
                                                <?php echo e($newsItem->title); ?>

                                            </a>
                                        </h5>

                                        <?php if($newsItem->summary): ?>
                                            <p class="card-text text-muted mb-3"><?php echo e(Str::limit($newsItem->summary, 150)); ?></p>
                                        <?php else: ?>
                                            <p class="card-text text-muted mb-3"><?php echo e(Str::limit(strip_tags($newsItem->content), 150)); ?></p>
                                        <?php endif; ?>

                                        <div class="mt-auto">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="text-muted small d-flex align-items-center gap-3">
                                                    <?php if($newsItem->author): ?>
                                                        <span><i class="fas fa-user me-1"></i><?php echo e($newsItem->author); ?></span>
                                                    <?php endif; ?>
                                                    <span><i class="fas fa-calendar me-1"></i><?php echo e($newsItem->published_at->format('Y-m-d')); ?></span>
                                                    <?php if($newsItem->source): ?>
                                                        <span><i class="fas fa-link me-1"></i><?php echo e($newsItem->source); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <a href="<?php echo e(route('news.show', $newsItem->slug)); ?>"
                                                   class="btn btn-outline-primary btn-sm">
                                                    阅读全文 <i class="fas fa-arrow-right ms-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- 分页 -->
                <div class="d-flex justify-content-center mt-5">
                    <?php echo e($news->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无新闻</h4>
                    <p class="text-muted">
                        <?php if(request()->hasAny(['search', 'category', 'type'])): ?>
                            没有找到符合条件的新闻，请尝试其他搜索条件
                        <?php else: ?>
                            暂时还没有新闻资讯
                        <?php endif; ?>
                    </p>
                    <?php if(request()->hasAny(['search', 'category', 'type'])): ?>
                        <a href="<?php echo e(route('news.index')); ?>" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>查看所有新闻
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 新闻栏目 -->
            <?php if($categories->count() > 0): ?>
                <div class="card border-0 shadow-sm mb-4 rounded-4" data-aos="fade-left">
                    <div class="card-header bg-gradient text-white border-0 rounded-top-4"
                         style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <h6 class="mb-0 fw-bold"><i class="fas fa-folder me-2"></i>新闻栏目</h6>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('news.index', ['category' => $category->id])); ?>"
                               class="d-flex justify-content-between align-items-center text-decoration-none text-dark p-3 border-bottom hover-bg-light position-relative">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle me-3"
                                         style="width: 12px; height: 12px; background-color: <?php echo e($category->color); ?>;"></div>
                                    <span class="fw-bold"><?php echo e($category->name); ?></span>
                                </div>
                                <span class="badge bg-secondary rounded-pill"><?php echo e($category->news_count); ?></span>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* 新闻资讯Banner样式 */
    .news-hero-banner {
        height: 600px;
        display: flex;
        align-items: center;
        position: relative;
    }

    .news-bg-image {
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)),
                    url('<?php echo e(asset('images/banner/news-banner.png')); ?>');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .company-badge {
        animation: badgeFloat 3s ease-in-out infinite;
    }

    @keyframes badgeFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .news-hero-banner {
            height: 400px;
        }

        .news-bg-image {
            background-attachment: scroll;
        }

        .hero-content h1 {
            font-size: 2.5rem !important;
        }

        .hero-content .lead {
            font-size: 1.1rem !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .news-card {
        transition: all 0.3s ease;
    }
    .news-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }
    .page-header {
        background: var(--gradient-primary);
    }
    .hover-bg-light:hover {
        background-color: #f8f9fa !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\hanwang-test-equipment\resources\views/frontend/news/index.blade.php ENDPATH**/ ?>