<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with('category');

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('brand', 'like', "%{$search}%");
            });
        }

        // 分类筛选
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // 状态筛选
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->where('is_published', true);
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        $products = $query->ordered()->paginate(15);
        $categories = Category::active()->ordered()->get();

        return view('admin.products.index', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $categories = Category::active()->ordered()->get();
        $selectedCategory = $request->get('category');

        return view('admin.products.create', compact('categories', 'selectedCategory'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'image' => 'nullable|string', // 现在是图片路径字符串
            'images' => 'nullable|string', // 现在是JSON字符串
            'model' => 'nullable|string|max:255',
            'brand' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // 处理slug
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // 处理主图（从隐藏字段获取已上传的路径）
        if ($request->filled('image')) {
            $data['image'] = $request->input('image');
        }

        // 处理多图（从隐藏字段获取已上传的路径）
        if ($request->filled('images')) {
            $images = json_decode($request->input('images'), true);
            if (is_array($images)) {
                $data['images'] = $images;
            }
        }

        // 处理规格参数
        if ($request->filled('spec_names') && $request->filled('spec_values')) {
            $specifications = [];
            $names = $request->spec_names;
            $values = $request->spec_values;

            for ($i = 0; $i < count($names); $i++) {
                if (!empty($names[$i]) && !empty($values[$i])) {
                    $specifications[$names[$i]] = $values[$i];
                }
            }
            $data['specifications'] = $specifications;
        }

        Product::create($data);

        return redirect()->route('admin.products.index')
            ->with('success', '产品创建成功！');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load('category');
        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $categories = Category::active()->ordered()->get();
        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug,' . $product->id,
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'image' => 'nullable|string', // 现在是图片路径字符串
            'images' => 'nullable|string', // 现在是JSON字符串
            'specifications' => 'nullable|array',
            'model' => 'nullable|string|max:255',
            'brand' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // 处理slug
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // 处理主图（从隐藏字段获取已上传的路径）
        if ($request->filled('image')) {
            $newImagePath = $request->input('image');
            // 只有当新图片路径与原图片路径不同时才删除旧图片
            if ($product->image && $product->image !== $newImagePath) {
                Storage::disk('public')->delete($product->image);
            }
            $data['image'] = $newImagePath;
        } else {
            // 如果没有提供新图片，保留原有图片
            $data['image'] = $product->image;
        }

        // 处理多图（从隐藏字段获取已上传的路径）
        if ($request->filled('images')) {
            $newImages = json_decode($request->input('images'), true);
            if (is_array($newImages)) {
                // 找出需要删除的旧图片（不在新图片列表中的）
                if ($product->images && is_array($product->images)) {
                    $imagesToDelete = array_diff($product->images, $newImages);
                    foreach ($imagesToDelete as $image) {
                        Storage::disk('public')->delete($image);
                    }
                }
                $data['images'] = $newImages;
            }
        } else {
            // 如果没有提供新图片集，保留原有图片集
            $data['images'] = $product->images;
        }

        // 处理规格参数
        if ($request->filled('spec_names') && $request->filled('spec_values')) {
            $specifications = [];
            $names = $request->spec_names;
            $values = $request->spec_values;

            for ($i = 0; $i < count($names); $i++) {
                if (!empty($names[$i]) && !empty($values[$i])) {
                    $specifications[$names[$i]] = $values[$i];
                }
            }
            $data['specifications'] = $specifications;
        }

        $product->update($data);

        return redirect()->route('admin.products.index')
            ->with('success', '产品更新成功！');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        // 删除图片
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        if ($product->images) {
            foreach ($product->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $product->delete();

        return redirect()->route('admin.products.index')
            ->with('success', '产品删除成功！');
    }
}
