@echo off
echo ========================================
echo 同步uploads图片文件到PHPStudy目录
echo ========================================
echo.

set "SOURCE_DIR=c:\Users\<USER>\Desktop\hanwang-test-equipment"
set "TARGET_DIR=D:\phpstudy_pro\WWW\hanwang-test-equipment"

echo 1. 创建目标目录结构...
mkdir "%TARGET_DIR%\storage\app\public\uploads\editor" 2>nul
mkdir "%TARGET_DIR%\public\storage\uploads\editor" 2>nul

echo.
echo 2. 复制storage目录中的uploads...
xcopy "%SOURCE_DIR%\storage\app\public\uploads" "%TARGET_DIR%\storage\app\public\uploads" /E /Y /I
if %errorlevel% == 0 (
    echo ✅ storage uploads 复制完成
) else (
    echo ❌ storage uploads 复制失败
)

echo.
echo 3. 复制public目录中的storage链接...
xcopy "%SOURCE_DIR%\public\storage\uploads" "%TARGET_DIR%\public\storage\uploads" /E /Y /I
if %errorlevel% == 0 (
    echo ✅ public storage uploads 复制完成
) else (
    echo ❌ public storage uploads 复制失败
)

echo.
echo 4. 验证关键文件是否存在...
if exist "%TARGET_DIR%\public\storage\uploads\editor\1754155667_bYmxyvmD.jpg" (
    echo ✅ 测试文件存在: 1754155667_bYmxyvmD.jpg
) else (
    echo ❌ 测试文件不存在: 1754155667_bYmxyvmD.jpg
)

echo.
echo ========================================
echo 同步完成！
echo ========================================
echo.
echo 现在图片应该可以通过以下URL访问：
echo http://localhost:8000/storage/uploads/editor/1754155667_bYmxyvmD.jpg
echo.
pause
