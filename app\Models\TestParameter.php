<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TestParameter extends Model
{
    use HasFactory;

    protected $fillable = [
        'test_record_id',
        'parameter_name',
        'parameter_value',
        'unit',
        'min_value',
        'max_value',
        'measured_value',
        'is_pass',
    ];

    protected $casts = [
        'min_value' => 'decimal:4',
        'max_value' => 'decimal:4',
        'measured_value' => 'decimal:4',
        'is_pass' => 'boolean',
    ];

    /**
     * 获取测试记录
     */
    public function testRecord(): BelongsTo
    {
        return $this->belongsTo(TestRecord::class);
    }

    /**
     * 获取通过的参数
     */
    public function scopePassed($query)
    {
        return $query->where('is_pass', true);
    }

    /**
     * 获取失败的参数
     */
    public function scopeFailed($query)
    {
        return $query->where('is_pass', false);
    }
}
